[package]
name = "sfquant-wasm"
version = "0.1.0"
edition = "2021"
description = "SFQuant WebAssembly module for high-performance calculations"
license = "MIT"

[lib]
crate-type = ["cdylib"]

[dependencies]
wasm-bindgen = "0.2"
serde = { version = "1.0", features = ["derive"] }
serde-wasm-bindgen = "0.4"
js-sys = "0.3"
console_error_panic_hook = "0.1"

[dependencies.web-sys]
version = "0.3"
features = [
  "console",
  "Performance",
  "Window",
]

[profile.release]
# 优化WASM包大小
opt-level = "s"
lto = true
panic = "abort"

[package.metadata.wasm-pack.profile.release]
wasm-opt = ["-Oz", "--enable-mutable-globals"]
