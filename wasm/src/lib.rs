use wasm_bindgen::prelude::*;
use serde::{Deserialize, Serialize};

// 设置panic hook以便在浏览器中调试
#[wasm_bindgen(start)]
pub fn main() {
    console_error_panic_hook::set_once();
}

// 市场数据结构
#[derive(Debug, Serialize, Deserialize)]
#[wasm_bindgen]
pub struct MarketTick {
    pub symbol: String,
    pub price: f64,
    pub volume: f64,
    pub timestamp: i64,
}

#[wasm_bindgen]
impl MarketTick {
    #[wasm_bindgen(constructor)]
    pub fn new(symbol: String, price: f64, volume: f64, timestamp: i64) -> MarketTick {
        MarketTick {
            symbol,
            price,
            volume,
            timestamp,
        }
    }
}

// 技术指标计算器
#[wasm_bindgen]
pub struct TechnicalIndicators;

#[wasm_bindgen]
impl TechnicalIndicators {
    #[wasm_bindgen(constructor)]
    pub fn new() -> TechnicalIndicators {
        TechnicalIndicators
    }
    
    // 简单移动平均线 (SMA)
    #[wasm_bindgen]
    pub fn sma(&self, prices: &[f64], period: usize) -> Vec<f64> {
        if prices.len() < period {
            return Vec::new();
        }
        
        let mut result = Vec::new();
        
        for i in period..=prices.len() {
            let sum: f64 = prices[i-period..i].iter().sum();
            result.push(sum / period as f64);
        }
        
        result
    }
    
    // 指数移动平均线 (EMA)
    #[wasm_bindgen]
    pub fn ema(&self, prices: &[f64], period: usize) -> Vec<f64> {
        if prices.is_empty() {
            return Vec::new();
        }
        
        let mut result = Vec::new();
        let multiplier = 2.0 / (period as f64 + 1.0);
        
        result.push(prices[0]);
        
        for i in 1..prices.len() {
            let ema = (prices[i] * multiplier) + (result[i-1] * (1.0 - multiplier));
            result.push(ema);
        }
        
        result
    }
    
    // RSI相对强弱指数
    #[wasm_bindgen]
    pub fn rsi(&self, prices: &[f64], period: usize) -> Vec<f64> {
        if prices.len() < period + 1 {
            return Vec::new();
        }
        
        let mut gains = Vec::new();
        let mut losses = Vec::new();
        
        // 计算价格变化
        for i in 1..prices.len() {
            let change = prices[i] - prices[i-1];
            gains.push(if change > 0.0 { change } else { 0.0 });
            losses.push(if change < 0.0 { -change } else { 0.0 });
        }
        
        let avg_gains = self.sma(&gains, period);
        let avg_losses = self.sma(&losses, period);
        
        let mut rsi_values = Vec::new();
        for i in 0..avg_gains.len() {
            if avg_losses[i] == 0.0 {
                rsi_values.push(100.0);
            } else {
                let rs = avg_gains[i] / avg_losses[i];
                let rsi = 100.0 - (100.0 / (1.0 + rs));
                rsi_values.push(rsi);
            }
        }
        
        rsi_values
    }
    
    // MACD指标
    #[wasm_bindgen]
    pub fn macd(&self, prices: &[f64], fast: usize, slow: usize, signal: usize) -> Vec<f64> {
        let ema_fast = self.ema(prices, fast);
        let ema_slow = self.ema(prices, slow);
        
        let mut macd_line = Vec::new();
        let min_len = ema_fast.len().min(ema_slow.len());
        
        for i in 0..min_len {
            macd_line.push(ema_fast[i] - ema_slow[i]);
        }
        
        self.ema(&macd_line, signal)
    }
    
    // 布林带
    #[wasm_bindgen]
    pub fn bollinger_bands(&self, prices: &[f64], period: usize, std_dev: f64) -> Vec<f64> {
        let sma = self.sma(prices, period);
        let mut bands = Vec::new();
        
        for i in 0..sma.len() {
            let start_idx = i;
            let end_idx = i + period;
            
            if end_idx <= prices.len() {
                let slice = &prices[start_idx..end_idx];
                let mean = sma[i];
                
                // 计算标准差
                let variance: f64 = slice.iter()
                    .map(|&x| (x - mean).powi(2))
                    .sum::<f64>() / period as f64;
                let std = variance.sqrt();
                
                // 上轨、中轨、下轨
                bands.push(mean + (std_dev * std)); // 上轨
                bands.push(mean);                   // 中轨
                bands.push(mean - (std_dev * std)); // 下轨
            }
        }
        
        bands
    }
}

// 高频数据处理
#[wasm_bindgen]
pub fn process_tick_data(ticks: &[f64], window_size: usize) -> Vec<f64> {
    if ticks.len() < window_size {
        return Vec::new();
    }
    
    let mut processed = Vec::new();
    
    for window in ticks.windows(window_size) {
        let sum: f64 = window.iter().sum();
        let avg = sum / window_size as f64;
        processed.push(avg);
    }
    
    processed
}

// 套利机会检测
#[wasm_bindgen]
pub fn detect_arbitrage(
    exchange1_prices: &[f64],
    exchange2_prices: &[f64],
    threshold: f64
) -> Vec<bool> {
    let mut opportunities = Vec::new();
    
    let min_len = exchange1_prices.len().min(exchange2_prices.len());
    
    for i in 0..min_len {
        let price_diff = (exchange1_prices[i] - exchange2_prices[i]).abs();
        let min_price = exchange1_prices[i].min(exchange2_prices[i]);
        
        if min_price > 0.0 {
            let percentage_diff = price_diff / min_price;
            opportunities.push(percentage_diff > threshold);
        } else {
            opportunities.push(false);
        }
    }
    
    opportunities
}

// 风险计算
#[wasm_bindgen]
pub fn calculate_var(returns: &[f64], confidence_level: f64) -> f64 {
    if returns.is_empty() {
        return 0.0;
    }
    
    let mut sorted_returns = returns.to_vec();
    sorted_returns.sort_by(|a, b| a.partial_cmp(b).unwrap());
    
    let index = ((1.0 - confidence_level) * sorted_returns.len() as f64) as usize;
    
    if index < sorted_returns.len() {
        sorted_returns[index]
    } else {
        sorted_returns[sorted_returns.len() - 1]
    }
}

// 夏普比率计算
#[wasm_bindgen]
pub fn calculate_sharpe_ratio(returns: &[f64], risk_free_rate: f64) -> f64 {
    if returns.is_empty() {
        return 0.0;
    }
    
    let mean_return: f64 = returns.iter().sum::<f64>() / returns.len() as f64;
    let excess_return = mean_return - risk_free_rate;
    
    if returns.len() < 2 {
        return 0.0;
    }
    
    let variance: f64 = returns.iter()
        .map(|&x| (x - mean_return).powi(2))
        .sum::<f64>() / (returns.len() - 1) as f64;
    
    let std_dev = variance.sqrt();
    
    if std_dev == 0.0 {
        0.0
    } else {
        excess_return / std_dev
    }
}

// 最大回撤计算
#[wasm_bindgen]
pub fn calculate_max_drawdown(prices: &[f64]) -> f64 {
    if prices.len() < 2 {
        return 0.0;
    }
    
    let mut max_drawdown = 0.0;
    let mut peak = prices[0];
    
    for &price in prices.iter().skip(1) {
        if price > peak {
            peak = price;
        }
        
        let drawdown = (peak - price) / peak;
        if drawdown > max_drawdown {
            max_drawdown = drawdown;
        }
    }
    
    max_drawdown
}
