import { BaseStrategy } from './BaseStrategy'
import { ExchangeService } from '../services/ExchangeService'
import { MarketDataService } from '../services/MarketDataService'
import { RiskManager } from '../services/RiskManager'

export interface ArbitrageConfig {
  symbol: string
  exchanges: string[] // 至少2个交易所
  minProfitThreshold: number // 最小利润阈值 (%)
  maxPositionSize: number // 最大仓位大小
  slippageTolerance: number // 滑点容忍度 (%)
  maxSpread: number // 最大价差 (%)
  cooldownPeriod: number // 冷却期 (秒)
}

export interface ArbitrageOpportunity {
  buyExchange: string
  sellExchange: string
  buyPrice: number
  sellPrice: number
  profit: number
  profitPercent: number
  volume: number
  timestamp: number
}

export class ArbitrageStrategy extends BaseStrategy {
  private config: ArbitrageConfig
  private exchangeService: ExchangeService
  private marketDataService: MarketDataService
  private riskManager: RiskManager
  private lastExecutionTime: number = 0
  private opportunities: ArbitrageOpportunity[] = []

  constructor(
    config: ArbitrageConfig,
    exchangeService: ExchangeService,
    marketDataService: MarketDataService,
    riskManager: RiskManager
  ) {
    super('arbitrage', config.symbol)
    this.config = config
    this.exchangeService = exchangeService
    this.marketDataService = marketDataService
    this.riskManager = riskManager
  }

  async initialize(): Promise<void> {
    console.log(`🔄 Initializing arbitrage strategy for ${this.config.symbol}`)
    
    // 验证交易所可用性
    for (const exchange of this.config.exchanges) {
      const isHealthy = this.exchangeService.getExchangeStatus(exchange)
      if (!isHealthy) {
        throw new Error(`Exchange ${exchange} is not healthy`)
      }
    }

    // 验证交易对在所有交易所都可用
    await this.validateTradingPairs()
    
    console.log('✅ Arbitrage strategy initialized')
  }

  async execute(): Promise<void> {
    try {
      // 检查冷却期
      if (this.isInCooldown()) {
        return
      }

      // 获取所有交易所的市场数据
      const marketData = await this.fetchMarketData()
      
      // 寻找套利机会
      const opportunities = this.findArbitrageOpportunities(marketData)
      
      if (opportunities.length > 0) {
        // 选择最佳机会
        const bestOpportunity = this.selectBestOpportunity(opportunities)
        
        // 风险检查
        if (await this.riskManager.checkArbitrageRisk(bestOpportunity)) {
          // 执行套利交易
          await this.executeArbitrage(bestOpportunity)
        }
      }
    } catch (error) {
      console.error('❌ Error executing arbitrage strategy:', error)
      this.handleError(error)
    }
  }

  private async validateTradingPairs(): Promise<void> {
    for (const exchangeId of this.config.exchanges) {
      const exchange = this.exchangeService.getExchange(exchangeId)
      if (!exchange) {
        throw new Error(`Exchange ${exchangeId} not available`)
      }

      try {
        const markets = await exchange.loadMarkets()
        if (!markets[this.config.symbol]) {
          throw new Error(`Symbol ${this.config.symbol} not available on ${exchangeId}`)
        }
      } catch (error) {
        throw new Error(`Failed to validate trading pair on ${exchangeId}: ${error}`)
      }
    }
  }

  private async fetchMarketData(): Promise<Record<string, any>> {
    const marketData: Record<string, any> = {}
    
    for (const exchangeId of this.config.exchanges) {
      try {
        const ticker = await this.marketDataService.getTicker(exchangeId, this.config.symbol)
        const orderbook = await this.marketDataService.getOrderBook(exchangeId, this.config.symbol)
        
        marketData[exchangeId] = {
          ticker,
          orderbook,
          timestamp: Date.now()
        }
      } catch (error) {
        console.error(`Failed to fetch market data from ${exchangeId}:`, error)
      }
    }
    
    return marketData
  }

  private findArbitrageOpportunities(marketData: Record<string, any>): ArbitrageOpportunity[] {
    const opportunities: ArbitrageOpportunity[] = []
    const exchanges = Object.keys(marketData)
    
    // 比较所有交易所对
    for (let i = 0; i < exchanges.length; i++) {
      for (let j = i + 1; j < exchanges.length; j++) {
        const exchange1 = exchanges[i]
        const exchange2 = exchanges[j]
        
        const data1 = marketData[exchange1]
        const data2 = marketData[exchange2]
        
        if (!data1 || !data2) continue
        
        // 检查两个方向的套利机会
        const opp1 = this.calculateArbitrageOpportunity(exchange1, exchange2, data1, data2)
        const opp2 = this.calculateArbitrageOpportunity(exchange2, exchange1, data2, data1)
        
        if (opp1) opportunities.push(opp1)
        if (opp2) opportunities.push(opp2)
      }
    }
    
    return opportunities.filter(opp => 
      opp.profitPercent >= this.config.minProfitThreshold &&
      opp.profitPercent <= this.config.maxSpread
    )
  }

  private calculateArbitrageOpportunity(
    buyExchange: string,
    sellExchange: string,
    buyData: any,
    sellData: any
  ): ArbitrageOpportunity | null {
    try {
      const buyPrice = buyData.ticker.ask // 买入价格（卖方报价）
      const sellPrice = sellData.ticker.bid // 卖出价格（买方报价）
      
      if (!buyPrice || !sellPrice || buyPrice >= sellPrice) {
        return null
      }
      
      const profit = sellPrice - buyPrice
      const profitPercent = (profit / buyPrice) * 100
      
      // 计算可交易量（取两个交易所的最小值）
      const buyVolume = buyData.orderbook.asks[0]?.[1] || 0
      const sellVolume = sellData.orderbook.bids[0]?.[1] || 0
      const volume = Math.min(buyVolume, sellVolume, this.config.maxPositionSize)
      
      return {
        buyExchange,
        sellExchange,
        buyPrice,
        sellPrice,
        profit,
        profitPercent,
        volume,
        timestamp: Date.now()
      }
    } catch (error) {
      console.error('Error calculating arbitrage opportunity:', error)
      return null
    }
  }

  private selectBestOpportunity(opportunities: ArbitrageOpportunity[]): ArbitrageOpportunity {
    // 按利润率排序，选择最佳机会
    return opportunities.sort((a, b) => b.profitPercent - a.profitPercent)[0]
  }

  private async executeArbitrage(opportunity: ArbitrageOpportunity): Promise<void> {
    console.log(`🚀 Executing arbitrage: Buy ${opportunity.volume} ${this.config.symbol} on ${opportunity.buyExchange} at ${opportunity.buyPrice}, Sell on ${opportunity.sellExchange} at ${opportunity.sellPrice}`)
    
    try {
      // 同时下买单和卖单
      const [buyOrder, sellOrder] = await Promise.all([
        this.placeBuyOrder(opportunity),
        this.placeSellOrder(opportunity)
      ])
      
      // 记录交易
      this.recordTrade(opportunity, buyOrder, sellOrder)
      
      // 更新最后执行时间
      this.lastExecutionTime = Date.now()
      
      console.log('✅ Arbitrage executed successfully')
    } catch (error) {
      console.error('❌ Failed to execute arbitrage:', error)
      throw error
    }
  }

  private async placeBuyOrder(opportunity: ArbitrageOpportunity): Promise<any> {
    const exchange = this.exchangeService.getExchange(opportunity.buyExchange)
    if (!exchange) {
      throw new Error(`Exchange ${opportunity.buyExchange} not available`)
    }
    
    return await exchange.createMarketBuyOrder(
      this.config.symbol,
      opportunity.volume
    )
  }

  private async placeSellOrder(opportunity: ArbitrageOpportunity): Promise<any> {
    const exchange = this.exchangeService.getExchange(opportunity.sellExchange)
    if (!exchange) {
      throw new Error(`Exchange ${opportunity.sellExchange} not available`)
    }
    
    return await exchange.createMarketSellOrder(
      this.config.symbol,
      opportunity.volume
    )
  }

  private recordTrade(opportunity: ArbitrageOpportunity, buyOrder: any, sellOrder: any): void {
    // 记录交易到数据库
    const trade = {
      strategy: 'arbitrage',
      symbol: this.config.symbol,
      buyExchange: opportunity.buyExchange,
      sellExchange: opportunity.sellExchange,
      buyPrice: opportunity.buyPrice,
      sellPrice: opportunity.sellPrice,
      volume: opportunity.volume,
      profit: opportunity.profit,
      profitPercent: opportunity.profitPercent,
      buyOrderId: buyOrder.id,
      sellOrderId: sellOrder.id,
      timestamp: Date.now()
    }
    
    // TODO: 保存到数据库
    console.log('📊 Trade recorded:', trade)
  }

  private isInCooldown(): boolean {
    return Date.now() - this.lastExecutionTime < this.config.cooldownPeriod * 1000
  }

  private handleError(error: any): void {
    // 错误处理逻辑
    console.error('Strategy error:', error)
    // TODO: 发送告警通知
  }

  // 获取策略状态
  getStatus(): any {
    return {
      type: 'arbitrage',
      symbol: this.config.symbol,
      exchanges: this.config.exchanges,
      lastExecution: this.lastExecutionTime,
      opportunities: this.opportunities.length,
      config: this.config
    }
  }
}
