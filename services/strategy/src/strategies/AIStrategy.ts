import { BaseStrategy } from './BaseStrategy'
import { ExchangeService } from '../services/ExchangeService'
import { MarketDataService } from '../services/MarketDataService'
import { TechnicalIndicators } from '../services/TechnicalIndicators'

export interface AIConfig {
  symbol: string
  exchange: string
  timeframe: string // '1m', '5m', '15m', '1h', '4h', '1d'
  lookbackPeriod: number // 历史数据回看期数
  predictionHorizon: number // 预测时间范围（分钟）
  confidenceThreshold: number // 置信度阈值 (0-1)
  positionSize: number // 仓位大小
  stopLoss: number // 止损百分比
  takeProfit: number // 止盈百分比
  features: string[] // 使用的特征列表
  modelType: 'lstm' | 'transformer' | 'ensemble' // 模型类型
}

export interface MarketFeatures {
  price: number[]
  volume: number[]
  rsi: number[]
  macd: number[]
  bollinger: any[]
  sma: number[]
  ema: number[]
  volatility: number[]
  momentum: number[]
  timestamp: number[]
}

export interface Prediction {
  direction: 'buy' | 'sell' | 'hold'
  confidence: number
  targetPrice: number
  timeHorizon: number
  features: MarketFeatures
  timestamp: number
}

export class AIStrategy extends BaseStrategy {
  private config: AIConfig
  private exchangeService: ExchangeService
  private marketDataService: MarketDataService
  private technicalIndicators: TechnicalIndicators
  private model: any // AI模型实例
  private historicalData: any[] = []
  private currentPosition: any = null
  private lastPrediction: Prediction | null = null

  constructor(
    config: AIConfig,
    exchangeService: ExchangeService,
    marketDataService: MarketDataService,
    technicalIndicators: TechnicalIndicators
  ) {
    super('ai', config.symbol)
    this.config = config
    this.exchangeService = exchangeService
    this.marketDataService = marketDataService
    this.technicalIndicators = technicalIndicators
  }

  async initialize(): Promise<void> {
    console.log(`🤖 Initializing AI strategy for ${this.config.symbol}`)
    
    // 验证交易所可用性
    const exchange = this.exchangeService.getExchange(this.config.exchange)
    if (!exchange) {
      throw new Error(`Exchange ${this.config.exchange} not available`)
    }

    // 加载历史数据
    await this.loadHistoricalData()
    
    // 初始化AI模型
    await this.initializeModel()
    
    console.log('✅ AI strategy initialized')
  }

  async execute(): Promise<void> {
    try {
      // 获取最新市场数据
      const latestData = await this.fetchLatestData()
      
      // 更新历史数据
      this.updateHistoricalData(latestData)
      
      // 提取特征
      const features = await this.extractFeatures()
      
      // 生成预测
      const prediction = await this.generatePrediction(features)
      
      // 执行交易决策
      if (prediction.confidence >= this.config.confidenceThreshold) {
        await this.executeTradingDecision(prediction)
      }
      
      // 管理现有仓位
      await this.managePosition()
      
    } catch (error) {
      console.error('❌ Error executing AI strategy:', error)
      this.handleError(error)
    }
  }

  private async loadHistoricalData(): Promise<void> {
    try {
      const exchange = this.exchangeService.getExchange(this.config.exchange)
      if (!exchange) {
        throw new Error('Exchange not available')
      }

      // 获取历史K线数据
      const ohlcv = await exchange.fetchOHLCV(
        this.config.symbol,
        this.config.timeframe,
        undefined,
        this.config.lookbackPeriod
      )

      this.historicalData = ohlcv.map(candle => ({
        timestamp: candle[0],
        open: candle[1],
        high: candle[2],
        low: candle[3],
        close: candle[4],
        volume: candle[5]
      }))

      console.log(`📊 Loaded ${this.historicalData.length} historical data points`)
    } catch (error) {
      throw new Error(`Failed to load historical data: ${error}`)
    }
  }

  private async initializeModel(): Promise<void> {
    // 这里应该加载预训练的AI模型
    // 由于这是演示代码，我们使用简化的模型逻辑
    
    switch (this.config.modelType) {
      case 'lstm':
        this.model = new LSTMModel(this.config)
        break
      case 'transformer':
        this.model = new TransformerModel(this.config)
        break
      case 'ensemble':
        this.model = new EnsembleModel(this.config)
        break
      default:
        throw new Error(`Unsupported model type: ${this.config.modelType}`)
    }

    await this.model.initialize()
    console.log(`🧠 AI model (${this.config.modelType}) initialized`)
  }

  private async fetchLatestData(): Promise<any> {
    const exchange = this.exchangeService.getExchange(this.config.exchange)
    if (!exchange) {
      throw new Error('Exchange not available')
    }

    const ticker = await exchange.fetchTicker(this.config.symbol)
    const orderbook = await exchange.fetchOrderBook(this.config.symbol)
    
    return {
      price: ticker.last,
      bid: ticker.bid,
      ask: ticker.ask,
      volume: ticker.baseVolume,
      timestamp: ticker.timestamp,
      orderbook
    }
  }

  private updateHistoricalData(latestData: any): void {
    // 添加最新数据点
    this.historicalData.push({
      timestamp: latestData.timestamp,
      open: latestData.price,
      high: latestData.price,
      low: latestData.price,
      close: latestData.price,
      volume: latestData.volume
    })

    // 保持固定长度
    if (this.historicalData.length > this.config.lookbackPeriod) {
      this.historicalData.shift()
    }
  }

  private async extractFeatures(): Promise<MarketFeatures> {
    const closes = this.historicalData.map(d => d.close)
    const volumes = this.historicalData.map(d => d.volume)
    const highs = this.historicalData.map(d => d.high)
    const lows = this.historicalData.map(d => d.low)
    const timestamps = this.historicalData.map(d => d.timestamp)

    const features: MarketFeatures = {
      price: closes,
      volume: volumes,
      rsi: this.technicalIndicators.calculateRSI(closes, 14),
      macd: this.technicalIndicators.calculateMACD(closes),
      bollinger: this.technicalIndicators.calculateBollingerBands(closes, 20, 2),
      sma: this.technicalIndicators.calculateSMA(closes, 20),
      ema: this.technicalIndicators.calculateEMA(closes, 20),
      volatility: this.calculateVolatility(closes),
      momentum: this.calculateMomentum(closes),
      timestamp: timestamps
    }

    return features
  }

  private calculateVolatility(prices: number[]): number[] {
    const volatility: number[] = []
    const period = 20

    for (let i = period; i < prices.length; i++) {
      const slice = prices.slice(i - period, i)
      const returns = slice.slice(1).map((price, idx) => 
        Math.log(price / slice[idx])
      )
      const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length
      const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length
      volatility.push(Math.sqrt(variance))
    }

    return volatility
  }

  private calculateMomentum(prices: number[]): number[] {
    const momentum: number[] = []
    const period = 10

    for (let i = period; i < prices.length; i++) {
      const currentPrice = prices[i]
      const pastPrice = prices[i - period]
      momentum.push((currentPrice - pastPrice) / pastPrice * 100)
    }

    return momentum
  }

  private async generatePrediction(features: MarketFeatures): Promise<Prediction> {
    // 使用AI模型生成预测
    const modelOutput = await this.model.predict(features)
    
    const prediction: Prediction = {
      direction: modelOutput.direction,
      confidence: modelOutput.confidence,
      targetPrice: modelOutput.targetPrice,
      timeHorizon: this.config.predictionHorizon,
      features,
      timestamp: Date.now()
    }

    this.lastPrediction = prediction
    return prediction
  }

  private async executeTradingDecision(prediction: Prediction): Promise<void> {
    const currentPrice = this.historicalData[this.historicalData.length - 1].close

    if (prediction.direction === 'buy' && !this.currentPosition) {
      await this.openLongPosition(currentPrice, prediction)
    } else if (prediction.direction === 'sell' && !this.currentPosition) {
      await this.openShortPosition(currentPrice, prediction)
    }
  }

  private async openLongPosition(price: number, prediction: Prediction): Promise<void> {
    try {
      const exchange = this.exchangeService.getExchange(this.config.exchange)
      if (!exchange) {
        throw new Error('Exchange not available')
      }

      const order = await exchange.createMarketBuyOrder(
        this.config.symbol,
        this.config.positionSize
      )

      this.currentPosition = {
        type: 'long',
        entryPrice: price,
        size: this.config.positionSize,
        stopLoss: price * (1 - this.config.stopLoss / 100),
        takeProfit: price * (1 + this.config.takeProfit / 100),
        orderId: order.id,
        timestamp: Date.now(),
        prediction
      }

      this.addTrade({
        type: 'open_long',
        price,
        size: this.config.positionSize,
        orderId: order.id,
        prediction: prediction.confidence
      })

      console.log(`📈 Opened long position at ${price}`)
    } catch (error) {
      console.error('Failed to open long position:', error)
    }
  }

  private async openShortPosition(price: number, prediction: Prediction): Promise<void> {
    try {
      const exchange = this.exchangeService.getExchange(this.config.exchange)
      if (!exchange) {
        throw new Error('Exchange not available')
      }

      const order = await exchange.createMarketSellOrder(
        this.config.symbol,
        this.config.positionSize
      )

      this.currentPosition = {
        type: 'short',
        entryPrice: price,
        size: this.config.positionSize,
        stopLoss: price * (1 + this.config.stopLoss / 100),
        takeProfit: price * (1 - this.config.takeProfit / 100),
        orderId: order.id,
        timestamp: Date.now(),
        prediction
      }

      this.addTrade({
        type: 'open_short',
        price,
        size: this.config.positionSize,
        orderId: order.id,
        prediction: prediction.confidence
      })

      console.log(`📉 Opened short position at ${price}`)
    } catch (error) {
      console.error('Failed to open short position:', error)
    }
  }

  private async managePosition(): Promise<void> {
    if (!this.currentPosition) {
      return
    }

    const currentPrice = this.historicalData[this.historicalData.length - 1].close
    
    // 检查止损和止盈
    if (this.shouldClosePosition(currentPrice)) {
      await this.closePosition(currentPrice)
    }
  }

  private shouldClosePosition(currentPrice: number): boolean {
    if (!this.currentPosition) {
      return false
    }

    const { type, stopLoss, takeProfit } = this.currentPosition

    if (type === 'long') {
      return currentPrice <= stopLoss || currentPrice >= takeProfit
    } else {
      return currentPrice >= stopLoss || currentPrice <= takeProfit
    }
  }

  private async closePosition(price: number): Promise<void> {
    if (!this.currentPosition) {
      return
    }

    try {
      const exchange = this.exchangeService.getExchange(this.config.exchange)
      if (!exchange) {
        throw new Error('Exchange not available')
      }

      const { type, entryPrice, size } = this.currentPosition
      
      let order
      if (type === 'long') {
        order = await exchange.createMarketSellOrder(this.config.symbol, size)
      } else {
        order = await exchange.createMarketBuyOrder(this.config.symbol, size)
      }

      const profit = type === 'long' 
        ? (price - entryPrice) * size
        : (entryPrice - price) * size

      this.addTrade({
        type: `close_${type}`,
        price,
        size,
        orderId: order.id,
        profit,
        profitPercent: (profit / (entryPrice * size)) * 100
      })

      console.log(`💰 Closed ${type} position at ${price}, Profit: ${profit}`)
      this.currentPosition = null
    } catch (error) {
      console.error('Failed to close position:', error)
    }
  }

  getStatus(): any {
    return {
      ...super.getStatus(),
      model: this.config.modelType,
      exchange: this.config.exchange,
      timeframe: this.config.timeframe,
      currentPosition: this.currentPosition,
      lastPrediction: this.lastPrediction,
      dataPoints: this.historicalData.length
    }
  }
}

// 简化的AI模型类（实际应用中应该使用真实的ML框架）
class LSTMModel {
  constructor(private config: AIConfig) {}
  
  async initialize(): Promise<void> {
    // 初始化LSTM模型
  }
  
  async predict(features: MarketFeatures): Promise<any> {
    // 简化的预测逻辑
    const lastPrice = features.price[features.price.length - 1]
    const rsi = features.rsi[features.rsi.length - 1]
    
    let direction: 'buy' | 'sell' | 'hold' = 'hold'
    let confidence = 0.5
    
    if (rsi < 30) {
      direction = 'buy'
      confidence = 0.7
    } else if (rsi > 70) {
      direction = 'sell'
      confidence = 0.7
    }
    
    return {
      direction,
      confidence,
      targetPrice: lastPrice * (direction === 'buy' ? 1.02 : 0.98)
    }
  }
}

class TransformerModel extends LSTMModel {}
class EnsembleModel extends LSTMModel {}
