export abstract class BaseStrategy {
  protected type: string
  protected symbol: string
  protected isRunning: boolean = false
  protected startTime: number = 0
  protected trades: any[] = []
  protected performance: any = {}

  constructor(type: string, symbol: string) {
    this.type = type
    this.symbol = symbol
  }

  // 抽象方法，子类必须实现
  abstract initialize(): Promise<void>
  abstract execute(): Promise<void>

  // 启动策略
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log(`⚠️ Strategy ${this.type} is already running`)
      return
    }

    try {
      await this.initialize()
      this.isRunning = true
      this.startTime = Date.now()
      console.log(`🚀 Strategy ${this.type} started for ${this.symbol}`)
    } catch (error) {
      console.error(`❌ Failed to start strategy ${this.type}:`, error)
      throw error
    }
  }

  // 停止策略
  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log(`⚠️ Strategy ${this.type} is not running`)
      return
    }

    this.isRunning = false
    console.log(`🛑 Strategy ${this.type} stopped`)
  }

  // 暂停策略
  pause(): void {
    this.isRunning = false
    console.log(`⏸️ Strategy ${this.type} paused`)
  }

  // 恢复策略
  resume(): void {
    this.isRunning = true
    console.log(`▶️ Strategy ${this.type} resumed`)
  }

  // 获取策略状态
  getStatus(): any {
    return {
      type: this.type,
      symbol: this.symbol,
      isRunning: this.isRunning,
      startTime: this.startTime,
      uptime: this.isRunning ? Date.now() - this.startTime : 0,
      tradesCount: this.trades.length,
      performance: this.performance
    }
  }

  // 获取策略类型
  getType(): string {
    return this.type
  }

  // 获取交易对
  getSymbol(): string {
    return this.symbol
  }

  // 是否正在运行
  getIsRunning(): boolean {
    return this.isRunning
  }

  // 获取交易记录
  getTrades(): any[] {
    return this.trades
  }

  // 获取性能指标
  getPerformance(): any {
    return this.performance
  }

  // 添加交易记录
  protected addTrade(trade: any): void {
    this.trades.push({
      ...trade,
      timestamp: Date.now(),
      strategy: this.type,
      symbol: this.symbol
    })
    
    // 更新性能指标
    this.updatePerformance()
  }

  // 更新性能指标
  protected updatePerformance(): void {
    if (this.trades.length === 0) {
      this.performance = {
        totalTrades: 0,
        totalProfit: 0,
        totalProfitPercent: 0,
        winRate: 0,
        averageProfit: 0,
        averageLoss: 0,
        maxProfit: 0,
        maxLoss: 0,
        sharpeRatio: 0,
        maxDrawdown: 0
      }
      return
    }

    let totalProfit = 0
    let profitableTrades = 0
    let totalProfits = 0
    let totalLosses = 0
    let maxProfit = 0
    let maxLoss = 0
    const profits: number[] = []

    for (const trade of this.trades) {
      const profit = trade.profit || 0
      totalProfit += profit
      profits.push(profit)

      if (profit > 0) {
        profitableTrades++
        totalProfits += profit
        maxProfit = Math.max(maxProfit, profit)
      } else {
        totalLosses += Math.abs(profit)
        maxLoss = Math.min(maxLoss, profit)
      }
    }

    const winRate = (profitableTrades / this.trades.length) * 100
    const averageProfit = profitableTrades > 0 ? totalProfits / profitableTrades : 0
    const averageLoss = (this.trades.length - profitableTrades) > 0 ? 
      totalLosses / (this.trades.length - profitableTrades) : 0

    // 计算夏普比率
    const averageReturn = profits.reduce((sum, p) => sum + p, 0) / profits.length
    const variance = profits.reduce((sum, p) => sum + Math.pow(p - averageReturn, 2), 0) / profits.length
    const standardDeviation = Math.sqrt(variance)
    const sharpeRatio = standardDeviation > 0 ? averageReturn / standardDeviation : 0

    // 计算最大回撤
    let maxDrawdown = 0
    let peak = 0
    let runningProfit = 0

    for (const trade of this.trades) {
      runningProfit += trade.profit || 0
      if (runningProfit > peak) {
        peak = runningProfit
      }
      const drawdown = (peak - runningProfit) / peak * 100
      maxDrawdown = Math.max(maxDrawdown, drawdown)
    }

    this.performance = {
      totalTrades: this.trades.length,
      totalProfit,
      totalProfitPercent: 0, // 需要初始资金来计算
      winRate,
      averageProfit,
      averageLoss,
      maxProfit,
      maxLoss,
      sharpeRatio,
      maxDrawdown,
      profitableTrades,
      losingTrades: this.trades.length - profitableTrades,
      lastUpdated: Date.now()
    }
  }

  // 重置策略
  reset(): void {
    this.isRunning = false
    this.startTime = 0
    this.trades = []
    this.performance = {}
    console.log(`🔄 Strategy ${this.type} reset`)
  }

  // 验证配置
  protected validateConfig(config: any): void {
    if (!config) {
      throw new Error('Strategy config is required')
    }
    
    if (!config.symbol) {
      throw new Error('Symbol is required in strategy config')
    }
    
    // 子类可以重写此方法来添加特定的验证逻辑
  }

  // 日志记录
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [${this.type.toUpperCase()}] [${level.toUpperCase()}] ${message}`
    
    switch (level) {
      case 'info':
        console.log(logMessage, data || '')
        break
      case 'warn':
        console.warn(logMessage, data || '')
        break
      case 'error':
        console.error(logMessage, data || '')
        break
    }
  }

  // 错误处理
  protected handleError(error: any): void {
    this.log('error', 'Strategy execution error', error)
    
    // 可以在这里添加错误恢复逻辑
    // 例如：重试、暂停策略、发送告警等
  }

  // 获取策略摘要
  getSummary(): any {
    return {
      type: this.type,
      symbol: this.symbol,
      status: this.isRunning ? 'running' : 'stopped',
      uptime: this.isRunning ? Date.now() - this.startTime : 0,
      trades: this.trades.length,
      totalProfit: this.performance.totalProfit || 0,
      winRate: this.performance.winRate || 0,
      lastTrade: this.trades.length > 0 ? this.trades[this.trades.length - 1].timestamp : null
    }
  }
}
