# SFQuant PWA 实施指南

## 🚀 PWA实施路线图

### 第一阶段：基础PWA功能 (2-3周)

#### 1.1 项目初始化
```bash
# 创建项目
npm create vite@latest sfquant-pwa -- --template react-ts
cd sfquant-pwa

# 安装PWA相关依赖
npm install vite-plugin-pwa workbox-window
npm install @types/serviceworker

# 安装UI和状态管理
npm install antd @ant-design/icons
npm install zustand @tanstack/react-query
npm install react-router-dom

# 安装开发工具
npm install -D @types/node
```

#### 1.2 Vite PWA配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { VitePWA } from 'vite-plugin-pwa';

export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'masked-icon.svg'],
      manifest: {
        name: 'SFQuant - 智能量化交易平台',
        short_name: 'SFQuant',
        description: '专业的加密货币量化策略管理平台',
        theme_color: '#1890ff',
        background_color: '#ffffff',
        display: 'standalone',
        orientation: 'landscape-primary',
        scope: '/',
        start_url: '/',
        icons: [
          {
            src: 'pwa-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'pwa-512x512.png',
            sizes: '512x512',
            type: 'image/png'
          },
          {
            src: 'pwa-512x512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'any maskable'
          }
        ],
        shortcuts: [
          {
            name: '交易面板',
            short_name: '交易',
            description: '快速访问交易面板',
            url: '/trading',
            icons: [{ src: 'trading-96x96.png', sizes: '96x96' }]
          },
          {
            name: '策略管理',
            short_name: '策略',
            description: '管理量化策略',
            url: '/strategies',
            icons: [{ src: 'strategy-96x96.png', sizes: '96x96' }]
          }
        ]
      },
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,wasm}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/api\.sfquant\.com\/.*/i,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 // 24小时
              },
              cacheKeyWillBeUsed: async ({ request }) => {
                return `${request.url}?v=${Date.now()}`;
              }
            }
          },
          {
            urlPattern: /^https:\/\/.*\.(?:png|jpg|jpeg|svg|gif)$/,
            handler: 'CacheFirst',
            options: {
              cacheName: 'images-cache',
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 60 * 60 * 24 * 30 // 30天
              }
            }
          }
        ]
      }
    })
  ],
  build: {
    target: 'esnext',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          charts: ['@ant-design/charts'],
          utils: ['lodash-es', 'dayjs']
        }
      }
    }
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'antd']
  }
});
```

#### 1.3 基础应用结构
```typescript
// src/App.tsx
import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, App as AntApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { PWAInstallPrompt } from './components/PWAInstallPrompt';
import { OfflineIndicator } from './components/OfflineIndicator';
import { Layout } from './components/Layout';
import { Dashboard } from './pages/Dashboard';
import { Trading } from './pages/Trading';
import { Strategies } from './pages/Strategies';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
    },
  },
});

function App() {
  useEffect(() => {
    // 注册Service Worker更新监听
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        window.location.reload();
      });
    }
  }, []);

  return (
    <ConfigProvider locale={zhCN}>
      <QueryClientProvider client={queryClient}>
        <AntApp>
          <Router>
            <Layout>
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/trading" element={<Trading />} />
                <Route path="/strategies" element={<Strategies />} />
              </Routes>
            </Layout>
            <PWAInstallPrompt />
            <OfflineIndicator />
          </Router>
        </AntApp>
      </QueryClientProvider>
    </ConfigProvider>
  );
}

export default App;
```

#### 1.4 PWA安装提示组件
```typescript
// src/components/PWAInstallPrompt.tsx
import React, { useState, useEffect } from 'react';
import { Button, Modal, Space } from 'antd';
import { DownloadOutlined, MobileOutlined, DesktopOutlined } from '@ant-design/icons';

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export const PWAInstallPrompt: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallModal, setShowInstallModal] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // 检查是否已安装
    const checkInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isInWebAppiOS = (window.navigator as any).standalone === true;
      setIsInstalled(isStandalone || isInWebAppiOS);
    };

    checkInstalled();

    // 监听安装提示事件
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      
      // 延迟显示安装提示
      setTimeout(() => {
        setShowInstallModal(true);
      }, 30000); // 30秒后显示
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', () => {
      setIsInstalled(true);
      setShowInstallModal(false);
    });

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstall = async () => {
    if (!deferredPrompt) return;

    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;
    
    if (outcome === 'accepted') {
      setDeferredPrompt(null);
      setShowInstallModal(false);
    }
  };

  if (isInstalled || !deferredPrompt) {
    return null;
  }

  return (
    <Modal
      title="安装SFQuant应用"
      open={showInstallModal}
      onCancel={() => setShowInstallModal(false)}
      footer={[
        <Button key="cancel" onClick={() => setShowInstallModal(false)}>
          稍后再说
        </Button>,
        <Button key="install" type="primary" icon={<DownloadOutlined />} onClick={handleInstall}>
          立即安装
        </Button>
      ]}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <p>将SFQuant安装到您的设备上，享受更好的使用体验：</p>
        <ul>
          <li><DesktopOutlined /> 桌面快捷方式，快速启动</li>
          <li><MobileOutlined /> 离线使用，无需网络连接</li>
          <li>🚀 更快的加载速度</li>
          <li>🔔 实时推送通知</li>
        </ul>
      </Space>
    </Modal>
  );
};
```

#### 1.5 离线状态指示器
```typescript
// src/components/OfflineIndicator.tsx
import React, { useState, useEffect } from 'react';
import { Alert, Space } from 'antd';
import { WifiOutlined, DisconnectOutlined } from '@ant-design/icons';

export const OfflineIndicator: React.FC = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showOfflineAlert, setShowOfflineAlert] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineAlert(false);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineAlert(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!showOfflineAlert) {
    return null;
  }

  return (
    <div style={{ 
      position: 'fixed', 
      top: 0, 
      left: 0, 
      right: 0, 
      zIndex: 9999 
    }}>
      <Alert
        message={
          <Space>
            <DisconnectOutlined />
            您当前处于离线状态，部分功能可能受限
          </Space>
        }
        type="warning"
        showIcon={false}
        closable
        onClose={() => setShowOfflineAlert(false)}
      />
    </div>
  );
};
```

### 第二阶段：高性能数据处理 (3-4周)

#### 2.1 Web Assembly集成
```bash
# 安装Rust和wasm-pack
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
cargo install wasm-pack

# 创建WASM模块
mkdir wasm
cd wasm
cargo init --lib
```

```toml
# wasm/Cargo.toml
[package]
name = "sfquant-wasm"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
wasm-bindgen = "0.2"
serde = { version = "1.0", features = ["derive"] }
serde-wasm-bindgen = "0.4"

[dependencies.web-sys]
version = "0.3"
features = [
  "console",
  "Performance",
]
```

#### 2.2 构建脚本
```json
// package.json scripts
{
  "scripts": {
    "build:wasm": "cd wasm && wasm-pack build --target web --out-dir ../public/wasm",
    "dev": "npm run build:wasm && vite",
    "build": "npm run build:wasm && vite build"
  }
}
```

### 第三阶段：实时数据和离线功能 (2-3周)

#### 3.1 WebSocket服务集成
```typescript
// src/hooks/useWebSocket.ts
import { useEffect, useRef, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';

export const useWebSocket = (url: string, options?: {
  onMessage?: (data: any) => void;
  onError?: (error: Event) => void;
  reconnectAttempts?: number;
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const queryClient = useQueryClient();

  const connect = () => {
    try {
      const ws = new WebSocket(url);
      
      ws.onopen = () => {
        setIsConnected(true);
        setError(null);
        reconnectAttemptsRef.current = 0;
        console.log('WebSocket connected');
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        // 更新React Query缓存
        queryClient.setQueryData(['market-data', data.symbol], data);
        
        options?.onMessage?.(data);
      };

      ws.onclose = () => {
        setIsConnected(false);
        console.log('WebSocket disconnected');
        
        // 自动重连
        if (reconnectAttemptsRef.current < (options?.reconnectAttempts || 5)) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
          setTimeout(() => {
            reconnectAttemptsRef.current++;
            connect();
          }, delay);
        }
      };

      ws.onerror = (error) => {
        setError('WebSocket连接错误');
        options?.onError?.(error);
      };

      wsRef.current = ws;
    } catch (err) {
      setError('无法创建WebSocket连接');
    }
  };

  useEffect(() => {
    connect();
    
    return () => {
      wsRef.current?.close();
    };
  }, [url]);

  const sendMessage = (message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    }
  };

  return { isConnected, error, sendMessage };
};
```

### 第四阶段：用户体验优化 (1-2周)

#### 4.1 性能监控集成
```typescript
// src/utils/analytics.ts
export const trackPerformance = () => {
  // 监控Core Web Vitals
  if ('web-vital' in window) {
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(console.log);
      getFID(console.log);
      getFCP(console.log);
      getLCP(console.log);
      getTTFB(console.log);
    });
  }
};
```

## 📋 实施检查清单

### PWA基础功能
- [ ] Web App Manifest配置
- [ ] Service Worker注册
- [ ] 离线页面设计
- [ ] 安装提示实现
- [ ] 更新机制

### 性能优化
- [ ] Web Assembly模块
- [ ] Web Workers集成
- [ ] 代码分割优化
- [ ] 资源预加载
- [ ] 缓存策略

### 数据管理
- [ ] IndexedDB存储
- [ ] 离线同步队列
- [ ] 数据压缩
- [ ] 增量更新
- [ ] 冲突解决

### 用户体验
- [ ] 响应式设计
- [ ] 加载状态
- [ ] 错误处理
- [ ] 无障碍访问
- [ ] 国际化支持

### 测试和部署
- [ ] 单元测试
- [ ] 集成测试
- [ ] PWA审计
- [ ] 性能测试
- [ ] HTTPS部署

## 🎯 成功指标

### 技术指标
- Lighthouse PWA分数 > 90
- 首次内容绘制 < 1.5s
- 最大内容绘制 < 2.5s
- 累积布局偏移 < 0.1
- 首次输入延迟 < 100ms

### 用户体验指标
- 应用安装率 > 20%
- 离线使用率 > 10%
- 用户留存率 > 60%
- 页面加载成功率 > 99%

这个实施指南提供了完整的PWA开发路径，确保SFQuant能够提供优秀的用户体验。
