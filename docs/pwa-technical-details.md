# SFQuant PWA 技术细节深度分析

## 1. PWA核心技术栈详解

### 1.1 技术架构层次
```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  React 18 + TypeScript + Ant Design                       │
│  - 组件化开发                                               │
│  - 类型安全                                                 │
│  - 企业级UI组件                                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    状态管理层 (State Layer)                  │
├─────────────────────────────────────────────────────────────┤
│  Zustand + React Query                                     │
│  - 轻量级状态管理                                           │
│  - 服务端状态缓存                                           │
│  - 乐观更新                                                 │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    PWA增强层 (PWA Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  Service Worker + Web App Manifest + IndexedDB             │
│  - 离线缓存策略                                             │
│  - 应用安装体验                                             │
│  - 本地数据存储                                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    性能优化层 (Performance)                  │
├─────────────────────────────────────────────────────────────┤
│  Web Assembly + Web Workers + Shared Array Buffer          │
│  - 高性能计算                                               │
│  - 多线程处理                                               │
│  - 内存共享                                                 │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    网络通信层 (Network)                      │
├─────────────────────────────────────────────────────────────┤
│  WebSocket + HTTP/2 + Server-Sent Events                   │
│  - 实时数据推送                                             │
│  - 高效HTTP通信                                             │
│  - 服务端事件流                                             │
└─────────────────────────────────────────────────────────────┘
```

## 2. 关键技术实现细节

### 2.1 Service Worker高级策略

#### 智能缓存策略
```typescript
// sw-strategies.ts - 高级缓存策略
export class CacheStrategies {
  // 网络优先策略 - 用于API请求
  static async networkFirst(request: Request): Promise<Response> {
    const cache = await caches.open('api-cache-v1');

    try {
      // 尝试网络请求
      const networkResponse = await fetch(request);

      if (networkResponse.ok) {
        // 成功则更新缓存
        cache.put(request, networkResponse.clone());
        return networkResponse;
      }

      // 网络失败，返回缓存
      return await cache.match(request) || new Response('Network Error', { status: 503 });
    } catch (error) {
      // 网络不可用，返回缓存
      const cachedResponse = await cache.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }

      // 返回离线页面
      return await cache.match('/offline.html') || new Response('Offline', { status: 503 });
    }
  }

  // 缓存优先策略 - 用于静态资源
  static async cacheFirst(request: Request): Promise<Response> {
    const cache = await caches.open('static-cache-v1');
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  }

  // 最新优先策略 - 用于关键数据
  static async staleWhileRevalidate(request: Request): Promise<Response> {
    const cache = await caches.open('dynamic-cache-v1');
    const cachedResponse = await cache.match(request);

    // 后台更新缓存
    const fetchPromise = fetch(request).then(response => {
      if (response.ok) {
        cache.put(request, response.clone());
      }
      return response;
    });

    // 立即返回缓存，如果有的话
    return cachedResponse || await fetchPromise;
  }
}

// Service Worker主文件
self.addEventListener('fetch', (event: FetchEvent) => {
  const { request } = event;
  const url = new URL(request.url);

  // API请求 - 网络优先
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(CacheStrategies.networkFirst(request));
    return;
  }

  // 静态资源 - 缓存优先
  if (request.destination === 'script' || request.destination === 'style') {
    event.respondWith(CacheStrategies.cacheFirst(request));
    return;
  }

  // 动态内容 - 最新优先
  event.respondWith(CacheStrategies.staleWhileRevalidate(request));
});
```

#### 后台数据同步
```typescript
// background-sync.ts - 后台数据同步
export class BackgroundSync {
  static async registerSync(tag: string, data: any) {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      const registration = await navigator.serviceWorker.ready;

      // 存储待同步数据
      await this.storeForSync(tag, data);

      // 注册后台同步
      await registration.sync.register(tag);
    } else {
      // 降级处理 - 立即同步
      await this.syncData(tag, data);
    }
  }

  private static async storeForSync(tag: string, data: any) {
    const db = await this.openDB();
    const transaction = db.transaction(['sync-queue'], 'readwrite');
    const store = transaction.objectStore('sync-queue');

    await store.add({
      tag,
      data,
      timestamp: Date.now(),
      retries: 0
    });
  }

  private static async syncData(tag: string, data: any) {
    try {
      switch (tag) {
        case 'strategy-update':
          await fetch('/api/strategies', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          });
          break;

        case 'trade-order':
          await fetch('/api/trades', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          });
          break;
      }
    } catch (error) {
      console.error('Sync failed:', error);
      throw error;
    }
  }
}

// Service Worker中的同步处理
self.addEventListener('sync', async (event: SyncEvent) => {
  if (event.tag.startsWith('strategy-')) {
    event.waitUntil(BackgroundSync.handleStrategySync(event.tag));
  } else if (event.tag.startsWith('trade-')) {
    event.waitUntil(BackgroundSync.handleTradeSync(event.tag));
  }
});
```

### 2.2 Web Assembly高性能计算

#### 技术指标计算模块
```rust
// wasm/src/indicators.rs - Rust实现的技术指标
use wasm_bindgen::prelude::*;

#[wasm_bindgen]
pub struct TechnicalIndicators {
    data: Vec<f64>,
}

#[wasm_bindgen]
impl TechnicalIndicators {
    #[wasm_bindgen(constructor)]
    pub fn new() -> TechnicalIndicators {
        TechnicalIndicators {
            data: Vec::new(),
        }
    }

    // 移动平均线
    #[wasm_bindgen]
    pub fn sma(&self, prices: &[f64], period: usize) -> Vec<f64> {
        let mut result = Vec::new();

        for i in period..=prices.len() {
            let sum: f64 = prices[i-period..i].iter().sum();
            result.push(sum / period as f64);
        }

        result
    }

    // 指数移动平均线
    #[wasm_bindgen]
    pub fn ema(&self, prices: &[f64], period: usize) -> Vec<f64> {
        let mut result = Vec::new();
        let multiplier = 2.0 / (period as f64 + 1.0);

        if prices.is_empty() {
            return result;
        }

        result.push(prices[0]);

        for i in 1..prices.len() {
            let ema = (prices[i] * multiplier) + (result[i-1] * (1.0 - multiplier));
            result.push(ema);
        }

        result
    }

    // RSI相对强弱指数
    #[wasm_bindgen]
    pub fn rsi(&self, prices: &[f64], period: usize) -> Vec<f64> {
        let mut gains = Vec::new();
        let mut losses = Vec::new();

        // 计算价格变化
        for i in 1..prices.len() {
            let change = prices[i] - prices[i-1];
            gains.push(if change > 0.0 { change } else { 0.0 });
            losses.push(if change < 0.0 { -change } else { 0.0 });
        }

        let avg_gains = self.sma(&gains, period);
        let avg_losses = self.sma(&losses, period);

        let mut rsi_values = Vec::new();
        for i in 0..avg_gains.len() {
            if avg_losses[i] == 0.0 {
                rsi_values.push(100.0);
            } else {
                let rs = avg_gains[i] / avg_losses[i];
                let rsi = 100.0 - (100.0 / (1.0 + rs));
                rsi_values.push(rsi);
            }
        }

        rsi_values
    }

    // MACD指标
    #[wasm_bindgen]
    pub fn macd(&self, prices: &[f64], fast: usize, slow: usize, signal: usize) -> Vec<f64> {
        let ema_fast = self.ema(prices, fast);
        let ema_slow = self.ema(prices, slow);

        let mut macd_line = Vec::new();
        let min_len = ema_fast.len().min(ema_slow.len());

        for i in 0..min_len {
            macd_line.push(ema_fast[i] - ema_slow[i]);
        }

        self.ema(&macd_line, signal)
    }
}

// 高频数据处理
#[wasm_bindgen]
pub fn process_tick_data(ticks: &[f64], window_size: usize) -> Vec<f64> {
    let mut processed = Vec::new();

    for window in ticks.windows(window_size) {
        let sum: f64 = window.iter().sum();
        let avg = sum / window_size as f64;
        processed.push(avg);
    }

    processed
}

// 套利机会检测
#[wasm_bindgen]
pub fn detect_arbitrage(
    exchange1_prices: &[f64],
    exchange2_prices: &[f64],
    threshold: f64
) -> Vec<bool> {
    let mut opportunities = Vec::new();

    let min_len = exchange1_prices.len().min(exchange2_prices.len());

    for i in 0..min_len {
        let price_diff = (exchange1_prices[i] - exchange2_prices[i]).abs();
        let percentage_diff = price_diff / exchange1_prices[i].min(exchange2_prices[i]);

        opportunities.push(percentage_diff > threshold);
    }

    opportunities
}
```

#### TypeScript集成
```typescript
// src/services/wasmService.ts
import init, { TechnicalIndicators, process_tick_data, detect_arbitrage } from '../wasm/pkg';

export class WasmService {
  private static instance: WasmService;
  private indicators: TechnicalIndicators | null = null;
  private initialized = false;

  static getInstance(): WasmService {
    if (!WasmService.instance) {
      WasmService.instance = new WasmService();
    }
    return WasmService.instance;
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await init();
      this.indicators = new TechnicalIndicators();
      this.initialized = true;
      console.log('WASM module initialized successfully');
    } catch (error) {
      console.error('Failed to initialize WASM module:', error);
      throw error;
    }
  }

  // 计算技术指标
  calculateSMA(prices: number[], period: number): number[] {
    if (!this.indicators) throw new Error('WASM not initialized');
    return Array.from(this.indicators.sma(new Float64Array(prices), period));
  }

  calculateEMA(prices: number[], period: number): number[] {
    if (!this.indicators) throw new Error('WASM not initialized');
    return Array.from(this.indicators.ema(new Float64Array(prices), period));
  }

  calculateRSI(prices: number[], period: number): number[] {
    if (!this.indicators) throw new Error('WASM not initialized');
    return Array.from(this.indicators.rsi(new Float64Array(prices), period));
  }

  calculateMACD(prices: number[], fast: number, slow: number, signal: number): number[] {
    if (!this.indicators) throw new Error('WASM not initialized');
    return Array.from(this.indicators.macd(new Float64Array(prices), fast, slow, signal));
  }

  // 高频数据处理
  processTickData(ticks: number[], windowSize: number): number[] {
    if (!this.initialized) throw new Error('WASM not initialized');
    return Array.from(process_tick_data(new Float64Array(ticks), windowSize));
  }

  // 套利检测
  detectArbitrageOpportunities(
    exchange1Prices: number[],
    exchange2Prices: number[],
    threshold: number
  ): boolean[] {
    if (!this.initialized) throw new Error('WASM not initialized');
    return Array.from(detect_arbitrage(
      new Float64Array(exchange1Prices),
      new Float64Array(exchange2Prices),
      threshold
    ));
  }
}

// 使用示例
export const useWasmIndicators = () => {
  const wasmService = WasmService.getInstance();

  const calculateIndicators = async (prices: number[]) => {
    await wasmService.initialize();

    return {
      sma20: wasmService.calculateSMA(prices, 20),
      ema12: wasmService.calculateEMA(prices, 12),
      rsi14: wasmService.calculateRSI(prices, 14),
      macd: wasmService.calculateMACD(prices, 12, 26, 9)
    };
  };

  return { calculateIndicators };
};
```

### 2.3 Web Workers多线程处理

#### 数据处理Worker
```typescript
// workers/dataProcessor.worker.ts
import { WasmService } from '../services/wasmService';

export interface WorkerMessage {
  id: string;
  type: 'PROCESS_MARKET_DATA' | 'CALCULATE_INDICATORS' | 'DETECT_ARBITRAGE';
  payload: any;
}

export interface WorkerResponse {
  id: string;
  success: boolean;
  data?: any;
  error?: string;
}

class DataProcessorWorker {
  private wasmService: WasmService;

  constructor() {
    this.wasmService = WasmService.getInstance();
    this.initialize();
  }

  private async initialize() {
    try {
      await this.wasmService.initialize();
      console.log('Data processor worker initialized');
    } catch (error) {
      console.error('Failed to initialize worker:', error);
    }
  }

  async processMessage(message: WorkerMessage): Promise<WorkerResponse> {
    try {
      switch (message.type) {
        case 'PROCESS_MARKET_DATA':
          return await this.processMarketData(message);

        case 'CALCULATE_INDICATORS':
          return await this.calculateIndicators(message);

        case 'DETECT_ARBITRAGE':
          return await this.detectArbitrage(message);

        default:
          throw new Error(`Unknown message type: ${message.type}`);
      }
    } catch (error) {
      return {
        id: message.id,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async processMarketData(message: WorkerMessage): Promise<WorkerResponse> {
    const { ticks, windowSize } = message.payload;
    const processed = this.wasmService.processTickData(ticks, windowSize);

    return {
      id: message.id,
      success: true,
      data: { processed }
    };
  }

  private async calculateIndicators(message: WorkerMessage): Promise<WorkerResponse> {
    const { prices, config } = message.payload;

    const indicators = {
      sma: this.wasmService.calculateSMA(prices, config.sma || 20),
      ema: this.wasmService.calculateEMA(prices, config.ema || 12),
      rsi: this.wasmService.calculateRSI(prices, config.rsi || 14),
      macd: this.wasmService.calculateMACD(prices, 12, 26, 9)
    };

    return {
      id: message.id,
      success: true,
      data: indicators
    };
  }

  private async detectArbitrage(message: WorkerMessage): Promise<WorkerResponse> {
    const { exchange1Prices, exchange2Prices, threshold } = message.payload;

    const opportunities = this.wasmService.detectArbitrageOpportunities(
      exchange1Prices,
      exchange2Prices,
      threshold
    );

    return {
      id: message.id,
      success: true,
      data: { opportunities }
    };
  }
}

// Worker主线程
const worker = new DataProcessorWorker();

self.addEventListener('message', async (event: MessageEvent<WorkerMessage>) => {
  const response = await worker.processMessage(event.data);
  self.postMessage(response);
});
```

#### Worker管理器
```typescript
// src/services/workerManager.ts
import { WorkerMessage, WorkerResponse } from '../workers/dataProcessor.worker';

export class WorkerManager {
  private workers: Worker[] = [];
  private currentWorkerIndex = 0;
  private pendingTasks = new Map<string, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
  }>();

  constructor(workerCount: number = navigator.hardwareConcurrency || 4) {
    this.initializeWorkers(workerCount);
  }

  private initializeWorkers(count: number) {
    for (let i = 0; i < count; i++) {
      const worker = new Worker('/workers/dataProcessor.worker.js');

      worker.addEventListener('message', (event: MessageEvent<WorkerResponse>) => {
        this.handleWorkerResponse(event.data);
      });

      worker.addEventListener('error', (error) => {
        console.error('Worker error:', error);
      });

      this.workers.push(worker);
    }
  }

  private handleWorkerResponse(response: WorkerResponse) {
    const task = this.pendingTasks.get(response.id);
    if (task) {
      this.pendingTasks.delete(response.id);

      if (response.success) {
        task.resolve(response.data);
      } else {
        task.reject(new Error(response.error || 'Worker task failed'));
      }
    }
  }

  private getNextWorker(): Worker {
    const worker = this.workers[this.currentWorkerIndex];
    this.currentWorkerIndex = (this.currentWorkerIndex + 1) % this.workers.length;
    return worker;
  }

  async executeTask<T>(type: WorkerMessage['type'], payload: any): Promise<T> {
    const id = `task_${Date.now()}_${Math.random()}`;
    const worker = this.getNextWorker();

    return new Promise<T>((resolve, reject) => {
      this.pendingTasks.set(id, { resolve, reject });

      const message: WorkerMessage = { id, type, payload };
      worker.postMessage(message);

      // 设置超时
      setTimeout(() => {
        if (this.pendingTasks.has(id)) {
          this.pendingTasks.delete(id);
          reject(new Error('Worker task timeout'));
        }
      }, 30000); // 30秒超时
    });
  }

  // 便捷方法
  async processMarketData(ticks: number[], windowSize: number) {
    return this.executeTask('PROCESS_MARKET_DATA', { ticks, windowSize });
  }

  async calculateIndicators(prices: number[], config: any) {
    return this.executeTask('CALCULATE_INDICATORS', { prices, config });
  }

  async detectArbitrage(exchange1Prices: number[], exchange2Prices: number[], threshold: number) {
    return this.executeTask('DETECT_ARBITRAGE', { exchange1Prices, exchange2Prices, threshold });
  }

  destroy() {
    this.workers.forEach(worker => worker.terminate());
    this.workers = [];
    this.pendingTasks.clear();
  }
}

// 全局实例
export const workerManager = new WorkerManager();
```

### 2.4 实时数据流处理

#### WebSocket连接池管理
```typescript
// src/services/websocketPool.ts
export interface ExchangeConfig {
  name: string;
  wsUrl: string;
  subscribeMessage: (symbols: string[]) => any;
  parseMessage: (data: any) => MarketData | null;
}

export class WebSocketPool {
  private connections = new Map<string, WebSocket>();
  private subscriptions = new Map<string, Set<string>>();
  private reconnectAttempts = new Map<string, number>();
  private messageHandlers = new Map<string, (data: MarketData) => void>();

  constructor(private configs: ExchangeConfig[]) {}

  async connect(exchangeName: string, symbols: string[]): Promise<void> {
    const config = this.configs.find(c => c.name === exchangeName);
    if (!config) throw new Error(`Unknown exchange: ${exchangeName}`);

    const ws = new WebSocket(config.wsUrl);

    ws.onopen = () => {
      console.log(`Connected to ${exchangeName}`);
      this.connections.set(exchangeName, ws);
      this.reconnectAttempts.set(exchangeName, 0);

      // 订阅符号
      const subscribeMsg = config.subscribeMessage(symbols);
      ws.send(JSON.stringify(subscribeMsg));
      this.subscriptions.set(exchangeName, new Set(symbols));
    };

    ws.onmessage = (event) => {
      const data = config.parseMessage(JSON.parse(event.data));
      if (data) {
        this.handleMarketData(exchangeName, data);
      }
    };

    ws.onclose = () => {
      console.log(`Disconnected from ${exchangeName}`);
      this.connections.delete(exchangeName);
      this.scheduleReconnect(exchangeName, symbols);
    };

    ws.onerror = (error) => {
      console.error(`WebSocket error for ${exchangeName}:`, error);
    };
  }

  private handleMarketData(exchange: string, data: MarketData) {
    // 发送到Service Worker进行处理
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'MARKET_DATA',
        exchange,
        data
      });
    }

    // 触发本地处理器
    const handler = this.messageHandlers.get(exchange);
    if (handler) {
      handler(data);
    }
  }

  private async scheduleReconnect(exchangeName: string, symbols: string[]) {
    const attempts = this.reconnectAttempts.get(exchangeName) || 0;

    if (attempts < 5) {
      const delay = Math.min(1000 * Math.pow(2, attempts), 30000);
      this.reconnectAttempts.set(exchangeName, attempts + 1);

      setTimeout(() => {
        this.connect(exchangeName, symbols);
      }, delay);
    }
  }

  subscribe(exchange: string, symbols: string[]) {
    const ws = this.connections.get(exchange);
    if (ws && ws.readyState === WebSocket.OPEN) {
      const config = this.configs.find(c => c.name === exchange);
      if (config) {
        const subscribeMsg = config.subscribeMessage(symbols);
        ws.send(JSON.stringify(subscribeMsg));

        const currentSubs = this.subscriptions.get(exchange) || new Set();
        symbols.forEach(symbol => currentSubs.add(symbol));
        this.subscriptions.set(exchange, currentSubs);
      }
    }
  }

  onMessage(exchange: string, handler: (data: MarketData) => void) {
    this.messageHandlers.set(exchange, handler);
  }
}
```

#### 数据流处理管道
```typescript
// src/services/dataStreamProcessor.ts
export class DataStreamProcessor {
  private buffers = new Map<string, MarketData[]>();
  private processors = new Map<string, (data: MarketData[]) => void>();
  private intervals = new Map<string, NodeJS.Timeout>();

  constructor(private workerManager: WorkerManager) {}

  // 注册数据流处理器
  registerProcessor(
    symbol: string,
    processor: (data: MarketData[]) => void,
    bufferSize: number = 100,
    flushInterval: number = 1000
  ) {
    this.processors.set(symbol, processor);
    this.buffers.set(symbol, []);

    // 定期刷新缓冲区
    const interval = setInterval(() => {
      this.flushBuffer(symbol);
    }, flushInterval);

    this.intervals.set(symbol, interval);
  }

  // 处理新的市场数据
  async processMarketData(data: MarketData) {
    const buffer = this.buffers.get(data.symbol) || [];
    buffer.push(data);

    // 限制缓冲区大小
    if (buffer.length > 1000) {
      buffer.splice(0, buffer.length - 1000);
    }

    this.buffers.set(data.symbol, buffer);

    // 实时处理
    await this.processRealtime(data);
  }

  private async processRealtime(data: MarketData) {
    try {
      // 使用Web Worker进行实时计算
      const indicators = await this.workerManager.calculateIndicators(
        [data.price],
        { sma: 20, ema: 12, rsi: 14 }
      );

      // 发送处理结果到UI
      this.broadcastUpdate(data.symbol, {
        ...data,
        indicators
      });
    } catch (error) {
      console.error('Real-time processing error:', error);
    }
  }

  private flushBuffer(symbol: string) {
    const buffer = this.buffers.get(symbol);
    const processor = this.processors.get(symbol);

    if (buffer && buffer.length > 0 && processor) {
      processor([...buffer]);
      buffer.length = 0; // 清空缓冲区
    }
  }

  private broadcastUpdate(symbol: string, data: any) {
    // 使用BroadcastChannel在多个标签页间同步
    const channel = new BroadcastChannel('market-data');
    channel.postMessage({
      type: 'MARKET_UPDATE',
      symbol,
      data
    });
  }

  destroy() {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals.clear();
    this.buffers.clear();
    this.processors.clear();
  }
}
```

### 2.5 离线功能和数据同步

#### 离线数据管理
```typescript
// src/services/offlineManager.ts
export class OfflineManager {
  private db: IDBDatabase | null = null;
  private syncQueue: Array<{
    id: string;
    type: string;
    data: any;
    timestamp: number;
  }> = [];

  async initialize() {
    return new Promise<void>((resolve, reject) => {
      const request = indexedDB.open('SFQuantOffline', 2);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        this.loadSyncQueue();
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // 创建存储
        if (!db.objectStoreNames.contains('marketData')) {
          const marketStore = db.createObjectStore('marketData', { keyPath: 'id' });
          marketStore.createIndex('symbol', 'symbol');
          marketStore.createIndex('timestamp', 'timestamp');
        }

        if (!db.objectStoreNames.contains('strategies')) {
          db.createObjectStore('strategies', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('syncQueue')) {
          db.createObjectStore('syncQueue', { keyPath: 'id' });
        }
      };
    });
  }

  // 存储市场数据
  async storeMarketData(data: MarketData[]) {
    if (!this.db) return;

    const transaction = this.db.transaction(['marketData'], 'readwrite');
    const store = transaction.objectStore('marketData');

    for (const item of data) {
      const record = {
        id: `${item.symbol}_${item.timestamp}`,
        ...item
      };
      store.put(record);
    }
  }

  // 获取离线数据
  async getOfflineData(symbol: string, from: number, to: number): Promise<MarketData[]> {
    if (!this.db) return [];

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['marketData'], 'readonly');
      const store = transaction.objectStore('marketData');
      const index = store.index('symbol');

      const request = index.getAll(IDBKeyRange.only(symbol));

      request.onsuccess = () => {
        const results = request.result.filter(
          item => item.timestamp >= from && item.timestamp <= to
        );
        resolve(results);
      };

      request.onerror = () => reject(request.error);
    });
  }

  // 添加到同步队列
  async addToSyncQueue(type: string, data: any) {
    const item = {
      id: `${type}_${Date.now()}_${Math.random()}`,
      type,
      data,
      timestamp: Date.now()
    };

    this.syncQueue.push(item);
    await this.saveSyncQueue();

    // 如果在线，立即尝试同步
    if (navigator.onLine) {
      this.processSyncQueue();
    }
  }

  private async loadSyncQueue() {
    if (!this.db) return;

    const transaction = this.db.transaction(['syncQueue'], 'readonly');
    const store = transaction.objectStore('syncQueue');
    const request = store.getAll();

    request.onsuccess = () => {
      this.syncQueue = request.result;
    };
  }

  private async saveSyncQueue() {
    if (!this.db) return;

    const transaction = this.db.transaction(['syncQueue'], 'readwrite');
    const store = transaction.objectStore('syncQueue');

    // 清空并重新保存
    store.clear();
    for (const item of this.syncQueue) {
      store.add(item);
    }
  }

  private async processSyncQueue() {
    const itemsToSync = [...this.syncQueue];

    for (const item of itemsToSync) {
      try {
        await this.syncItem(item);

        // 同步成功，从队列中移除
        const index = this.syncQueue.findIndex(i => i.id === item.id);
        if (index > -1) {
          this.syncQueue.splice(index, 1);
        }
      } catch (error) {
        console.error('Sync failed for item:', item, error);
      }
    }

    await this.saveSyncQueue();
  }

  private async syncItem(item: any) {
    switch (item.type) {
      case 'strategy_update':
        await fetch('/api/strategies', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(item.data)
        });
        break;

      case 'trade_order':
        await fetch('/api/trades', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(item.data)
        });
        break;
    }
  }
}

// 监听网络状态变化
window.addEventListener('online', () => {
  console.log('Back online, processing sync queue');
  const offlineManager = new OfflineManager();
  offlineManager.initialize().then(() => {
    offlineManager.processSyncQueue();
  });
});
```

### 2.6 性能监控和优化

#### 性能监控系统
```typescript
// src/services/performanceMonitor.ts
export class PerformanceMonitor {
  private metrics = new Map<string, number[]>();
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  private initializeObservers() {
    // 监控长任务
    if ('PerformanceObserver' in window) {
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('longTask', entry.duration);

          if (entry.duration > 50) {
            console.warn('Long task detected:', entry.duration + 'ms');
          }
        }
      });

      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.push(longTaskObserver);

      // 监控导航性能
      const navigationObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordNavigationMetrics(entry as PerformanceNavigationTiming);
        }
      });

      navigationObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navigationObserver);
    }
  }

  private recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const values = this.metrics.get(name)!;
    values.push(value);

    // 保持最近100个值
    if (values.length > 100) {
      values.shift();
    }
  }

  private recordNavigationMetrics(entry: PerformanceNavigationTiming) {
    this.recordMetric('domContentLoaded', entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart);
    this.recordMetric('loadComplete', entry.loadEventEnd - entry.loadEventStart);
    this.recordMetric('firstPaint', entry.responseEnd - entry.requestStart);
  }

  // 测量函数执行时间
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    try {
      const result = await fn();
      const duration = performance.now() - start;
      this.recordMetric(name, duration);
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      this.recordMetric(`${name}_error`, duration);
      throw error;
    }
  }

  measure<T>(name: string, fn: () => T): T {
    const start = performance.now();
    try {
      const result = fn();
      const duration = performance.now() - start;
      this.recordMetric(name, duration);
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      this.recordMetric(`${name}_error`, duration);
      throw error;
    }
  }

  // 获取性能报告
  getPerformanceReport() {
    const report: Record<string, any> = {};

    for (const [name, values] of this.metrics) {
      if (values.length > 0) {
        const sorted = [...values].sort((a, b) => a - b);
        report[name] = {
          count: values.length,
          min: sorted[0],
          max: sorted[sorted.length - 1],
          avg: values.reduce((a, b) => a + b, 0) / values.length,
          p50: sorted[Math.floor(sorted.length * 0.5)],
          p90: sorted[Math.floor(sorted.length * 0.9)],
          p99: sorted[Math.floor(sorted.length * 0.99)]
        };
      }
    }

    return report;
  }

  // 内存使用监控
  getMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
        usage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      };
    }
    return null;
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();
```

这些技术细节展示了PWA在SFQuant项目中的强大能力：

1. **Service Worker**: 提供智能缓存和离线功能
2. **Web Assembly**: 高性能技术指标计算
3. **Web Workers**: 多线程数据处理，避免UI阻塞
4. **IndexedDB**: 大容量本地数据存储
5. **WebSocket池**: 高效的实时数据连接管理
6. **离线同步**: 网络断开时的数据同步机制
7. **性能监控**: 实时性能监控和优化

这个方案既保持了Web的便利性，又提供了接近原生应用的性能和体验。
