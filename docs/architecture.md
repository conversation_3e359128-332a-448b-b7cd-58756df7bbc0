# SFQuant 系统架构设计

## 1. 整体架构概览

### 1.1 架构模式
采用PWA + 微服务架构，支持Web访问和应用安装双重体验。

```
┌─────────────────────────────────────────────────────────────┐
│                    PWA前端层 (PWA Frontend)                  │
├─────────────────────────────────────────────────────────────┤
│  React + TypeScript + Vite + PWA                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 策略管理界面 │ │ 数据可视化   │ │ 实时监控面板 │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    PWA增强层 (PWA Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Service     │ │ Web App     │ │ IndexedDB   │           │
│  │ Worker      │ │ Manifest    │ │ 本地存储     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Web         │ │ Web         │ │ Push        │           │
│  │ Assembly    │ │ Workers     │ │ Notifications│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      API网关层 (Gateway)                     │
├─────────────────────────────────────────────────────────────┤
│  - 路由转发                                                  │
│  - 身份验证                                                  │
│  - 限流控制                                                  │
│  - 负载均衡                                                  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      微服务层 (Services)                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 用户服务     │ │ 策略服务     │ │ 交易服务     │ │ 数据服务 │ │
│  │ User        │ │ Strategy    │ │ Trading     │ │ Data    │ │
│  │ Service     │ │ Service     │ │ Service     │ │ Service │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 回测服务     │ │ 风控服务     │ │ 通知服务     │ │ 监控服务 │ │
│  │ Backtest    │ │ Risk        │ │ Notification│ │ Monitor │ │
│  │ Service     │ │ Service     │ │ Service     │ │ Service │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据层 (Data Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│  │ PostgreSQL  │ │ Redis       │ │ InfluxDB    │             │
│  │ (主数据库)   │ │ (缓存/队列)  │ │ (时序数据)   │             │
│  └─────────────┘ └─────────────┘ └─────────────┘             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    外部接口层 (External APIs)                 │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│  │ CEX APIs    │ │ DEX APIs    │ │ 区块链节点   │             │
│  │ (CCXT)      │ │ (Web3)      │ │ (RPC)       │             │
│  └─────────────┘ └─────────────┘ └─────────────┘             │
└─────────────────────────────────────────────────────────────┘
```

## 2. 核心服务详细设计

### 2.1 用户服务 (User Service)
**职责**: 用户管理、身份验证、权限控制

**核心功能**:
- 用户注册/登录
- JWT令牌管理
- API密钥管理
- 权限控制

**技术栈**: Node.js + TypeScript + Express

### 2.2 策略服务 (Strategy Service)
**职责**: 策略管理、策略执行引擎

**核心功能**:
- 策略CRUD操作
- 策略模板管理
- 策略执行引擎
- 策略版本控制

**技术栈**: Node.js + TypeScript + VM2(沙箱执行)

### 2.3 交易服务 (Trading Service)
**职责**: 交易执行、订单管理

**核心功能**:
- 统一交易接口
- 订单管理
- 仓位管理
- 交易记录

**技术栈**: Node.js + TypeScript + CCXT + Web3.js

### 2.4 数据服务 (Data Service)
**职责**: 数据获取、处理、存储

**核心功能**:
- 实时行情数据
- 历史数据管理
- 技术指标计算
- 数据清洗

**技术栈**: Node.js + TypeScript + WebSocket + InfluxDB

### 2.5 回测服务 (Backtest Service)
**职责**: 策略回测、性能分析

**核心功能**:
- 历史数据回测
- 性能指标计算
- 回测报告生成
- 参数优化

**技术栈**: Node.js + TypeScript + 数学计算库

### 2.6 风控服务 (Risk Service)
**职责**: 风险监控、风险控制

**核心功能**:
- 实时风险监控
- 风险指标计算
- 预警通知
- 自动止损

**技术栈**: Node.js + TypeScript + Redis

## 3. 数据架构设计

### 3.1 数据库设计

#### 3.1.1 PostgreSQL (主数据库)
```sql
-- 用户表
users (
  id, username, email, password_hash,
  api_keys, created_at, updated_at
)

-- 策略表
strategies (
  id, user_id, name, description, code,
  strategy_type, status, created_at, updated_at
)

-- 交易记录表
trades (
  id, user_id, strategy_id, exchange, symbol,
  side, amount, price, fee, timestamp
)

-- 回测结果表
backtest_results (
  id, strategy_id, start_date, end_date,
  total_return, sharpe_ratio, max_drawdown, created_at
)
```

#### 3.1.2 Redis (缓存和队列)
```
-- 实时数据缓存
market_data:{exchange}:{symbol} -> JSON
user_session:{user_id} -> session_data
strategy_cache:{strategy_id} -> strategy_data

-- 消息队列
queue:trading_signals
queue:risk_alerts
queue:notifications
```

#### 3.1.3 InfluxDB (时序数据)
```
-- K线数据
klines,exchange=binance,symbol=BTCUSDT open=50000,high=51000,low=49000,close=50500,volume=100

-- 策略收益
strategy_returns,strategy_id=123,user_id=456 return=0.05,drawdown=0.02

-- 系统监控
system_metrics,service=trading_service cpu=80,memory=60,requests=1000
```

## 4. 技术选型说明

### 4.1 前端技术选型
- **React 18+**: 现代化UI框架，生态丰富
- **TypeScript**: 类型安全，提高代码质量
- **Next.js**: SSR支持，SEO友好
- **Ant Design**: 企业级UI组件库
- **TradingView**: 专业金融图表库

### 4.2 后端技术选型
- **Node.js**: JavaScript运行时，与前端技术栈统一
- **TypeScript**: 全栈类型安全
- **Express/Fastify**: 轻量级Web框架
- **CCXT**: 统一交易所API接口
- **Web3.js**: 以太坊区块链交互

### 4.3 数据库选型
- **PostgreSQL**: 关系型数据库，ACID特性，适合交易数据
- **Redis**: 内存数据库，高性能缓存和消息队列
- **InfluxDB**: 时序数据库，适合存储K线和监控数据

## 5. 部署架构

### 5.1 容器化部署
```yaml
# docker-compose.yml 示例
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"

  api-gateway:
    build: ./api-gateway
    ports:
      - "8080:8080"

  user-service:
    build: ./services/user
    environment:
      - DATABASE_URL=postgresql://...

  trading-service:
    build: ./services/trading
    environment:
      - REDIS_URL=redis://...

  postgres:
    image: postgres:14
    environment:
      - POSTGRES_DB=sfquant

  redis:
    image: redis:7-alpine
```

### 5.2 生产环境部署
- **容器编排**: Kubernetes
- **负载均衡**: Nginx/HAProxy
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
- **CI/CD**: GitHub Actions

## 6. 安全架构

### 6.1 身份验证和授权
- JWT令牌认证
- API密钥管理
- 角色权限控制
- OAuth2集成

### 6.2 数据安全
- 数据库连接加密
- API密钥加密存储
- 敏感数据脱敏
- 审计日志记录

### 6.3 网络安全
- HTTPS强制加密
- API限流防护
- DDoS防护
- 防火墙配置

## 7. 性能优化

### 7.1 缓存策略
- Redis缓存热点数据
- CDN静态资源缓存
- 数据库查询缓存
- 应用层缓存

### 7.2 数据库优化
- 索引优化
- 分库分表
- 读写分离
- 连接池管理

### 7.3 系统监控
- 实时性能监控
- 错误日志追踪
- 业务指标监控
- 告警机制

---

**文档版本**: v1.0
**创建日期**: 2024年
**维护人员**: 架构团队
