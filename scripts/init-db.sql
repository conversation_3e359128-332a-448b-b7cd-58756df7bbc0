-- SFQuant 数据库初始化脚本
-- 创建数据库和用户

-- 创建数据库
CREATE DATABASE sfquant;

-- 创建用户
CREATE USER sfquant WITH PASSWORD 'sfquant_password_2024';

-- 授权
GRANT ALL PRIVILEGES ON DATABASE sfquant TO sfquant;

-- 连接到 sfquant 数据库
\c sfquant;

-- 授权 schema 权限
GRANT ALL ON SCHEMA public TO sfquant;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO sfquant;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO sfquant;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 创建索引优化查询性能
-- 这些索引将在 Prisma 迁移后创建

-- 用户表索引
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username ON users(username);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users(created_at);

-- 策略表索引
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_strategies_user_id ON strategies(user_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_strategies_type ON strategies(type);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_strategies_status ON strategies(status);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_strategies_created_at ON strategies(created_at);

-- 交易表索引
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_strategy_id ON trades(strategy_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_symbol ON trades(symbol);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_timestamp ON trades(timestamp);

-- 性能表索引
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performances_strategy_id ON performances(strategy_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performances_timestamp ON performances(timestamp);

-- API 密钥表索引
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_keys_key ON api_keys(key);

-- 价格告警表索引
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_price_alerts_user_id ON price_alerts(user_id);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_price_alerts_symbol ON price_alerts(symbol);

-- 系统日志表索引
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_system_logs_level ON system_logs(level);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp);

-- 创建视图用于常用查询
-- 活跃策略视图
-- CREATE OR REPLACE VIEW active_strategies AS
-- SELECT s.*, u.username, u.email
-- FROM strategies s
-- JOIN users u ON s.user_id = u.id
-- WHERE s.status = 'active';

-- 策略性能汇总视图
-- CREATE OR REPLACE VIEW strategy_performance_summary AS
-- SELECT 
--     s.id,
--     s.name,
--     s.type,
--     s.user_id,
--     COUNT(t.id) as total_trades,
--     SUM(CASE WHEN t.side = 'sell' THEN t.amount * t.price ELSE -t.amount * t.price END) as total_pnl,
--     AVG(CASE WHEN t.side = 'sell' THEN t.amount * t.price ELSE -t.amount * t.price END) as avg_trade_pnl
-- FROM strategies s
-- LEFT JOIN trades t ON s.id = t.strategy_id
-- GROUP BY s.id, s.name, s.type, s.user_id;

-- 创建函数用于数据清理
CREATE OR REPLACE FUNCTION cleanup_old_logs()
RETURNS void AS $$
BEGIN
    -- 删除30天前的系统日志
    DELETE FROM system_logs 
    WHERE timestamp < NOW() - INTERVAL '30 days';
    
    -- 删除已触发且超过7天的价格告警
    DELETE FROM price_alerts 
    WHERE triggered = true 
    AND triggered_at < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- 创建定时任务（需要 pg_cron 扩展）
-- SELECT cron.schedule('cleanup-logs', '0 2 * * *', 'SELECT cleanup_old_logs();');

-- 插入初始数据
-- 创建管理员用户（密码: admin123）
INSERT INTO users (id, email, username, password_hash, role, created_at, updated_at)
VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    'admin',
    '$2b$10$rQZ8qVZ8qVZ8qVZ8qVZ8qOqVZ8qVZ8qVZ8qVZ8qVZ8qVZ8qVZ8qVZ8',
    'admin',
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- 创建演示用户（密码: demo123）
INSERT INTO users (id, email, username, password_hash, role, created_at, updated_at)
VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    'demo',
    '$2b$10$demohashdemohashdemohashdemohashdemohashdemohashdemohashdemo',
    'user',
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '✅ SFQuant 数据库初始化完成';
    RAISE NOTICE '📊 数据库: sfquant';
    RAISE NOTICE '👤 用户: sfquant';
    RAISE NOTICE '🔑 密码: sfquant_password_2024';
    RAISE NOTICE '🚀 请运行 Prisma 迁移来创建表结构';
END $$;
