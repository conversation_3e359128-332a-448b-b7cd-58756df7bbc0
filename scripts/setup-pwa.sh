#!/bin/bash

# SFQuant PWA 项目快速设置脚本
# 使用方法: ./scripts/setup-pwa.sh

set -e

echo "🚀 开始设置 SFQuant PWA 项目..."

# 检查必要工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装 $1"
        exit 1
    else
        echo "✅ $1 已安装"
    fi
}

echo "📋 检查必要工具..."
check_tool "node"
check_tool "npm"
check_tool "pnpm"
check_tool "rustc"
check_tool "wasm-pack"

# 检查Node.js版本
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 版本需要 >= 18，当前版本: $(node -v)"
    exit 1
else
    echo "✅ Node.js 版本符合要求: $(node -v)"
fi

# 安装根目录依赖
echo "📦 安装根目录依赖..."
pnpm install

# 设置前端项目
echo "🎨 设置前端 PWA 项目..."
cd apps/frontend

# 创建基础目录结构
mkdir -p src/{components,hooks,services,stores,types,utils,workers}
mkdir -p src/components/{common,trading,strategy,charts}
mkdir -p public/{icons,workers}

# 创建基础文件
echo "📄 创建基础文件..."

# 创建主入口文件
cat > src/main.tsx << 'EOF'
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// 注册Service Worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
EOF

# 创建App组件
cat > src/App.tsx << 'EOF'
import React from 'react';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import './App.css';

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <div className="App">
        <header className="App-header">
          <h1>🚀 SFQuant PWA</h1>
          <p>智能量化交易平台</p>
          <p>PWA 项目已成功启动！</p>
        </header>
      </div>
    </ConfigProvider>
  );
}

export default App;
EOF

# 创建CSS文件
cat > src/index.css << 'EOF'
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}
EOF

cat > src/App.css << 'EOF'
.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  border-radius: 8px;
}

.App-header h1 {
  margin: 0 0 16px 0;
  font-size: 2.5rem;
}

.App-header p {
  margin: 8px 0;
  font-size: 1.2rem;
}
EOF

# 创建基础图标文件（占位符）
echo "🎨 创建PWA图标..."
mkdir -p public/icons

# 创建简单的SVG图标作为占位符
cat > public/icons/icon.svg << 'EOF'
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" fill="#1890ff"/>
  <text x="256" y="280" font-family="Arial, sans-serif" font-size="120" fill="white" text-anchor="middle">SF</text>
</svg>
EOF

# 安装前端依赖
echo "📦 安装前端依赖..."
pnpm install

cd ../..

# 设置WASM模块
echo "🦀 设置 WASM 模块..."
cd wasm

# 构建WASM模块
echo "🔨 构建 WASM 模块..."
wasm-pack build --target web --out-dir ../apps/frontend/public/wasm

cd ..

# 创建开发脚本
echo "📝 创建开发脚本..."
cat > scripts/dev.sh << 'EOF'
#!/bin/bash

echo "🚀 启动 SFQuant PWA 开发环境..."

# 启动后端服务（如果存在）
if [ -d "apps/api-gateway" ]; then
    echo "🔧 启动后端服务..."
    cd apps/api-gateway
    pnpm dev &
    BACKEND_PID=$!
    cd ../..
fi

# 启动前端PWA
echo "🎨 启动前端 PWA..."
cd apps/frontend
pnpm dev &
FRONTEND_PID=$!

# 等待用户中断
echo "✅ 开发环境已启动！"
echo "📱 前端地址: http://localhost:3001"
echo "🔧 后端地址: http://localhost:8080 (如果已启动)"
echo ""
echo "按 Ctrl+C 停止所有服务..."

# 捕获中断信号
trap 'echo "🛑 停止所有服务..."; kill $FRONTEND_PID 2>/dev/null; kill $BACKEND_PID 2>/dev/null; exit 0' INT

# 等待
wait
EOF

chmod +x scripts/dev.sh

# 创建构建脚本
cat > scripts/build.sh << 'EOF'
#!/bin/bash

echo "🔨 构建 SFQuant PWA 项目..."

# 构建WASM模块
echo "🦀 构建 WASM 模块..."
cd wasm
wasm-pack build --target web --out-dir ../apps/frontend/public/wasm
cd ..

# 构建前端
echo "🎨 构建前端 PWA..."
cd apps/frontend
pnpm build
cd ../..

echo "✅ 构建完成！"
echo "📁 构建文件位置: apps/frontend/dist"
EOF

chmod +x scripts/build.sh

# 更新根目录package.json脚本
echo "📝 更新根目录脚本..."
cat > package.json << 'EOF'
{
  "name": "sfquant",
  "version": "0.1.0",
  "description": "SFQuant - 智能量化交易平台 (PWA版本)",
  "private": true,
  "workspaces": [
    "apps/*",
    "services/*",
    "packages/*"
  ],
  "scripts": {
    "dev": "./scripts/dev.sh",
    "build": "./scripts/build.sh",
    "dev:frontend": "cd apps/frontend && pnpm dev",
    "build:frontend": "cd apps/frontend && pnpm build",
    "build:wasm": "cd wasm && wasm-pack build --target web --out-dir ../apps/frontend/public/wasm",
    "test": "pnpm -r test",
    "test:coverage": "pnpm -r test:coverage",
    "lint": "pnpm -r lint",
    "lint:fix": "pnpm -r lint:fix",
    "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"",
    "clean": "pnpm -r clean && rm -rf node_modules",
    "docker:up": "docker-compose up -d",
    "docker:down": "docker-compose down",
    "docker:logs": "docker-compose logs -f",
    "prepare": "husky install",
    "pwa:audit": "cd apps/frontend && pnpm pwa:audit"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "concurrently": "^8.2.0",
    "eslint": "^8.45.0",
    "eslint-config-prettier": "^9.0.0",
    "eslint-plugin-prettier": "^5.0.0",
    "husky": "^8.0.3",
    "lint-staged": "^13.2.3",
    "prettier": "^3.0.0",
    "typescript": "^5.1.6"
  },
  "engines": {
    "node": ">=18.0.0",
    "pnpm": ">=8.0.0"
  },
  "packageManager": "pnpm@8.6.12",
  "keywords": [
    "pwa",
    "cryptocurrency",
    "quantitative-trading",
    "trading-bot",
    "defi",
    "arbitrage",
    "typescript",
    "react",
    "webassembly"
  ],
  "author": "SFQuant Team",
  "license": "MIT",
  "repository": {
    "type": "git",
    "url": "https://github.com/your-org/sfquant.git"
  },
  "bugs": {
    "url": "https://github.com/your-org/sfquant/issues"
  },
  "homepage": "https://github.com/your-org/sfquant#readme"
}
EOF

echo ""
echo "🎉 SFQuant PWA 项目设置完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 运行 'pnpm install' 安装依赖"
echo "2. 运行 'pnpm dev' 启动开发环境"
echo "3. 访问 http://localhost:3001 查看PWA应用"
echo ""
echo "🔧 有用的命令:"
echo "- pnpm dev          # 启动开发环境"
echo "- pnpm build        # 构建生产版本"
echo "- pnpm pwa:audit    # PWA审计"
echo "- pnpm test         # 运行测试"
echo ""
echo "📚 文档位置:"
echo "- docs/pwa-implementation-plan.md    # PWA实施方案"
echo "- docs/pwa-technical-details.md      # PWA技术细节"
echo "- docs/pwa-implementation-guide.md   # PWA实施指南"
echo ""
echo "✨ 开始你的PWA量化交易平台开发之旅吧！"
