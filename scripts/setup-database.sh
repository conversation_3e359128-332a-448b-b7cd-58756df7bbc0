#!/bin/bash

# SFQuant 数据库设置脚本
# 此脚本将设置 PostgreSQL、Redis 和 InfluxDB

set -e

echo "🚀 开始设置 SFQuant 数据库..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker 未安装，请先安装 Docker${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose 未安装，请先安装 Docker Compose${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Docker 环境检查通过${NC}"
}

# 创建必要的目录
create_directories() {
    echo -e "${BLUE}📁 创建必要的目录...${NC}"
    
    mkdir -p docker/redis
    mkdir -p docker/postgres
    mkdir -p docker/influxdb
    mkdir -p logs
    mkdir -p data/backups
    
    echo -e "${GREEN}✅ 目录创建完成${NC}"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f "apps/api-gateway/.env" ]; then
        echo -e "${YELLOW}⚠️ 环境变量文件不存在，从模板复制...${NC}"
        cp apps/api-gateway/.env.example apps/api-gateway/.env
        echo -e "${YELLOW}📝 请编辑 apps/api-gateway/.env 文件配置您的环境变量${NC}"
    else
        echo -e "${GREEN}✅ 环境变量文件已存在${NC}"
    fi
}

# 启动数据库服务
start_databases() {
    echo -e "${BLUE}🔄 启动数据库服务...${NC}"
    
    # 只启动数据库相关服务
    docker-compose up -d postgres redis influxdb
    
    echo -e "${YELLOW}⏳ 等待数据库服务启动...${NC}"
    sleep 10
}

# 检查数据库连接
check_database_connections() {
    echo -e "${BLUE}🔍 检查数据库连接...${NC}"
    
    # 检查 PostgreSQL
    echo -e "${YELLOW}检查 PostgreSQL...${NC}"
    for i in {1..30}; do
        if docker-compose exec -T postgres pg_isready -U sfquant -d sfquant > /dev/null 2>&1; then
            echo -e "${GREEN}✅ PostgreSQL 连接成功${NC}"
            break
        fi
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ PostgreSQL 连接失败${NC}"
            exit 1
        fi
        sleep 2
    done
    
    # 检查 Redis
    echo -e "${YELLOW}检查 Redis...${NC}"
    for i in {1..30}; do
        if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Redis 连接成功${NC}"
            break
        fi
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ Redis 连接失败${NC}"
            exit 1
        fi
        sleep 2
    done
    
    # 检查 InfluxDB
    echo -e "${YELLOW}检查 InfluxDB...${NC}"
    for i in {1..30}; do
        if curl -f http://localhost:8086/ping > /dev/null 2>&1; then
            echo -e "${GREEN}✅ InfluxDB 连接成功${NC}"
            break
        fi
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ InfluxDB 连接失败${NC}"
            exit 1
        fi
        sleep 2
    done
}

# 运行 Prisma 迁移
run_prisma_migration() {
    echo -e "${BLUE}🔄 运行 Prisma 数据库迁移...${NC}"
    
    cd apps/api-gateway
    
    # 检查是否安装了依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}📦 安装依赖...${NC}"
        pnpm install
    fi
    
    # 生成 Prisma 客户端
    echo -e "${YELLOW}🔧 生成 Prisma 客户端...${NC}"
    npx prisma generate
    
    # 运行数据库迁移
    echo -e "${YELLOW}🔄 运行数据库迁移...${NC}"
    npx prisma db push
    
    # 可选：填充种子数据
    if [ -f "prisma/seed.ts" ]; then
        echo -e "${YELLOW}🌱 填充种子数据...${NC}"
        npx prisma db seed
    fi
    
    cd ../..
    
    echo -e "${GREEN}✅ Prisma 迁移完成${NC}"
}

# 创建 InfluxDB 配置
setup_influxdb() {
    echo -e "${BLUE}🔧 配置 InfluxDB...${NC}"
    
    # 等待 InfluxDB 完全启动
    sleep 5
    
    # 检查是否已经初始化
    if curl -f http://localhost:8086/api/v2/setup > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️ InfluxDB 已经初始化${NC}"
    else
        echo -e "${GREEN}✅ InfluxDB 配置完成${NC}"
    fi
}

# 显示连接信息
show_connection_info() {
    echo -e "${GREEN}🎉 数据库设置完成！${NC}"
    echo ""
    echo -e "${BLUE}📊 数据库连接信息：${NC}"
    echo ""
    echo -e "${YELLOW}PostgreSQL:${NC}"
    echo "  Host: localhost"
    echo "  Port: 5432"
    echo "  Database: sfquant"
    echo "  Username: sfquant"
    echo "  Password: sfquant_password_2024"
    echo "  URL: postgresql://sfquant:sfquant_password_2024@localhost:5432/sfquant"
    echo ""
    echo -e "${YELLOW}Redis:${NC}"
    echo "  Host: localhost"
    echo "  Port: 6379"
    echo "  URL: redis://localhost:6379"
    echo ""
    echo -e "${YELLOW}InfluxDB:${NC}"
    echo "  Host: localhost"
    echo "  Port: 8086"
    echo "  Organization: sfquant"
    echo "  Bucket: market_data"
    echo "  Token: sfquant-influxdb-token-2024"
    echo "  URL: http://localhost:8086"
    echo ""
    echo -e "${BLUE}🔧 管理命令：${NC}"
    echo "  查看服务状态: docker-compose ps"
    echo "  查看日志: docker-compose logs [service_name]"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart [service_name]"
    echo ""
    echo -e "${GREEN}✅ 现在可以启动 API Gateway 服务了！${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}🚀 SFQuant 数据库设置脚本${NC}"
    echo ""
    
    check_docker
    create_directories
    check_env_file
    start_databases
    check_database_connections
    setup_influxdb
    run_prisma_migration
    show_connection_info
    
    echo ""
    echo -e "${GREEN}🎉 设置完成！${NC}"
}

# 错误处理
trap 'echo -e "${RED}❌ 脚本执行失败${NC}"; exit 1' ERR

# 运行主函数
main "$@"
