#!/bin/bash

# SFQuant PWA 项目快速设置脚本 (纯JavaScript版本)
# 使用方法: ./scripts/setup-pwa-js-only.sh

set -e

echo "🚀 开始设置 SFQuant PWA 项目 (纯JavaScript版本)..."

# 检查必要工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装 $1"
        exit 1
    else
        echo "✅ $1 已安装"
    fi
}

echo "📋 检查必要工具..."
check_tool "node"
check_tool "npm"
check_tool "pnpm"

# 检查Node.js版本
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 版本需要 >= 18，当前版本: $(node -v)"
    exit 1
else
    echo "✅ Node.js 版本符合要求: $(node -v)"
fi

# 安装根目录依赖
echo "📦 安装根目录依赖..."
pnpm install

# 设置前端项目
echo "🎨 设置前端 PWA 项目..."
cd apps/frontend

# 创建基础目录结构
mkdir -p src/{components,hooks,services,stores,types,utils,workers}
mkdir -p src/components/{common,trading,strategy,charts}
mkdir -p src/services/{indicators,calculations}
mkdir -p public/{icons,workers}

# 创建基础文件
echo "📄 创建基础文件..."

# 创建主入口文件
cat > src/main.tsx << 'EOF'
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// 注册Service Worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
EOF

# 创建App组件
cat > src/App.tsx << 'EOF'
import React, { useState, useEffect } from 'react';
import { ConfigProvider, Card, Row, Col, Statistic, Button, message } from 'antd';
import { RocketOutlined, CalculatorOutlined, WifiOutlined } from '@ant-design/icons';
import zhCN from 'antd/locale/zh_CN';
import { TechnicalIndicators } from './services/indicators/TechnicalIndicators';
import './App.css';

function App() {
  const [indicators, setIndicators] = useState<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  // 示例价格数据
  const samplePrices = [
    50000, 51000, 49000, 52000, 48000, 53000, 47000, 54000, 46000, 55000,
    45000, 56000, 44000, 57000, 43000, 58000, 42000, 59000, 41000, 60000
  ];

  const calculateIndicators = async () => {
    setIsCalculating(true);
    try {
      const calculator = new TechnicalIndicators();
      
      const startTime = performance.now();
      
      const results = {
        sma: calculator.sma(samplePrices, 5),
        ema: calculator.ema(samplePrices, 5),
        rsi: calculator.rsi(samplePrices, 14),
        macd: calculator.macd(samplePrices, 12, 26, 9)
      };
      
      const endTime = performance.now();
      const calculationTime = endTime - startTime;
      
      setIndicators({
        ...results,
        calculationTime: calculationTime.toFixed(2)
      });
      
      message.success(`技术指标计算完成！耗时: ${calculationTime.toFixed(2)}ms`);
    } catch (error) {
      message.error('计算失败');
      console.error(error);
    } finally {
      setIsCalculating(false);
    }
  };

  useEffect(() => {
    // 自动计算一次
    calculateIndicators();
  }, []);

  return (
    <ConfigProvider locale={zhCN}>
      <div className="App">
        <header className="App-header">
          <h1><RocketOutlined /> SFQuant PWA</h1>
          <p>智能量化交易平台 (纯JavaScript版本)</p>
          <p>✅ PWA功能已启用 | 📱 可安装到桌面 | 📴 支持离线使用</p>
        </header>
        
        <div className="App-content">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="🧮 技术指标计算演示" extra={
                <Button 
                  type="primary" 
                  icon={<CalculatorOutlined />}
                  loading={isCalculating}
                  onClick={calculateIndicators}
                >
                  重新计算
                </Button>
              }>
                {indicators && (
                  <Row gutter={[16, 16]}>
                    <Col span={6}>
                      <Statistic 
                        title="SMA (5期)" 
                        value={indicators.sma[indicators.sma.length - 1]?.toFixed(2) || 0}
                        prefix="$"
                      />
                    </Col>
                    <Col span={6}>
                      <Statistic 
                        title="EMA (5期)" 
                        value={indicators.ema[indicators.ema.length - 1]?.toFixed(2) || 0}
                        prefix="$"
                      />
                    </Col>
                    <Col span={6}>
                      <Statistic 
                        title="RSI (14期)" 
                        value={indicators.rsi[indicators.rsi.length - 1]?.toFixed(2) || 0}
                      />
                    </Col>
                    <Col span={6}>
                      <Statistic 
                        title="计算耗时" 
                        value={indicators.calculationTime}
                        suffix="ms"
                      />
                    </Col>
                  </Row>
                )}
              </Card>
            </Col>
            
            <Col span={12}>
              <Card title="📱 PWA 功能">
                <ul style={{ textAlign: 'left' }}>
                  <li>✅ Service Worker 缓存</li>
                  <li>✅ 离线访问支持</li>
                  <li>✅ 应用安装提示</li>
                  <li>✅ 自动更新机制</li>
                  <li>✅ 推送通知支持</li>
                </ul>
              </Card>
            </Col>
            
            <Col span={12}>
              <Card title="⚡ 性能特性">
                <ul style={{ textAlign: 'left' }}>
                  <li>🧮 JavaScript 技术指标计算</li>
                  <li>🔄 Web Workers 多线程处理</li>
                  <li>💾 IndexedDB 本地存储</li>
                  <li>📡 WebSocket 实时数据</li>
                  <li>🎯 React Query 状态管理</li>
                </ul>
              </Card>
            </Col>
          </Row>
        </div>
      </div>
    </ConfigProvider>
  );
}

export default App;
EOF

# 创建技术指标计算服务 (JavaScript版本)
cat > src/services/indicators/TechnicalIndicators.ts << 'EOF'
/**
 * 技术指标计算类 (JavaScript版本)
 * 提供常用的技术分析指标计算
 */
export class TechnicalIndicators {
  
  /**
   * 简单移动平均线 (SMA)
   * @param prices 价格数组
   * @param period 周期
   */
  sma(prices: number[], period: number): number[] {
    if (prices.length < period) {
      return [];
    }
    
    const result: number[] = [];
    
    for (let i = period - 1; i < prices.length; i++) {
      const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push(sum / period);
    }
    
    return result;
  }
  
  /**
   * 指数移动平均线 (EMA)
   * @param prices 价格数组
   * @param period 周期
   */
  ema(prices: number[], period: number): number[] {
    if (prices.length === 0) {
      return [];
    }
    
    const result: number[] = [];
    const multiplier = 2 / (period + 1);
    
    result.push(prices[0]);
    
    for (let i = 1; i < prices.length; i++) {
      const ema = (prices[i] * multiplier) + (result[i - 1] * (1 - multiplier));
      result.push(ema);
    }
    
    return result;
  }
  
  /**
   * RSI相对强弱指数
   * @param prices 价格数组
   * @param period 周期
   */
  rsi(prices: number[], period: number): number[] {
    if (prices.length < period + 1) {
      return [];
    }
    
    const gains: number[] = [];
    const losses: number[] = [];
    
    // 计算价格变化
    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? -change : 0);
    }
    
    const avgGains = this.sma(gains, period);
    const avgLosses = this.sma(losses, period);
    
    const rsiValues: number[] = [];
    for (let i = 0; i < avgGains.length; i++) {
      if (avgLosses[i] === 0) {
        rsiValues.push(100);
      } else {
        const rs = avgGains[i] / avgLosses[i];
        const rsi = 100 - (100 / (1 + rs));
        rsiValues.push(rsi);
      }
    }
    
    return rsiValues;
  }
  
  /**
   * MACD指标
   * @param prices 价格数组
   * @param fastPeriod 快线周期
   * @param slowPeriod 慢线周期
   * @param signalPeriod 信号线周期
   */
  macd(prices: number[], fastPeriod: number, slowPeriod: number, signalPeriod: number): number[] {
    const emaFast = this.ema(prices, fastPeriod);
    const emaSlow = this.ema(prices, slowPeriod);
    
    const macdLine: number[] = [];
    const minLength = Math.min(emaFast.length, emaSlow.length);
    
    for (let i = 0; i < minLength; i++) {
      macdLine.push(emaFast[i] - emaSlow[i]);
    }
    
    return this.ema(macdLine, signalPeriod);
  }
  
  /**
   * 布林带
   * @param prices 价格数组
   * @param period 周期
   * @param stdDev 标准差倍数
   */
  bollingerBands(prices: number[], period: number, stdDev: number = 2): {
    upper: number[];
    middle: number[];
    lower: number[];
  } {
    const sma = this.sma(prices, period);
    const upper: number[] = [];
    const lower: number[] = [];
    
    for (let i = 0; i < sma.length; i++) {
      const startIdx = i;
      const endIdx = i + period;
      
      if (endIdx <= prices.length) {
        const slice = prices.slice(startIdx, endIdx);
        const mean = sma[i];
        
        // 计算标准差
        const variance = slice.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / period;
        const std = Math.sqrt(variance);
        
        upper.push(mean + (stdDev * std));
        lower.push(mean - (stdDev * std));
      }
    }
    
    return {
      upper,
      middle: sma,
      lower
    };
  }
  
  /**
   * 套利机会检测
   * @param exchange1Prices 交易所1价格
   * @param exchange2Prices 交易所2价格
   * @param threshold 阈值
   */
  detectArbitrage(exchange1Prices: number[], exchange2Prices: number[], threshold: number): boolean[] {
    const opportunities: boolean[] = [];
    const minLength = Math.min(exchange1Prices.length, exchange2Prices.length);
    
    for (let i = 0; i < minLength; i++) {
      const priceDiff = Math.abs(exchange1Prices[i] - exchange2Prices[i]);
      const minPrice = Math.min(exchange1Prices[i], exchange2Prices[i]);
      
      if (minPrice > 0) {
        const percentageDiff = priceDiff / minPrice;
        opportunities.push(percentageDiff > threshold);
      } else {
        opportunities.push(false);
      }
    }
    
    return opportunities;
  }
  
  /**
   * 计算夏普比率
   * @param returns 收益率数组
   * @param riskFreeRate 无风险利率
   */
  calculateSharpeRatio(returns: number[], riskFreeRate: number = 0): number {
    if (returns.length === 0) {
      return 0;
    }
    
    const meanReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const excessReturn = meanReturn - riskFreeRate;
    
    if (returns.length < 2) {
      return 0;
    }
    
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - meanReturn, 2), 0) / (returns.length - 1);
    const stdDev = Math.sqrt(variance);
    
    return stdDev === 0 ? 0 : excessReturn / stdDev;
  }
  
  /**
   * 计算最大回撤
   * @param prices 价格数组
   */
  calculateMaxDrawdown(prices: number[]): number {
    if (prices.length < 2) {
      return 0;
    }
    
    let maxDrawdown = 0;
    let peak = prices[0];
    
    for (let i = 1; i < prices.length; i++) {
      if (prices[i] > peak) {
        peak = prices[i];
      }
      
      const drawdown = (peak - prices[i]) / peak;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }
    
    return maxDrawdown;
  }
}
EOF

# 创建CSS文件
cat > src/index.css << 'EOF'
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

#root {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}
EOF

cat > src/App.css << 'EOF'
.App {
  text-align: center;
}

.App-header {
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  padding: 30px;
  color: white;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.App-header h1 {
  margin: 0 0 16px 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.App-header p {
  margin: 8px 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.App-content {
  max-width: 1000px;
  margin: 0 auto;
}

.ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ant-statistic-title {
  font-weight: 600;
}

.ant-statistic-content {
  color: #1890ff;
}
EOF

# 安装前端依赖
echo "📦 安装前端依赖..."
pnpm install

cd ../..

# 创建开发脚本
echo "📝 创建开发脚本..."
cat > scripts/dev-js.sh << 'EOF'
#!/bin/bash

echo "🚀 启动 SFQuant PWA 开发环境 (纯JavaScript版本)..."

# 启动前端PWA
echo "🎨 启动前端 PWA..."
cd apps/frontend
pnpm dev &
FRONTEND_PID=$!

# 等待用户中断
echo "✅ 开发环境已启动！"
echo "📱 前端地址: http://localhost:3001"
echo "🔧 PWA功能: 可安装、离线访问、自动更新"
echo ""
echo "按 Ctrl+C 停止服务..."

# 捕获中断信号
trap 'echo "🛑 停止服务..."; kill $FRONTEND_PID 2>/dev/null; exit 0' INT

# 等待
wait
EOF

chmod +x scripts/dev-js.sh

echo ""
echo "🎉 SFQuant PWA 项目设置完成！(纯JavaScript版本)"
echo ""
echo "📋 下一步操作:"
echo "1. 运行 'pnpm install' 安装依赖"
echo "2. 运行 './scripts/dev-js.sh' 启动开发环境"
echo "3. 访问 http://localhost:3001 查看PWA应用"
echo ""
echo "✨ 功能特性:"
echo "- 📱 PWA功能完整 (可安装、离线、自动更新)"
echo "- 🧮 JavaScript技术指标计算"
echo "- ⚡ 高性能优化 (代码分割、缓存)"
echo "- 🎨 现代化UI (Ant Design)"
echo ""
echo "🔧 有用的命令:"
echo "- ./scripts/dev-js.sh    # 启动开发环境"
echo "- pnpm build             # 构建生产版本"
echo "- pnpm pwa:audit         # PWA审计"
echo ""
echo "💡 提示: 如果后续需要更高性能，可以再添加Rust WASM模块"
echo "✨ 开始你的PWA量化交易平台开发之旅吧！"
