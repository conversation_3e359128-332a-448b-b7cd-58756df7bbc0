#!/bin/bash

# SFQuant 系统停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🛑 停止 SFQuant 系统...${NC}"

# 停止应用进程
stop_applications() {
    echo -e "${YELLOW}停止应用进程...${NC}"
    
    # 停止 API Gateway
    if [ -f .api_pid ]; then
        API_PID=$(cat .api_pid)
        if kill -0 $API_PID 2>/dev/null; then
            echo -e "${YELLOW}停止 API Gateway (PID: $API_PID)...${NC}"
            kill $API_PID
            wait $API_PID 2>/dev/null || true
        fi
        rm .api_pid
    fi
    
    # 停止前端应用
    if [ -f .frontend_pid ]; then
        FRONTEND_PID=$(cat .frontend_pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            echo -e "${YELLOW}停止前端应用 (PID: $FRONTEND_PID)...${NC}"
            kill $FRONTEND_PID
            wait $FRONTEND_PID 2>/dev/null || true
        fi
        rm .frontend_pid
    fi
    
    # 查找并停止其他相关进程
    pkill -f "pnpm dev" 2>/dev/null || true
    pkill -f "node.*api-gateway" 2>/dev/null || true
    pkill -f "vite.*frontend" 2>/dev/null || true
    
    echo -e "${GREEN}✅ 应用进程已停止${NC}"
}

# 停止 Docker 服务
stop_docker_services() {
    echo -e "${YELLOW}停止 Docker 服务...${NC}"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose down
        echo -e "${GREEN}✅ Docker 服务已停止${NC}"
    else
        echo -e "${YELLOW}⚠️ Docker Compose 未找到，跳过${NC}"
    fi
}

# 清理临时文件
cleanup_temp_files() {
    echo -e "${YELLOW}清理临时文件...${NC}"
    
    # 清理 PID 文件
    rm -f .api_pid .frontend_pid
    
    # 清理日志文件（可选）
    # rm -rf logs/*.log
    
    echo -e "${GREEN}✅ 临时文件已清理${NC}"
}

# 显示停止信息
show_stop_info() {
    echo ""
    echo -e "${GREEN}🎉 SFQuant 系统已完全停止${NC}"
    echo ""
    echo -e "${BLUE}💡 重新启动系统：${NC}"
    echo "  ./scripts/start-sfquant.sh"
    echo ""
    echo -e "${BLUE}💡 仅启动数据库：${NC}"
    echo "  docker-compose up -d postgres redis influxdb"
    echo ""
    echo -e "${BLUE}💡 查看数据库状态：${NC}"
    echo "  docker-compose ps"
    echo ""
}

# 主函数
main() {
    stop_applications
    stop_docker_services
    cleanup_temp_files
    show_stop_info
}

# 运行主函数
main "$@"
