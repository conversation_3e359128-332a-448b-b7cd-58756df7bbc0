#!/bin/bash

# SFQuant 系统启动脚本
# 此脚本将启动完整的 SFQuant 系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示 Logo
show_logo() {
    echo -e "${CYAN}"
    cat << "EOF"
   _____ ______ ____                        __ 
  / ___// ____/ __ \__  ______ _____  _____/ /_
  \__ \/ /_  / / / / / / / __ `/ __ \/ ___/ __/
 ___/ / __/ / /_/ / /_/ / /_/ / / / / /__/ /_  
/____/_/    \___\_\__,_/\__,_/_/ /_/\___/\__/  
                                              
Smart Finance Quantitative Trading Platform
EOF
    echo -e "${NC}"
    echo -e "${BLUE}🚀 欢迎使用 SFQuant 量化交易平台${NC}"
    echo ""
}

# 检查系统要求
check_requirements() {
    echo -e "${BLUE}🔍 检查系统要求...${NC}"
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js 18+${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo -e "${RED}❌ Node.js 版本过低，需要 18+，当前版本: $(node -v)${NC}"
        exit 1
    fi
    
    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        echo -e "${YELLOW}⚠️ pnpm 未安装，正在安装...${NC}"
        npm install -g pnpm
    fi
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker 未安装，请先安装 Docker${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose 未安装，请先安装 Docker Compose${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 系统要求检查通过${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}📦 安装项目依赖...${NC}"
    
    # 安装根目录依赖
    echo -e "${YELLOW}安装根目录依赖...${NC}"
    pnpm install
    
    # 安装 API Gateway 依赖
    echo -e "${YELLOW}安装 API Gateway 依赖...${NC}"
    cd apps/api-gateway
    pnpm install
    cd ../..
    
    # 安装前端依赖
    echo -e "${YELLOW}安装前端依赖...${NC}"
    cd apps/frontend
    pnpm install
    cd ../..
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 设置环境变量
setup_environment() {
    echo -e "${BLUE}⚙️ 设置环境变量...${NC}"
    
    # API Gateway 环境变量
    if [ ! -f "apps/api-gateway/.env" ]; then
        echo -e "${YELLOW}复制 API Gateway 环境变量模板...${NC}"
        cp apps/api-gateway/.env.example apps/api-gateway/.env
    fi
    
    # 前端环境变量
    if [ ! -f "apps/frontend/.env" ]; then
        echo -e "${YELLOW}创建前端环境变量文件...${NC}"
        cat > apps/frontend/.env << EOF
VITE_API_URL=http://localhost:8080
VITE_WS_URL=ws://localhost:8080
VITE_APP_NAME=SFQuant
VITE_APP_VERSION=1.0.0
EOF
    fi
    
    echo -e "${GREEN}✅ 环境变量设置完成${NC}"
}

# 启动数据库
start_databases() {
    echo -e "${BLUE}🗄️ 启动数据库服务...${NC}"
    
    # 启动数据库服务
    docker-compose up -d postgres redis influxdb
    
    echo -e "${YELLOW}⏳ 等待数据库服务启动...${NC}"
    sleep 15
    
    # 检查数据库连接
    echo -e "${YELLOW}检查数据库连接...${NC}"
    
    # 检查 PostgreSQL
    for i in {1..30}; do
        if docker-compose exec -T postgres pg_isready -U sfquant -d sfquant > /dev/null 2>&1; then
            echo -e "${GREEN}✅ PostgreSQL 连接成功${NC}"
            break
        fi
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ PostgreSQL 连接失败${NC}"
            exit 1
        fi
        sleep 2
    done
    
    # 检查 Redis
    for i in {1..30}; do
        if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Redis 连接成功${NC}"
            break
        fi
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ Redis 连接失败${NC}"
            exit 1
        fi
        sleep 2
    done
    
    echo -e "${GREEN}✅ 数据库服务启动成功${NC}"
}

# 运行数据库迁移
run_migrations() {
    echo -e "${BLUE}🔄 运行数据库迁移...${NC}"
    
    cd apps/api-gateway
    
    # 生成 Prisma 客户端
    echo -e "${YELLOW}生成 Prisma 客户端...${NC}"
    npx prisma generate
    
    # 运行数据库迁移
    echo -e "${YELLOW}运行数据库迁移...${NC}"
    npx prisma db push
    
    cd ../..
    
    echo -e "${GREEN}✅ 数据库迁移完成${NC}"
}

# 构建项目
build_project() {
    echo -e "${BLUE}🔨 构建项目...${NC}"
    
    # 构建 API Gateway
    echo -e "${YELLOW}构建 API Gateway...${NC}"
    cd apps/api-gateway
    pnpm build
    cd ../..
    
    # 构建前端（开发模式不需要构建）
    echo -e "${YELLOW}前端将在开发模式下运行${NC}"
    
    echo -e "${GREEN}✅ 项目构建完成${NC}"
}

# 启动服务
start_services() {
    echo -e "${BLUE}🚀 启动应用服务...${NC}"
    
    # 启动 API Gateway
    echo -e "${YELLOW}启动 API Gateway...${NC}"
    cd apps/api-gateway
    pnpm dev &
    API_PID=$!
    cd ../..
    
    # 等待 API Gateway 启动
    echo -e "${YELLOW}等待 API Gateway 启动...${NC}"
    sleep 10
    
    # 检查 API Gateway 健康状态
    for i in {1..30}; do
        if curl -f http://localhost:8080/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ API Gateway 启动成功${NC}"
            break
        fi
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ API Gateway 启动失败${NC}"
            kill $API_PID 2>/dev/null || true
            exit 1
        fi
        sleep 2
    done
    
    # 启动前端
    echo -e "${YELLOW}启动前端应用...${NC}"
    cd apps/frontend
    pnpm dev &
    FRONTEND_PID=$!
    cd ../..
    
    # 等待前端启动
    echo -e "${YELLOW}等待前端应用启动...${NC}"
    sleep 10
    
    # 检查前端健康状态
    for i in {1..30}; do
        if curl -f http://localhost:5173 > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 前端应用启动成功${NC}"
            break
        fi
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ 前端应用启动失败${NC}"
            kill $API_PID $FRONTEND_PID 2>/dev/null || true
            exit 1
        fi
        sleep 2
    done
    
    # 保存进程 ID
    echo $API_PID > .api_pid
    echo $FRONTEND_PID > .frontend_pid
}

# 显示启动信息
show_startup_info() {
    echo ""
    echo -e "${GREEN}🎉 SFQuant 系统启动成功！${NC}"
    echo ""
    echo -e "${CYAN}📊 服务访问地址：${NC}"
    echo -e "${YELLOW}前端应用:${NC} http://localhost:5173"
    echo -e "${YELLOW}API 服务:${NC} http://localhost:8080"
    echo -e "${YELLOW}API 文档:${NC} http://localhost:8080/documentation"
    echo -e "${YELLOW}健康检查:${NC} http://localhost:8080/health"
    echo ""
    echo -e "${CYAN}🗄️ 数据库连接：${NC}"
    echo -e "${YELLOW}PostgreSQL:${NC} postgresql://sfquant:sfquant_password_2024@localhost:5432/sfquant"
    echo -e "${YELLOW}Redis:${NC} redis://localhost:6379"
    echo -e "${YELLOW}InfluxDB:${NC} http://localhost:8086"
    echo ""
    echo -e "${CYAN}🔧 管理命令：${NC}"
    echo -e "${YELLOW}停止系统:${NC} ./scripts/stop-sfquant.sh"
    echo -e "${YELLOW}查看日志:${NC} docker-compose logs -f"
    echo -e "${YELLOW}重启数据库:${NC} docker-compose restart postgres redis influxdb"
    echo ""
    echo -e "${BLUE}💡 提示：${NC}"
    echo "1. 首次使用请访问前端应用注册账户"
    echo "2. 在策略管理页面配置您的交易所 API 密钥"
    echo "3. 创建并启动您的第一个量化策略"
    echo ""
    echo -e "${GREEN}✨ 祝您交易愉快！${NC}"
}

# 清理函数
cleanup() {
    echo -e "${YELLOW}🧹 清理进程...${NC}"
    
    if [ -f .api_pid ]; then
        API_PID=$(cat .api_pid)
        kill $API_PID 2>/dev/null || true
        rm .api_pid
    fi
    
    if [ -f .frontend_pid ]; then
        FRONTEND_PID=$(cat .frontend_pid)
        kill $FRONTEND_PID 2>/dev/null || true
        rm .frontend_pid
    fi
}

# 主函数
main() {
    show_logo
    check_requirements
    install_dependencies
    setup_environment
    start_databases
    run_migrations
    build_project
    start_services
    show_startup_info
    
    # 等待用户中断
    echo -e "${BLUE}按 Ctrl+C 停止系统${NC}"
    trap cleanup EXIT
    
    # 保持脚本运行
    while true; do
        sleep 1
    done
}

# 错误处理
trap 'echo -e "${RED}❌ 启动失败${NC}"; cleanup; exit 1' ERR

# 运行主函数
main "$@"
