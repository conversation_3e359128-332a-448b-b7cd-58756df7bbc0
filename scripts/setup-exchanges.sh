#!/bin/bash

# SFQuant 交易所配置脚本
# 此脚本帮助配置交易所 API 密钥（测试网）

echo "🔧 SFQuant 交易所配置向导"
echo "================================"
echo ""
echo "⚠️  注意：此脚本将配置测试网 API 密钥"
echo "📝 请确保您已经在各个交易所创建了测试网 API 密钥"
echo ""

# 检查是否存在 .env 文件
ENV_FILE="apps/api-gateway/.env"

if [ ! -f "$ENV_FILE" ]; then
    echo "❌ 未找到 .env 文件: $ENV_FILE"
    echo "请先复制 .env.example 到 .env"
    exit 1
fi

echo "📋 当前支持的交易所："
echo "1. Binance (币安)"
echo "2. OKX (欧易)"
echo "3. Bybit"
echo "4. <PERSON><PERSON><PERSON> (火币)"
echo ""

# 函数：更新环境变量
update_env_var() {
    local key=$1
    local value=$2
    local file=$3
    
    if grep -q "^${key}=" "$file"; then
        # 如果变量存在，更新它
        sed -i.bak "s/^${key}=.*/${key}=${value}/" "$file"
    else
        # 如果变量不存在，添加它
        echo "${key}=${value}" >> "$file"
    fi
}

# 配置 Binance
echo "🟡 配置 Binance 测试网"
echo "📖 获取测试网 API: https://testnet.binance.vision/"
read -p "请输入 Binance API Key (留空跳过): " BINANCE_API_KEY
if [ ! -z "$BINANCE_API_KEY" ]; then
    read -p "请输入 Binance Secret Key: " BINANCE_SECRET_KEY
    update_env_var "BINANCE_API_KEY" "$BINANCE_API_KEY" "$ENV_FILE"
    update_env_var "BINANCE_SECRET_KEY" "$BINANCE_SECRET_KEY" "$ENV_FILE"
    update_env_var "BINANCE_SANDBOX" "true" "$ENV_FILE"
    echo "✅ Binance 配置完成"
else
    echo "⏭️  跳过 Binance 配置"
fi
echo ""

# 配置 OKX
echo "🟠 配置 OKX 测试网"
echo "📖 获取测试网 API: https://www.okx.com/docs-v5/en/#overview-demo-trading"
read -p "请输入 OKX API Key (留空跳过): " OKX_API_KEY
if [ ! -z "$OKX_API_KEY" ]; then
    read -p "请输入 OKX Secret Key: " OKX_SECRET_KEY
    read -p "请输入 OKX Passphrase: " OKX_PASSPHRASE
    update_env_var "OKX_API_KEY" "$OKX_API_KEY" "$ENV_FILE"
    update_env_var "OKX_SECRET_KEY" "$OKX_SECRET_KEY" "$ENV_FILE"
    update_env_var "OKX_PASSPHRASE" "$OKX_PASSPHRASE" "$ENV_FILE"
    update_env_var "OKX_SANDBOX" "true" "$ENV_FILE"
    echo "✅ OKX 配置完成"
else
    echo "⏭️  跳过 OKX 配置"
fi
echo ""

# 配置 Bybit
echo "🟣 配置 Bybit 测试网"
echo "📖 获取测试网 API: https://testnet.bybit.com/"
read -p "请输入 Bybit API Key (留空跳过): " BYBIT_API_KEY
if [ ! -z "$BYBIT_API_KEY" ]; then
    read -p "请输入 Bybit Secret Key: " BYBIT_SECRET_KEY
    update_env_var "BYBIT_API_KEY" "$BYBIT_API_KEY" "$ENV_FILE"
    update_env_var "BYBIT_SECRET_KEY" "$BYBIT_SECRET_KEY" "$ENV_FILE"
    update_env_var "BYBIT_SANDBOX" "true" "$ENV_FILE"
    echo "✅ Bybit 配置完成"
else
    echo "⏭️  跳过 Bybit 配置"
fi
echo ""

# 配置 Huobi
echo "🔴 配置 Huobi 测试网"
echo "📖 获取测试网 API: https://huobiapi.github.io/docs/spot/v1/en/#introduction"
read -p "请输入 Huobi API Key (留空跳过): " HUOBI_API_KEY
if [ ! -z "$HUOBI_API_KEY" ]; then
    read -p "请输入 Huobi Secret Key: " HUOBI_SECRET_KEY
    update_env_var "HUOBI_API_KEY" "$HUOBI_API_KEY" "$ENV_FILE"
    update_env_var "HUOBI_SECRET_KEY" "$HUOBI_SECRET_KEY" "$ENV_FILE"
    update_env_var "HUOBI_SANDBOX" "true" "$ENV_FILE"
    echo "✅ Huobi 配置完成"
else
    echo "⏭️  跳过 Huobi 配置"
fi
echo ""

# 清理备份文件
rm -f "${ENV_FILE}.bak"

echo "🎉 交易所配置完成！"
echo ""
echo "📋 配置摘要："
echo "- 配置文件: $ENV_FILE"
echo "- 所有交易所都设置为测试网模式"
echo ""
echo "🔄 重启 API Gateway 以应用新配置："
echo "cd apps/api-gateway && pnpm dev"
echo ""
echo "🔍 验证配置："
echo "curl http://localhost:8080/api/v1/exchanges/status"
echo ""
echo "⚠️  安全提醒："
echo "- 请勿在生产环境中使用测试网 API 密钥"
echo "- 请勿将 API 密钥提交到版本控制系统"
echo "- 定期轮换您的 API 密钥"
