#!/bin/bash

# SFQuant 交易所配置脚本
# 此脚本帮助用户配置交易所 API 密钥

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示标题
show_title() {
    echo -e "${CYAN}"
    echo "=================================================="
    echo "        SFQuant 交易所 API 配置向导"
    echo "=================================================="
    echo -e "${NC}"
    echo -e "${YELLOW}⚠️ 重要提示：${NC}"
    echo "1. API 密钥将存储在本地环境变量文件中"
    echo "2. 请确保您的 API 密钥具有适当的权限"
    echo "3. 建议在测试环境中先使用沙盒模式"
    echo "4. 请妥善保管您的 API 密钥，不要泄露给他人"
    echo ""
}

# 读取用户输入
read_input() {
    local prompt="$1"
    local var_name="$2"
    local is_secret="${3:-false}"
    
    if [ "$is_secret" = "true" ]; then
        echo -n -e "${BLUE}$prompt: ${NC}"
        read -s value
        echo ""
    else
        echo -n -e "${BLUE}$prompt: ${NC}"
        read value
    fi
    
    eval "$var_name='$value'"
}

# 确认输入
confirm_input() {
    local prompt="$1"
    echo -n -e "${YELLOW}$prompt (y/N): ${NC}"
    read confirm
    case $confirm in
        [Yy]* ) return 0;;
        * ) return 1;;
    esac
}

# 配置币安
configure_binance() {
    echo -e "${PURPLE}🔧 配置币安 (Binance)${NC}"
    echo ""
    
    if confirm_input "是否配置币安 API"; then
        read_input "API Key" BINANCE_API_KEY true
        read_input "Secret Key" BINANCE_SECRET_KEY true
        
        echo ""
        echo -e "${YELLOW}选择模式：${NC}"
        echo "1. 沙盒模式 (测试)"
        echo "2. 生产模式 (真实交易)"
        echo -n -e "${BLUE}请选择 (1-2): ${NC}"
        read mode_choice
        
        case $mode_choice in
            1)
                BINANCE_SANDBOX="true"
                echo -e "${GREEN}✅ 已选择沙盒模式${NC}"
                ;;
            2)
                BINANCE_SANDBOX="false"
                echo -e "${RED}⚠️ 已选择生产模式，请谨慎操作${NC}"
                ;;
            *)
                BINANCE_SANDBOX="true"
                echo -e "${YELLOW}默认选择沙盒模式${NC}"
                ;;
        esac
        
        # 更新环境变量
        update_env_var "BINANCE_API_KEY" "$BINANCE_API_KEY"
        update_env_var "BINANCE_SECRET_KEY" "$BINANCE_SECRET_KEY"
        update_env_var "BINANCE_SANDBOX" "$BINANCE_SANDBOX"
        
        echo -e "${GREEN}✅ 币安配置完成${NC}"
    else
        echo -e "${YELLOW}⏭️ 跳过币安配置${NC}"
    fi
    echo ""
}

# 配置 OKX
configure_okx() {
    echo -e "${PURPLE}🔧 配置 OKX${NC}"
    echo ""
    
    if confirm_input "是否配置 OKX API"; then
        read_input "API Key" OKX_API_KEY true
        read_input "Secret Key" OKX_SECRET_KEY true
        read_input "Passphrase" OKX_PASSPHRASE true
        
        echo ""
        echo -e "${YELLOW}选择模式：${NC}"
        echo "1. 沙盒模式 (测试)"
        echo "2. 生产模式 (真实交易)"
        echo -n -e "${BLUE}请选择 (1-2): ${NC}"
        read mode_choice
        
        case $mode_choice in
            1)
                OKX_SANDBOX="true"
                echo -e "${GREEN}✅ 已选择沙盒模式${NC}"
                ;;
            2)
                OKX_SANDBOX="false"
                echo -e "${RED}⚠️ 已选择生产模式，请谨慎操作${NC}"
                ;;
            *)
                OKX_SANDBOX="true"
                echo -e "${YELLOW}默认选择沙盒模式${NC}"
                ;;
        esac
        
        # 更新环境变量
        update_env_var "OKX_API_KEY" "$OKX_API_KEY"
        update_env_var "OKX_SECRET_KEY" "$OKX_SECRET_KEY"
        update_env_var "OKX_PASSPHRASE" "$OKX_PASSPHRASE"
        update_env_var "OKX_SANDBOX" "$OKX_SANDBOX"
        
        echo -e "${GREEN}✅ OKX 配置完成${NC}"
    else
        echo -e "${YELLOW}⏭️ 跳过 OKX 配置${NC}"
    fi
    echo ""
}

# 配置 Bybit
configure_bybit() {
    echo -e "${PURPLE}🔧 配置 Bybit${NC}"
    echo ""
    
    if confirm_input "是否配置 Bybit API"; then
        read_input "API Key" BYBIT_API_KEY true
        read_input "Secret Key" BYBIT_SECRET_KEY true
        
        echo ""
        echo -e "${YELLOW}选择模式：${NC}"
        echo "1. 沙盒模式 (测试)"
        echo "2. 生产模式 (真实交易)"
        echo -n -e "${BLUE}请选择 (1-2): ${NC}"
        read mode_choice
        
        case $mode_choice in
            1)
                BYBIT_SANDBOX="true"
                echo -e "${GREEN}✅ 已选择沙盒模式${NC}"
                ;;
            2)
                BYBIT_SANDBOX="false"
                echo -e "${RED}⚠️ 已选择生产模式，请谨慎操作${NC}"
                ;;
            *)
                BYBIT_SANDBOX="true"
                echo -e "${YELLOW}默认选择沙盒模式${NC}"
                ;;
        esac
        
        # 更新环境变量
        update_env_var "BYBIT_API_KEY" "$BYBIT_API_KEY"
        update_env_var "BYBIT_SECRET_KEY" "$BYBIT_SECRET_KEY"
        update_env_var "BYBIT_SANDBOX" "$BYBIT_SANDBOX"
        
        echo -e "${GREEN}✅ Bybit 配置完成${NC}"
    else
        echo -e "${YELLOW}⏭️ 跳过 Bybit 配置${NC}"
    fi
    echo ""
}

# 更新环境变量
update_env_var() {
    local key="$1"
    local value="$2"
    local env_file="apps/api-gateway/.env"
    
    if [ -f "$env_file" ]; then
        # 如果变量已存在，则更新
        if grep -q "^$key=" "$env_file"; then
            sed -i.bak "s/^$key=.*/$key=$value/" "$env_file"
        else
            # 如果变量不存在，则添加
            echo "$key=$value" >> "$env_file"
        fi
    else
        echo -e "${RED}❌ 环境变量文件不存在: $env_file${NC}"
        exit 1
    fi
}

# 验证配置
verify_configuration() {
    echo -e "${BLUE}🔍 验证配置...${NC}"
    
    local env_file="apps/api-gateway/.env"
    
    if [ ! -f "$env_file" ]; then
        echo -e "${RED}❌ 环境变量文件不存在${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}已配置的交易所：${NC}"
    
    # 检查币安
    if grep -q "^BINANCE_API_KEY=" "$env_file" && [ "$(grep "^BINANCE_API_KEY=" "$env_file" | cut -d'=' -f2)" != "your-binance-api-key-here" ]; then
        local sandbox_mode=$(grep "^BINANCE_SANDBOX=" "$env_file" | cut -d'=' -f2)
        echo -e "  ✅ 币安 (${sandbox_mode:-true} 模式)"
    fi
    
    # 检查 OKX
    if grep -q "^OKX_API_KEY=" "$env_file" && [ "$(grep "^OKX_API_KEY=" "$env_file" | cut -d'=' -f2)" != "your-okx-api-key-here" ]; then
        local sandbox_mode=$(grep "^OKX_SANDBOX=" "$env_file" | cut -d'=' -f2)
        echo -e "  ✅ OKX (${sandbox_mode:-true} 模式)"
    fi
    
    # 检查 Bybit
    if grep -q "^BYBIT_API_KEY=" "$env_file" && [ "$(grep "^BYBIT_API_KEY=" "$env_file" | cut -d'=' -f2)" != "your-bybit-api-key-here" ]; then
        local sandbox_mode=$(grep "^BYBIT_SANDBOX=" "$env_file" | cut -d'=' -f2)
        echo -e "  ✅ Bybit (${sandbox_mode:-true} 模式)"
    fi
    
    echo ""
}

# 显示完成信息
show_completion_info() {
    echo -e "${GREEN}🎉 交易所配置完成！${NC}"
    echo ""
    echo -e "${BLUE}📝 下一步：${NC}"
    echo "1. 重启 SFQuant 系统以应用新配置"
    echo "2. 在前端界面中验证交易所连接状态"
    echo "3. 创建并测试您的第一个策略"
    echo ""
    echo -e "${YELLOW}💡 提示：${NC}"
    echo "- 可以随时运行此脚本来更新配置"
    echo "- 建议先在沙盒模式下测试策略"
    echo "- 确认策略正常工作后再切换到生产模式"
    echo ""
    echo -e "${RED}⚠️ 安全提醒：${NC}"
    echo "- 定期更换 API 密钥"
    echo "- 不要在公共场所或不安全的网络中配置"
    echo "- 备份您的配置文件"
    echo ""
}

# 主函数
main() {
    show_title
    
    # 检查环境变量文件
    if [ ! -f "apps/api-gateway/.env" ]; then
        echo -e "${YELLOW}⚠️ 环境变量文件不存在，从模板复制...${NC}"
        cp apps/api-gateway/.env.example apps/api-gateway/.env
    fi
    
    # 配置各个交易所
    configure_binance
    configure_okx
    configure_bybit
    
    # 验证配置
    verify_configuration
    
    # 显示完成信息
    show_completion_info
}

# 运行主函数
main "$@"
