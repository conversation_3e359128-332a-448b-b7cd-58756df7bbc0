{"name": "sfquant", "version": "0.1.0", "description": "Smart Finance Quantitative - 专注于加密货币的量化策略管理平台", "private": true, "workspaces": ["apps/*", "services/*", "packages/*"], "scripts": {"dev": "concurrently \"pnpm dev:frontend\" \"pnpm dev:api-gateway\"", "dev:frontend": "pnpm --filter frontend dev", "dev:api-gateway": "pnpm --filter api-gateway dev", "build": "pnpm -r build", "test": "pnpm -r test", "test:coverage": "pnpm -r test:coverage", "lint": "pnpm -r lint", "lint:fix": "pnpm -r lint:fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "clean": "pnpm -r clean && rm -rf node_modules", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "db:migrate": "pnpm --filter database migrate", "db:seed": "pnpm --filter database seed", "prepare": "husky install"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "lint-staged": "^13.2.3", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.6.12", "keywords": ["cryptocurrency", "quantitative-trading", "trading-bot", "defi", "arbitrage", "typescript", "react", "nodejs"], "author": "SFQuant Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/sfquant.git"}, "bugs": {"url": "https://github.com/your-org/sfquant/issues"}, "homepage": "https://github.com/your-org/sfquant#readme", "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}