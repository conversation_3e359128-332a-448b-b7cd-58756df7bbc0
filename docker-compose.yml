version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: sfquant-postgres
    environment:
      POSTGRES_DB: sfquant
      POSTGRES_USER: sfquant
      POSTGRES_PASSWORD: sfquant_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - sfquant-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sfquant -d sfquant"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis 缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: sfquant-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - sfquant-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # InfluxDB 时序数据库
  influxdb:
    image: influxdb:2.7-alpine
    container_name: sfquant-influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: sfquant
      DOCKER_INFLUXDB_INIT_PASSWORD: sfquant_password_2024
      DOCKER_INFLUXDB_INIT_ORG: sfquant
      DOCKER_INFLUXDB_INIT_BUCKET: market_data
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: sfquant-influxdb-token-2024
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    networks:
      - sfquant-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: apps/api-gateway/Dockerfile
    container_name: sfquant-api-gateway
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************************/sfquant
      - REDIS_URL=redis://redis:6379
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=sfquant-influxdb-token-2024
      - JWT_SECRET=sfquant-super-secret-jwt-key-2024-change-in-production
      - CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:5173
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      influxdb:
        condition: service_healthy
    networks:
      - sfquant-network
    volumes:
      - ./apps/api-gateway:/app
      - /app/node_modules
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend
  frontend:
    build:
      context: .
      dockerfile: apps/frontend/Dockerfile
    container_name: sfquant-frontend
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:8080
      - VITE_WS_URL=ws://localhost:8080
    depends_on:
      api-gateway:
        condition: service_healthy
    networks:
      - sfquant-network
    volumes:
      - ./apps/frontend:/app
      - /app/node_modules
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3

  # User Service
  user-service:
    build:
      context: .
      dockerfile: services/user/Dockerfile
    container_name: sfquant-user-service
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*******************************************/sfquant
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key
    depends_on:
      - postgres
      - redis
    networks:
      - sfquant-network

  # Trading Service
  trading-service:
    build:
      context: .
      dockerfile: services/trading/Dockerfile
    container_name: sfquant-trading-service
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*******************************************/sfquant
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - sfquant-network

  # Data Service
  data-service:
    build:
      context: .
      dockerfile: services/data/Dockerfile
    container_name: sfquant-data-service
    environment:
      - NODE_ENV=development
      - REDIS_URL=redis://redis:6379
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=your-influxdb-token
    depends_on:
      - redis
      - influxdb
    networks:
      - sfquant-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  influxdb_data:
    driver: local
  influxdb_config:
    driver: local

networks:
  sfquant-network:
    driver: bridge
