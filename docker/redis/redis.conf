# Redis 配置文件 - SFQuant 项目
# 基于 Redis 7.x 版本

# 网络配置
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300

# 通用配置
daemonize no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# 持久化配置
# RDB 快照
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF 持久化
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# 内存管理
maxmemory 256mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 安全配置
# requirepass your-redis-password-here
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command DEBUG ""

# 客户端配置
maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 延迟监控
latency-monitor-threshold 100

# 事件通知
notify-keyspace-events ""

# 高级配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重新哈希
activerehashing yes

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议最大批量请求大小
proto-max-bulk-len 512mb

# HZ 频率
hz 10

# 动态 HZ
dynamic-hz yes

# AOF 重写增量 fsync
aof-rewrite-incremental-fsync yes

# RDB 保存增量 fsync
rdb-save-incremental-fsync yes

# LFU 和 LRU 配置
lfu-log-factor 10
lfu-decay-time 1

# 模块配置
# loadmodule /path/to/module.so

# 集群配置（如果需要）
# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000
# cluster-announce-ip *************
# cluster-announce-port 6379
# cluster-announce-bus-port 16379

# 性能优化
tcp-nodelay yes
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes

# 监控和调试
# 启用 Redis 监控
# monitor-enabled yes

# 禁用某些命令（生产环境建议）
# rename-command CONFIG ""
# rename-command SHUTDOWN SHUTDOWN_SFQUANT
# rename-command EVAL ""

# 内存使用报告
# memory-usage-sample-size 5

# 跟踪配置
# tracking-table-max-keys 1000000

# 用户和 ACL（Redis 6.0+）
# user default on nopass ~* &* -@all +@read +@write +@connection +@keyspace +@string +@bitmap +@hash +@list +@set +@sortedset +@stream +@pubsub +@transaction

# 性能调优建议：
# 1. 根据实际内存大小调整 maxmemory
# 2. 根据数据特点选择合适的 maxmemory-policy
# 3. 在生产环境中设置密码
# 4. 根据网络环境调整 timeout 和 tcp-keepalive
# 5. 监控慢查询日志和延迟
