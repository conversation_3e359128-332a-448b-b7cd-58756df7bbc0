# SFQuant - 智能金融量化交易平台

<div align="center">

![SFQuant Logo](https://via.placeholder.com/200x80/4CAF50/FFFFFF?text=SFQuant)

**专注于加密货币的开源量化策略管理平台**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-20232A?logo=react&logoColor=61DAFB)](https://reactjs.org/)
[![Node.js](https://img.shields.io/badge/Node.js-43853D?logo=node.js&logoColor=white)](https://nodejs.org/)

</div>

## 🚀 项目简介

SFQuant (Smart Finance Quantitative) 是一个专为加密货币交易设计的开源量化策略管理平台。我们致力于为个人交易者和量化团队提供易用、高效的策略开发环境。

### ✨ 核心特性

- 🔄 **多策略支持**: 套利、AI策略、趋势跟踪、高频交易、做市商
- 🏦 **多交易所对接**: 支持主流CEX(通过CCXT)和DEX
- 📊 **实时数据**: WebSocket实时行情推送
- 🔙 **策略回测**: 完整的历史数据回测系统
- ⚡ **高性能**: 微服务架构，支持高并发
- 🛡️ **风险管理**: 实时监控和风险控制
- 📱 **现代化UI**: React + TypeScript 响应式界面

### 🎯 目标用户

- 个人加密货币交易者
- 量化交易团队
- 策略开发者
- 金融科技爱好者

## 📋 功能概览

### 策略类型 (按优先级)
1. **套利策略** - 跨交易所价差套利
2. **AI策略** - 机器学习驱动的交易策略
3. **趋势跟踪** - 技术分析趋势策略
4. **高频交易** - 低延迟高频策略
5. **做市商策略** - 流动性提供策略

### 支持的交易所
- **CEX**: Binance, OKX, Huobi, Coinbase Pro, Kraken
- **DEX**: Uniswap, PancakeSwap, SushiSwap

### 数据源 (按优先级)
1. **实时行情数据** - WebSocket实时推送
2. **链上数据** - 区块链实时数据
3. **历史数据** - 完整的历史K线数据

## 🏗️ 技术架构

### 技术栈
- **前端**: React 18+ + TypeScript + Next.js
- **后端**: Node.js + TypeScript + Express
- **数据库**: PostgreSQL + Redis + InfluxDB
- **区块链**: Web3.js + Ethers.js
- **交易所**: CCXT
- **部署**: Docker + Kubernetes

### 架构模式
采用微服务架构，包含以下核心服务：
- 用户服务 (User Service)
- 策略服务 (Strategy Service)
- 交易服务 (Trading Service)
- 数据服务 (Data Service)
- 回测服务 (Backtest Service)
- 风控服务 (Risk Service)

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- Docker >= 20.0.0
- Docker Compose >= 2.0.0
- pnpm >= 8.0.0 (推荐)

### 一键启动

我们提供了完整的自动化脚本，让您可以一键启动整个系统：

```bash
# 克隆项目
git clone https://github.com/your-org/sfquant.git
cd sfquant

# 给脚本执行权限
chmod +x scripts/*.sh

# 一键启动系统（包含数据库设置、依赖安装、服务启动）
./scripts/start-sfquant.sh
```

### 手动安装步骤

如果您希望手动控制安装过程：

1. **克隆项目**
```bash
git clone https://github.com/your-org/sfquant.git
cd sfquant
```

2. **安装依赖**
```bash
# 使用 pnpm (推荐)
pnpm install

# 或使用 npm
npm install
```

3. **设置数据库**
```bash
# 运行数据库设置脚本
./scripts/setup-database.sh
```

4. **配置交易所 API**
```bash
# 运行交易所配置向导
./scripts/configure-exchanges.sh
```

5. **启动服务**
```bash
# 启动 API Gateway
cd apps/api-gateway
pnpm dev

# 新开终端，启动前端
cd apps/frontend
pnpm dev
```

4. **启动服务**
```bash
# 启动数据库服务
docker-compose up -d postgres redis

# 启动开发服务器
pnpm dev
```

### 访问应用

启动成功后，您可以访问以下地址：

- **前端应用**: http://localhost:5173
- **API 服务**: http://localhost:8080
- **API 文档**: http://localhost:8080/documentation
- **健康检查**: http://localhost:8080/health

### 系统管理

```bash
# 停止系统
./scripts/stop-sfquant.sh

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]

# 重启数据库
docker-compose restart postgres redis influxdb
```

## 🔧 配置说明

### 交易所 API 配置

支持以下交易所，请在对应交易所申请 API 密钥：

- **Binance**: 全球最大的加密货币交易所
- **OKX**: 知名衍生品交易所
- **Bybit**: 专业的衍生品交易平台
- **Huobi**: 老牌加密货币交易所

使用配置向导快速设置：
```bash
./scripts/configure-exchanges.sh
```

### 数据库配置

系统使用三种数据库：

- **PostgreSQL**: 存储用户数据、策略配置、交易记录
- **Redis**: 缓存、会话管理、消息队列
- **InfluxDB**: 时序数据、市场数据、监控指标

## 📁 项目结构

```
sfquant/
├── apps/                          # 应用程序
│   ├── frontend/                  # React前端应用
│   ├── api-gateway/               # API网关
│   └── admin/                     # 管理后台
├── services/                      # 微服务
│   ├── user/                      # 用户服务
│   ├── strategy/                  # 策略服务
│   ├── trading/                   # 交易服务
│   ├── data/                      # 数据服务
│   ├── backtest/                  # 回测服务
│   └── risk/                      # 风控服务
├── packages/                      # 共享包
│   ├── common/                    # 通用工具
│   ├── types/                     # TypeScript类型定义
│   ├── config/                    # 配置管理
│   └── database/                  # 数据库模型
├── docs/                          # 项目文档
│   ├── requirements.md            # 需求文档
│   ├── architecture.md            # 架构设计
│   ├── development-roadmap.md     # 开发路线图
│   └── api/                       # API文档
├── scripts/                       # 构建和部署脚本
├── docker/                        # Docker配置
├── k8s/                          # Kubernetes配置
└── tests/                        # 测试文件
```

## 🛠️ 开发指南

### 开发环境设置
详细的开发环境配置请参考 [开发指南](docs/development-guide.md)

### 代码规范
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint + Prettier 代码规范
- 提交前自动运行代码检查和测试

### 测试
```bash
# 运行所有测试
pnpm test

# 运行特定服务测试
pnpm test:user-service

# 生成测试覆盖率报告
pnpm test:coverage
```

## 📖 文档

- [需求文档](docs/requirements.md)
- [架构设计](docs/architecture.md)
- [开发路线图](docs/development-roadmap.md)
- [API文档](docs/api/)
- [部署指南](docs/deployment.md)

## 🤝 贡献指南

我们欢迎所有形式的贡献！请阅读 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

### 贡献方式
- 🐛 报告Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ⭐ 为项目点星支持

## 📄 开源协议

本项目采用 [MIT License](LICENSE) 开源协议。

## 🌟 路线图

### 第一阶段 (MVP - 2个月)
- [x] 基础项目架构
- [ ] 用户管理系统
- [ ] CCXT交易所对接
- [ ] 基础策略框架

### 第二阶段 (核心功能 - 3个月)
- [ ] 实时数据服务
- [ ] 策略回测系统
- [ ] DEX对接模块
- [ ] 风险管理功能

### 第三阶段 (完善功能 - 2个月)
- [ ] AI策略支持
- [ ] 移动端应用
- [ ] 性能优化
- [ ] 文档完善

详细路线图请查看 [开发路线图](docs/development-roadmap.md)

## 💬 社区

- **GitHub Issues**: [问题反馈](https://github.com/your-org/sfquant/issues)
- **GitHub Discussions**: [社区讨论](https://github.com/your-org/sfquant/discussions)
- **Discord**: [加入Discord社区](https://discord.gg/sfquant)
- **Telegram**: [Telegram群组](https://t.me/sfquant)

## ⚠️ 免责声明

本项目仅供学习和研究使用。加密货币交易存在高风险，请在使用前充分了解相关风险，并根据自身情况谨慎决策。项目开发者不对任何交易损失承担责任。

## 📊 项目状态

![GitHub stars](https://img.shields.io/github/stars/your-org/sfquant?style=social)
![GitHub forks](https://img.shields.io/github/forks/your-org/sfquant?style=social)
![GitHub issues](https://img.shields.io/github/issues/your-org/sfquant)
![GitHub pull requests](https://img.shields.io/github/issues-pr/your-org/sfquant)

---

<div align="center">

**如果这个项目对你有帮助，请给我们一个 ⭐ Star！**

Made with ❤️ by SFQuant Team

</div>
