# 数据库配置
DATABASE_URL=postgresql://sfquant:password@localhost:5432/sfquant
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# API配置
API_PORT=8080
FRONTEND_PORT=3001

# 交易所API密钥 (示例 - 请使用您自己的密钥)
BINANCE_API_KEY=your-binance-api-key
BINANCE_SECRET_KEY=your-binance-secret-key
BINANCE_SANDBOX=true

OKX_API_KEY=your-okx-api-key
OKX_SECRET_KEY=your-okx-secret-key
OKX_PASSPHRASE=your-okx-passphrase
OKX_SANDBOX=true

# Web3配置
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-infura-key
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your-infura-key
BSC_RPC_URL=https://bsc-dataseed.binance.org/

# 数据源配置
COINMARKETCAP_API_KEY=your-coinmarketcap-api-key
COINGECKO_API_KEY=your-coingecko-api-key

# 监控和日志
LOG_LEVEL=info
SENTRY_DSN=your-sentry-dsn

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# 通知配置
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
DISCORD_WEBHOOK_URL=your-discord-webhook-url

# 开发环境配置
NODE_ENV=development
DEBUG=sfquant:*
