import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// API 配置
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080'
const WS_BASE_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8080'

// 创建 axios 实例
const apiClient: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证 token
    const token = localStorage.getItem('auth_token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳
    if (config.headers) {
      config.headers['X-Request-Time'] = Date.now().toString()
    }

    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('❌ Response Error:', error)

    // 处理认证错误
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }

    return Promise.reject(error)
  }
)

// API 服务类
export class ApiService {
  // 认证相关
  static async login(email: string, password: string) {
    const response = await apiClient.post('/auth/login', { email, password })
    return response.data
  }

  static async register(userData: {
    email: string
    username: string
    password: string
    firstName?: string
    lastName?: string
  }) {
    const response = await apiClient.post('/auth/register', userData)
    return response.data
  }

  static async logout() {
    const response = await apiClient.post('/auth/logout')
    localStorage.removeItem('auth_token')
    return response.data
  }

  static async refreshToken() {
    const response = await apiClient.post('/auth/refresh')
    return response.data
  }

  // 用户相关
  static async getUserProfile() {
    const response = await apiClient.get('/auth/profile')
    return response.data
  }

  static async updateUserProfile(userData: any) {
    const response = await apiClient.put('/auth/profile', userData)
    return response.data
  }

  // 策略相关
  static async getStrategies(params?: {
    status?: string
    type?: string
    limit?: number
    offset?: number
  }) {
    const response = await apiClient.get('/strategies', { params })
    return response.data
  }

  // 策略管理器相关
  static async getStrategyManager() {
    const response = await apiClient.get('/strategy-manager')
    return response.data
  }

  static async createArbitrageStrategy(strategyData: {
    name: string
    symbol: string
    exchanges: string[]
    minProfitPercent: number
    maxPositionSize?: number
    checkInterval?: number
  }) {
    const response = await apiClient.post('/strategy-manager/arbitrage', strategyData)
    return response.data
  }

  static async createGridStrategy(strategyData: {
    name: string
    symbol: string
    exchange: string
    basePrice: number
    gridSpacing: number
    gridLevels: number
    orderAmount: number
    upperLimit?: number
    lowerLimit?: number
  }) {
    const response = await apiClient.post('/strategy-manager/grid', strategyData)
    return response.data
  }

  static async startStrategyManager(strategyId: string) {
    const response = await apiClient.post(`/strategy-manager/${strategyId}/start`)
    return response.data
  }

  static async stopStrategyManager(strategyId: string) {
    const response = await apiClient.post(`/strategy-manager/${strategyId}/stop`)
    return response.data
  }

  static async deleteStrategyManager(strategyId: string) {
    const response = await apiClient.delete(`/strategy-manager/${strategyId}`)
    return response.data
  }

  static async getStrategyManagerDetails(strategyId: string) {
    const response = await apiClient.get(`/strategy-manager/${strategyId}`)
    return response.data
  }

  static async getRunningStrategies() {
    const response = await apiClient.get('/strategy-manager/running')
    return response.data
  }

  static async getStrategy(strategyId: string) {
    const response = await apiClient.get(`/strategies/${strategyId}`)
    return response.data
  }

  static async createStrategy(strategyData: {
    name: string
    description?: string
    type: string
    config: any
  }) {
    const response = await apiClient.post('/strategies', strategyData)
    return response.data
  }

  static async updateStrategy(strategyId: string, updates: any) {
    const response = await apiClient.put(`/strategies/${strategyId}`, updates)
    return response.data
  }

  static async deleteStrategy(strategyId: string) {
    const response = await apiClient.delete(`/strategies/${strategyId}`)
    return response.data
  }

  static async startStrategy(strategyId: string) {
    const response = await apiClient.post(`/strategies/${strategyId}/start`)
    return response.data
  }

  static async stopStrategy(strategyId: string) {
    const response = await apiClient.post(`/strategies/${strategyId}/stop`)
    return response.data
  }

  static async getStrategyPerformance(strategyId: string) {
    const response = await apiClient.get(`/strategies/${strategyId}/performance`)
    return response.data
  }

  // 市场数据相关
  static async getMarketData(symbol: string, exchange?: string) {
    const params = exchange ? { exchange } : {}
    const response = await apiClient.get(`/market-data/${symbol}`, { params })
    return response.data
  }

  static async getKlineData(
    symbol: string,
    timeframe: string = '1h',
    limit: number = 100,
    exchange?: string
  ) {
    const params = { timeframe, limit, ...(exchange && { exchange }) }
    const response = await apiClient.get(`/market-data/${symbol}/kline`, { params })
    return response.data
  }

  static async getOrderBook(symbol: string, exchange?: string) {
    const params = exchange ? { exchange } : {}
    const response = await apiClient.get(`/market-data/${symbol}/orderbook`, { params })
    return response.data
  }

  static async getTicker(symbol: string, exchange?: string) {
    const params = exchange ? { exchange } : {}
    const response = await apiClient.get(`/market-data/${symbol}/ticker`, { params })
    return response.data
  }

  // 交易相关
  static async getTrades(strategyId?: string) {
    const params = strategyId ? { strategyId } : {}
    const response = await apiClient.get('/trading/trades', { params })
    return response.data
  }

  static async getPositions() {
    const response = await apiClient.get('/trading/positions')
    return response.data
  }

  static async getBalance(exchange?: string) {
    const params = exchange ? { exchange } : {}
    const response = await apiClient.get('/trading/balance', { params })
    return response.data
  }

  // 交易所相关
  static async getExchanges() {
    const response = await apiClient.get('/exchanges')
    return response.data
  }

  static async getExchangeStatus() {
    const response = await apiClient.get('/exchanges/status')
    return response.data
  }

  // 实时数据相关
  static async getRealTimePrice(exchange: string, symbol: string) {
    const response = await apiClient.get(`/realtime/price/${exchange}/${symbol}`)
    return response.data
  }

  static async getRealTimeOrderBook(exchange: string, symbol: string) {
    const response = await apiClient.get(`/realtime/orderbook/${exchange}/${symbol}`)
    return response.data
  }

  static async startRealTimeService() {
    const response = await apiClient.post('/realtime/start')
    return response.data
  }

  static async stopRealTimeService() {
    const response = await apiClient.post('/realtime/stop')
    return response.data
  }

  static async getExecutionStatus() {
    const response = await apiClient.get('/execution/status')
    return response.data
  }

  static async startExecutionEngine() {
    const response = await apiClient.post('/execution/start')
    return response.data
  }

  static async stopExecutionEngine() {
    const response = await apiClient.post('/execution/stop')
    return response.data
  }

  // 系统相关
  static async getSystemHealth() {
    const response = await apiClient.get('/health')
    return response.data
  }

  static async getSystemHealthDetailed() {
    const response = await apiClient.get('/health/detailed')
    return response.data
  }

  static async getSystemMetrics() {
    const response = await apiClient.get('/health/metrics')
    return response.data
  }

  static async getSystemAlerts() {
    const response = await apiClient.get('/metrics/alerts')
    return response.data
  }

  // WebSocket 相关
  static async getWebSocketStats() {
    const response = await apiClient.get('/websocket/stats')
    return response.data
  }
}

// WebSocket 服务类
export class WebSocketService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 5000
  private listeners: Map<string, Function[]> = new Map()

  constructor(private endpoint: string = '') {}

  connect(token?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const wsUrl = `${WS_BASE_URL}${this.endpoint}${token ? `?token=${token}` : ''}`
        this.ws = new WebSocket(wsUrl)

        this.ws.onopen = () => {
          console.log('🔗 WebSocket connected')
          this.reconnectAttempts = 0
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.handleMessage(data)
          } catch (error) {
            console.error('❌ Failed to parse WebSocket message:', error)
          }
        }

        this.ws.onclose = () => {
          console.log('🔌 WebSocket disconnected')
          this.attemptReconnect()
        }

        this.ws.onerror = (error) => {
          console.error('❌ WebSocket error:', error)
          reject(error)
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  send(data: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    } else {
      console.warn('⚠️ WebSocket is not connected')
    }
  }

  subscribe(channel: string, callback: Function): void {
    if (!this.listeners.has(channel)) {
      this.listeners.set(channel, [])
    }
    this.listeners.get(channel)!.push(callback)

    // 发送订阅消息
    this.send({
      type: 'subscribe',
      channel
    })
  }

  unsubscribe(channel: string, callback?: Function): void {
    if (callback) {
      const callbacks = this.listeners.get(channel) || []
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    } else {
      this.listeners.delete(channel)
    }

    // 发送取消订阅消息
    this.send({
      type: 'unsubscribe',
      channel
    })
  }

  private handleMessage(data: any): void {
    const { channel, type, payload } = data

    if (channel && this.listeners.has(channel)) {
      const callbacks = this.listeners.get(channel) || []
      callbacks.forEach(callback => callback(payload))
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)

      setTimeout(() => {
        const token = localStorage.getItem('auth_token')
        this.connect(token).catch(console.error)
      }, this.reconnectInterval)
    } else {
      console.error('❌ Max reconnection attempts reached')
    }
  }
}

// 创建全局 WebSocket 实例
export const marketDataWS = new WebSocketService('/ws/market-data')
export const tradingWS = new WebSocketService('/ws/trading')
export const systemWS = new WebSocketService('/ws/system')

export default ApiService
