/**
 * 技术指标计算类 (JavaScript版本)
 * 提供常用的技术分析指标计算
 */
export class TechnicalIndicators {
  
  /**
   * 简单移动平均线 (SMA)
   * @param prices 价格数组
   * @param period 周期
   */
  sma(prices: number[], period: number): number[] {
    if (prices.length < period) {
      return [];
    }
    
    const result: number[] = [];
    
    for (let i = period - 1; i < prices.length; i++) {
      const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push(sum / period);
    }
    
    return result;
  }
  
  /**
   * 指数移动平均线 (EMA)
   * @param prices 价格数组
   * @param period 周期
   */
  ema(prices: number[], period: number): number[] {
    if (prices.length === 0) {
      return [];
    }
    
    const result: number[] = [];
    const multiplier = 2 / (period + 1);
    
    result.push(prices[0]);
    
    for (let i = 1; i < prices.length; i++) {
      const ema = (prices[i] * multiplier) + (result[i - 1] * (1 - multiplier));
      result.push(ema);
    }
    
    return result;
  }
  
  /**
   * RSI相对强弱指数
   * @param prices 价格数组
   * @param period 周期
   */
  rsi(prices: number[], period: number): number[] {
    if (prices.length < period + 1) {
      return [];
    }
    
    const gains: number[] = [];
    const losses: number[] = [];
    
    // 计算价格变化
    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? -change : 0);
    }
    
    const avgGains = this.sma(gains, period);
    const avgLosses = this.sma(losses, period);
    
    const rsiValues: number[] = [];
    for (let i = 0; i < avgGains.length; i++) {
      if (avgLosses[i] === 0) {
        rsiValues.push(100);
      } else {
        const rs = avgGains[i] / avgLosses[i];
        const rsi = 100 - (100 / (1 + rs));
        rsiValues.push(rsi);
      }
    }
    
    return rsiValues;
  }
  
  /**
   * MACD指标
   * @param prices 价格数组
   * @param fastPeriod 快线周期
   * @param slowPeriod 慢线周期
   * @param signalPeriod 信号线周期
   */
  macd(prices: number[], fastPeriod: number, slowPeriod: number, signalPeriod: number): number[] {
    const emaFast = this.ema(prices, fastPeriod);
    const emaSlow = this.ema(prices, slowPeriod);
    
    const macdLine: number[] = [];
    const minLength = Math.min(emaFast.length, emaSlow.length);
    
    for (let i = 0; i < minLength; i++) {
      macdLine.push(emaFast[i] - emaSlow[i]);
    }
    
    return this.ema(macdLine, signalPeriod);
  }
  
  /**
   * 布林带
   * @param prices 价格数组
   * @param period 周期
   * @param stdDev 标准差倍数
   */
  bollingerBands(prices: number[], period: number, stdDev: number = 2): {
    upper: number[];
    middle: number[];
    lower: number[];
  } {
    const sma = this.sma(prices, period);
    const upper: number[] = [];
    const lower: number[] = [];
    
    for (let i = 0; i < sma.length; i++) {
      const startIdx = i;
      const endIdx = i + period;
      
      if (endIdx <= prices.length) {
        const slice = prices.slice(startIdx, endIdx);
        const mean = sma[i];
        
        // 计算标准差
        const variance = slice.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / period;
        const std = Math.sqrt(variance);
        
        upper.push(mean + (stdDev * std));
        lower.push(mean - (stdDev * std));
      }
    }
    
    return {
      upper,
      middle: sma,
      lower
    };
  }
  
  /**
   * 套利机会检测
   * @param exchange1Prices 交易所1价格
   * @param exchange2Prices 交易所2价格
   * @param threshold 阈值
   */
  detectArbitrage(exchange1Prices: number[], exchange2Prices: number[], threshold: number): boolean[] {
    const opportunities: boolean[] = [];
    const minLength = Math.min(exchange1Prices.length, exchange2Prices.length);
    
    for (let i = 0; i < minLength; i++) {
      const priceDiff = Math.abs(exchange1Prices[i] - exchange2Prices[i]);
      const minPrice = Math.min(exchange1Prices[i], exchange2Prices[i]);
      
      if (minPrice > 0) {
        const percentageDiff = priceDiff / minPrice;
        opportunities.push(percentageDiff > threshold);
      } else {
        opportunities.push(false);
      }
    }
    
    return opportunities;
  }
  
  /**
   * 计算夏普比率
   * @param returns 收益率数组
   * @param riskFreeRate 无风险利率
   */
  calculateSharpeRatio(returns: number[], riskFreeRate: number = 0): number {
    if (returns.length === 0) {
      return 0;
    }
    
    const meanReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const excessReturn = meanReturn - riskFreeRate;
    
    if (returns.length < 2) {
      return 0;
    }
    
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - meanReturn, 2), 0) / (returns.length - 1);
    const stdDev = Math.sqrt(variance);
    
    return stdDev === 0 ? 0 : excessReturn / stdDev;
  }
  
  /**
   * 计算最大回撤
   * @param prices 价格数组
   */
  calculateMaxDrawdown(prices: number[]): number {
    if (prices.length < 2) {
      return 0;
    }
    
    let maxDrawdown = 0;
    let peak = prices[0];
    
    for (let i = 1; i < prices.length; i++) {
      if (prices[i] > peak) {
        peak = prices[i];
      }
      
      const drawdown = (peak - prices[i]) / peak;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }
    
    return maxDrawdown;
  }
}
