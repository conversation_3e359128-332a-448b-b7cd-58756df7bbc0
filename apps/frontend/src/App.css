.App {
  text-align: center;
}

.App-header {
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  padding: 30px;
  color: white;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.App-header h1 {
  margin: 0 0 16px 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.App-header p {
  margin: 8px 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.App-content {
  max-width: 1000px;
  margin: 0 auto;
}

.ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ant-statistic-title {
  font-weight: 600;
}

.ant-statistic-content {
  color: #1890ff;
}
