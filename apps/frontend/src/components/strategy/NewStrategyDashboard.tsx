import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Row,
  Col,
  Statistic,
  Tooltip,
  Popconfirm,
  message,
  Tabs,
  Badge,
  Progress
} from 'antd'
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  TrophyOutlined,
  DollarOutlined,
  LineChartOutlined,
  RobotOutlined
} from '@ant-design/icons'
import { useStrategyManager } from '../../hooks/useStrategyManager'
import { useRealTimeData } from '../../hooks/useRealTimeData'

const { Option } = Select
const { TabPane } = Tabs

const NewStrategyDashboard: React.FC = () => {
  const {
    strategies,
    stats,
    loading,
    error,
    fetchStrategies,
    createArbitrageStrategy,
    createGridStrategy,
    startStrategy,
    stopStrategy,
    deleteStrategy,
    startAllStrategies,
    stopAllStrategies,
    getPerformanceStats
  } = useStrategyManager()

  const {
    isConnected,
    strategySignals,
    subscribeTrading,
    unsubscribeTrading
  } = useRealTimeData()

  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [createForm] = Form.useForm()
  const [selectedStrategyType, setSelectedStrategyType] = useState<'arbitrage' | 'grid'>('arbitrage')

  // 订阅交易信号
  useEffect(() => {
    if (isConnected) {
      subscribeTrading()
    }
    
    return () => {
      unsubscribeTrading()
    }
  }, [isConnected, subscribeTrading, unsubscribeTrading])

  // 创建策略
  const handleCreateStrategy = async (values: any) => {
    try {
      if (selectedStrategyType === 'arbitrage') {
        const config = {
          name: values.name,
          symbol: values.symbol,
          exchanges: values.exchanges,
          minProfitPercent: values.minProfitPercent,
          maxPositionSize: values.maxPositionSize || 1000,
          checkInterval: values.checkInterval || 5000
        }
        await createArbitrageStrategy(config)
      } else if (selectedStrategyType === 'grid') {
        const config = {
          name: values.name,
          symbol: values.symbol,
          exchange: values.exchange,
          basePrice: values.basePrice,
          gridSpacing: values.gridSpacing,
          gridLevels: values.gridLevels,
          orderAmount: values.orderAmount,
          upperLimit: values.upperLimit,
          lowerLimit: values.lowerLimit
        }
        await createGridStrategy(config)
      }
      
      setCreateModalVisible(false)
      createForm.resetFields()
    } catch (error) {
      // 错误已在 hook 中处理
    }
  }

  const handleStrategySettings = (strategy: any) => {
    // TODO: 实现策略设置功能
    message.info('策略设置功能开发中...')
  }

  // 工具函数
  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      arbitrage: 'gold',
      grid: 'blue',
      trend: 'green',
      dca: 'purple',
      ai: 'red'
    }
    return colors[type] || 'default'
  }

  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      arbitrage: '套利',
      grid: '网格',
      trend: '趋势',
      dca: '定投',
      ai: 'AI'
    }
    return labels[type] || type
  }

  const getStatusBadge = (status: string) => {
    const badges: Record<string, any> = {
      running: 'processing',
      stopped: 'default',
      error: 'error'
    }
    return badges[status] || 'default'
  }

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      running: '运行中',
      stopped: '已停止',
      error: '错误'
    }
    return labels[status] || status
  }

  // 表格列定义
  const columns = [
    {
      title: '策略名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <Space>
          <RobotOutlined />
          <span>{text}</span>
          <Tag color={getTypeColor(record.type)}>{getTypeLabel(record.type)}</Tag>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge
          status={getStatusBadge(status)}
          text={getStatusLabel(status)}
        />
      )
    },
    {
      title: '交易对',
      key: 'symbol',
      render: (record: any) => {
        const symbol = record.config?.symbol || record.strategySpecific?.symbol || '-'
        return <Tag>{symbol}</Tag>
      }
    },
    {
      title: '交易所',
      key: 'exchange',
      render: (record: any) => {
        const exchanges = record.config?.exchanges || [record.config?.exchange]
        return (
          <Space>
            {exchanges?.map((exchange: string) => (
              <Tag key={exchange} color="blue">{exchange}</Tag>
            ))}
          </Space>
        )
      }
    },
    {
      title: '收益率',
      key: 'return',
      render: (record: any) => {
        const totalReturn = record.performance?.totalReturn || 0
        return (
          <span style={{ color: totalReturn >= 0 ? '#52c41a' : '#ff4d4f' }}>
            {totalReturn >= 0 ? '+' : ''}{totalReturn.toFixed(2)}%
          </span>
        )
      }
    },
    {
      title: '胜率',
      key: 'winRate',
      render: (record: any) => {
        const winRate = record.performance?.winRate || 0
        return (
          <Progress
            percent={winRate}
            size="small"
            format={percent => `${percent}%`}
          />
        )
      }
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: any) => (
        <Space>
          {record.status === 'running' ? (
            <Tooltip title="停止策略">
              <Button
                type="text"
                icon={<PauseCircleOutlined />}
                onClick={() => stopStrategy(record.id)}
                loading={loading}
              />
            </Tooltip>
          ) : (
            <Tooltip title="启动策略">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => startStrategy(record.id)}
                loading={loading}
              />
            </Tooltip>
          )}
          
          <Tooltip title="策略设置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => handleStrategySettings(record)}
            />
          </Tooltip>
          
          <Popconfirm
            title="确定要删除这个策略吗？"
            onConfirm={() => deleteStrategy(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除策略">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                loading={loading}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  const performanceStats = getPerformanceStats()

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总策略数"
              value={stats?.total || 0}
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中"
              value={stats?.running || 0}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总收益率"
              value={performanceStats.totalReturn}
              precision={2}
              suffix="%"
              prefix={<TrophyOutlined />}
              valueStyle={{ 
                color: performanceStats.totalReturn >= 0 ? '#52c41a' : '#ff4d4f' 
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均胜率"
              value={performanceStats.averageWinRate}
              precision={1}
              suffix="%"
              prefix={<LineChartOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card
        title="策略管理"
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchStrategies}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              创建策略
            </Button>
          </Space>
        }
      >
        <Tabs defaultActiveKey="strategies">
          <TabPane tab="策略列表" key="strategies">
            <div style={{ marginBottom: '16px' }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={startAllStrategies}
                  loading={loading}
                >
                  启动全部
                </Button>
                <Button
                  icon={<PauseCircleOutlined />}
                  onClick={stopAllStrategies}
                  loading={loading}
                >
                  停止全部
                </Button>
                <span style={{ marginLeft: '16px', color: '#666' }}>
                  WebSocket: {isConnected ? 
                    <Badge status="success" text="已连接" /> : 
                    <Badge status="error" text="未连接" />
                  }
                </span>
              </Space>
            </div>
            
            <Table
              columns={columns}
              dataSource={strategies}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个策略`
              }}
            />
          </TabPane>
          
          <TabPane tab={`交易信号 (${strategySignals.length})`} key="signals">
            <Table
              dataSource={strategySignals}
              rowKey={(record) => `${record.strategyId}-${record.timestamp}`}
              pagination={{ pageSize: 20 }}
              columns={[
                {
                  title: '时间',
                  dataIndex: 'timestamp',
                  render: (timestamp) => new Date(timestamp).toLocaleString()
                },
                {
                  title: '策略ID',
                  dataIndex: 'strategyId'
                },
                {
                  title: '操作',
                  dataIndex: 'action',
                  render: (action) => (
                    <Tag color={action === 'buy' ? 'green' : action === 'sell' ? 'red' : 'default'}>
                      {action.toUpperCase()}
                    </Tag>
                  )
                },
                {
                  title: '交易对',
                  dataIndex: 'symbol'
                },
                {
                  title: '数量',
                  dataIndex: 'amount'
                },
                {
                  title: '价格',
                  dataIndex: 'price'
                },
                {
                  title: '置信度',
                  dataIndex: 'confidence',
                  render: (confidence) => `${confidence}%`
                },
                {
                  title: '原因',
                  dataIndex: 'reason'
                }
              ]}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建策略模态框 */}
      <Modal
        title="创建新策略"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false)
          createForm.resetFields()
        }}
        onOk={() => createForm.submit()}
        width={600}
        confirmLoading={loading}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateStrategy}
        >
          <Form.Item
            label="策略类型"
            name="type"
            initialValue="arbitrage"
          >
            <Select onChange={setSelectedStrategyType}>
              <Option value="arbitrage">套利策略</Option>
              <Option value="grid">网格策略</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="策略名称"
            name="name"
            rules={[{ required: true, message: '请输入策略名称' }]}
          >
            <Input placeholder="请输入策略名称" />
          </Form.Item>

          <Form.Item
            label="交易对"
            name="symbol"
            rules={[{ required: true, message: '请输入交易对' }]}
          >
            <Input placeholder="例如: BTCUSDT" />
          </Form.Item>

          {selectedStrategyType === 'arbitrage' && (
            <>
              <Form.Item
                label="交易所"
                name="exchanges"
                rules={[{ required: true, message: '请选择至少两个交易所' }]}
              >
                <Select mode="multiple" placeholder="选择交易所">
                  <Option value="binance">Binance</Option>
                  <Option value="okx">OKX</Option>
                  <Option value="bybit">Bybit</Option>
                  <Option value="huobi">Huobi</Option>
                </Select>
              </Form.Item>

              <Form.Item
                label="最小利润率 (%)"
                name="minProfitPercent"
                rules={[{ required: true, message: '请输入最小利润率' }]}
              >
                <InputNumber
                  min={0.1}
                  max={10}
                  step={0.1}
                  placeholder="0.5"
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item
                label="最大仓位 (USDT)"
                name="maxPositionSize"
              >
                <InputNumber
                  min={100}
                  max={100000}
                  step={100}
                  placeholder="1000"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </>
          )}

          {selectedStrategyType === 'grid' && (
            <>
              <Form.Item
                label="交易所"
                name="exchange"
                rules={[{ required: true, message: '请选择交易所' }]}
              >
                <Select placeholder="选择交易所">
                  <Option value="binance">Binance</Option>
                  <Option value="okx">OKX</Option>
                  <Option value="bybit">Bybit</Option>
                  <Option value="huobi">Huobi</Option>
                </Select>
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="基准价格"
                    name="basePrice"
                    rules={[{ required: true, message: '请输入基准价格' }]}
                  >
                    <InputNumber
                      min={0.01}
                      step={0.01}
                      placeholder="2000"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="网格间距 (%)"
                    name="gridSpacing"
                    rules={[{ required: true, message: '请输入网格间距' }]}
                  >
                    <InputNumber
                      min={0.1}
                      max={10}
                      step={0.1}
                      placeholder="1.0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="网格层数"
                    name="gridLevels"
                    rules={[{ required: true, message: '请输入网格层数' }]}
                  >
                    <InputNumber
                      min={4}
                      max={50}
                      step={2}
                      placeholder="10"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="单网格金额"
                    name="orderAmount"
                    rules={[{ required: true, message: '请输入单网格金额' }]}
                  >
                    <InputNumber
                      min={0.001}
                      step={0.001}
                      placeholder="0.1"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="上限价格"
                    name="upperLimit"
                  >
                    <InputNumber
                      min={0.01}
                      step={0.01}
                      placeholder="2200"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="下限价格"
                    name="lowerLimit"
                  >
                    <InputNumber
                      min={0.01}
                      step={0.01}
                      placeholder="1800"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </>
          )}
        </Form>
      </Modal>
    </div>
  )
}

export default NewStrategyDashboard
