import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Statistic,
  Row,
  Col,
  Tooltip,
  Progress,
  Divider,
  Tabs,
  Badge,
  Popconfirm
} from 'antd'
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  TrophyOutlined,
  DollarOutlined,
  PercentageOutlined,
  LineChartOutlined,
  ReloadOutlined,
  RobotOutlined,
  SettingOutlined
} from '@ant-design/icons'
import { useStrategyManager } from '../../hooks/useStrategyManager'
import { useRealTimeData } from '../../hooks/useRealTimeData'

const { Option } = Select
const { TextArea } = Input
const { TabPane } = Tabs

const StrategyDashboard: React.FC = () => {
  const {
    strategies,
    stats,
    loading,
    error,
    fetchStrategies,
    createArbitrageStrategy,
    createGridStrategy,
    startStrategy,
    stopStrategy,
    deleteStrategy,
    startAllStrategies,
    stopAllStrategies,
    getPerformanceStats
  } = useStrategyManager()

  const {
    isConnected,
    strategySignals,
    subscribeTrading,
    unsubscribeTrading
  } = useRealTimeData()

  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [createForm] = Form.useForm()
  const [selectedStrategyType, setSelectedStrategyType] = useState<'arbitrage' | 'grid'>('arbitrage')

  // 订阅交易信号
  useEffect(() => {
    if (isConnected) {
      subscribeTrading()
    }

    return () => {
      unsubscribeTrading()
    }
  }, [isConnected, subscribeTrading, unsubscribeTrading])

  // 创建策略
  const handleCreateStrategy = async (values: any) => {
    try {
      if (selectedStrategyType === 'arbitrage') {
        const config = {
          name: values.name,
          symbol: values.symbol,
          exchanges: values.exchanges,
          minProfitPercent: values.minProfitPercent,
          maxPositionSize: values.maxPositionSize,
          checkInterval: values.checkInterval
        }
        await createArbitrageStrategy(config)
      } else if (selectedStrategyType === 'grid') {
        const config = {
          name: values.name,
          symbol: values.symbol,
          exchange: values.exchange,
          basePrice: values.basePrice,
          gridSpacing: values.gridSpacing,
          gridLevels: values.gridLevels,
          orderAmount: values.orderAmount,
          upperLimit: values.upperLimit,
          lowerLimit: values.lowerLimit
        }
        await createGridStrategy(config)
      }

      setCreateModalVisible(false)
      createForm.resetFields()
    } catch (error) {
      // 错误已在 hook 中处理
    }
  }

  const handleStrategySettings = (strategy: any) => {
    // TODO: 实现策略设置功能
    message.info('策略设置功能开发中...')
  }

  // 工具函数
  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      arbitrage: 'gold',
      grid: 'blue',
      trend: 'green',
      dca: 'purple',
      ai: 'red'
    }
    return colors[type] || 'default'
  }

  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      arbitrage: '套利',
      grid: '网格',
      trend: '趋势',
      dca: '定投',
      ai: 'AI'
    }
    return labels[type] || type
  }

  const getStatusBadge = (status: string) => {
    const badges: Record<string, any> = {
      running: 'processing',
      stopped: 'default',
      error: 'error'
    }
    return badges[status] || 'default'
  }

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      running: '运行中',
      stopped: '已停止',
      error: '错误'
    }
    return labels[status] || status
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'paused': return 'orange'
      case 'error': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '运行中'
      case 'inactive': return '已停止'
      case 'paused': return '已暂停'
      case 'error': return '错误'
      default: return status
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'arbitrage': return '套利策略'
      case 'ai': return 'AI策略'
      case 'trend': return '趋势策略'
      case 'grid': return '网格策略'
      case 'dca': return '定投策略'
      default: return type
    }
  }

  const columns = [
    {
      title: '策略名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Strategy) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.config.symbol} @ {record.config.exchange}
          </div>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color="blue">{getTypeText(type)}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: '性能指标',
      key: 'performance',
      render: (record: Strategy) => {
        const perf = record.performance
        if (!perf) {
          return <span style={{ color: '#999' }}>暂无数据</span>
        }

        return (
          <Space direction="vertical" size="small">
            <div>
              <DollarOutlined style={{ color: '#52c41a' }} />
              <span style={{ marginLeft: 4 }}>
                收益: {perf.totalReturn?.toFixed(2) || '0.00'}
              </span>
            </div>
            <div>
              <PercentageOutlined style={{ color: '#1890ff' }} />
              <span style={{ marginLeft: 4 }}>
                胜率: {perf.winRate?.toFixed(1) || '0.0'}%
              </span>
            </div>
          </Space>
        )
      }
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: Strategy) => (
        <Space>
          {record.status === 'active' ? (
            <Tooltip title="停止策略">
              <Button
                type="text"
                icon={<StopOutlined />}
                onClick={() => handleStopStrategy(record.id)}
                danger
              />
            </Tooltip>
          ) : (
            <Tooltip title="启动策略">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => handleStartStrategy(record.id)}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          <Tooltip title="编辑策略">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditStrategy(record)}
            />
          </Tooltip>
          <Tooltip title="删除策略">
            <Button
              type="text"
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteStrategy(record.id)}
              danger
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  // 计算总体统计
  const totalStrategies = strategies.length
  const activeStrategies = strategies.filter(s => s.status === 'active').length
  const totalReturn = strategies.reduce((sum, s) => sum + (s.performance?.totalReturn || 0), 0)
  const avgWinRate = strategies.length > 0
    ? strategies.reduce((sum, s) => sum + (s.performance?.winRate || 0), 0) / strategies.length
    : 0

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总策略数"
              value={totalStrategies}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中"
              value={activeStrategies}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总收益"
              value={totalReturn}
              precision={2}
              prefix={<DollarOutlined />}
              valueStyle={{ color: totalReturn >= 0 ? '#52c41a' : '#f5222d' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均胜率"
              value={avgWinRate}
              precision={1}
              suffix="%"
              prefix={<PercentageOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 策略列表 */}
      <Card
        title="策略列表"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateStrategy}
          >
            创建策略
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={strategies}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 创建/编辑策略模态框 */}
      <Modal
        title={editingStrategy ? '编辑策略' : '创建策略'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: 'arbitrage',
            riskLevel: 'medium',
            capital: 1000
          }}
        >
          <Form.Item
            name="name"
            label="策略名称"
            rules={[{ required: true, message: '请输入策略名称' }]}
          >
            <Input placeholder="请输入策略名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="策略描述"
          >
            <TextArea rows={3} placeholder="请输入策略描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="策略类型"
                rules={[{ required: true, message: '请选择策略类型' }]}
              >
                <Select placeholder="请选择策略类型">
                  <Option value="arbitrage">套利策略</Option>
                  <Option value="ai">AI策略</Option>
                  <Option value="trend">趋势策略</Option>
                  <Option value="grid">网格策略</Option>
                  <Option value="dca">定投策略</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="symbol"
                label="交易对"
                rules={[{ required: true, message: '请输入交易对' }]}
              >
                <Input placeholder="如: BTC/USDT" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="exchange"
                label="交易所"
                rules={[{ required: true, message: '请选择交易所' }]}
              >
                <Select placeholder="请选择交易所">
                  <Option value="binance">Binance</Option>
                  <Option value="okx">OKX</Option>
                  <Option value="bybit">Bybit</Option>
                  <Option value="huobi">Huobi</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="capital"
                label="投入资金"
                rules={[{ required: true, message: '请输入投入资金' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入投入资金"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="riskLevel"
            label="风险等级"
            rules={[{ required: true, message: '请选择风险等级' }]}
          >
            <Select placeholder="请选择风险等级">
              <Option value="low">低风险</Option>
              <Option value="medium">中风险</Option>
              <Option value="high">高风险</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default StrategyDashboard
