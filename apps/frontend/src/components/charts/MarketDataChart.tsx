import React, { useState, useEffect, useRef } from 'react'
import { Card, Select, Space, Tag, Statistic, Row, Col, Button } from 'antd'
import { useRealTimeData } from '../../hooks/useRealTimeData'

const { Option } = Select

interface MarketDataChartProps {
  symbol?: string
  exchange?: string
  height?: number
}

const MarketDataChart: React.FC<MarketDataChartProps> = ({
  symbol = 'BTCUSDT',
  exchange = 'binance',
  height = 400
}) => {
  const {
    isConnected,
    prices,
    orderBooks,
    trades,
    subscribeMarketData,
    unsubscribeMarketData,
    getPrice,
    getOrderBook
  } = useRealTimeData()

  const [selectedSymbol, setSelectedSymbol] = useState(symbol)
  const [selectedExchange, setSelectedExchange] = useState(exchange)
  const [priceHistory, setPriceHistory] = useState<any[]>([])
  const [isSubscribed, setIsSubscribed] = useState(false)

  // 订阅市场数据
  useEffect(() => {
    if (isConnected && !isSubscribed) {
      subscribeMarketData(selectedExchange, selectedSymbol)
      setIsSubscribed(true)
    }

    return () => {
      if (isSubscribed) {
        unsubscribeMarketData(selectedExchange, selectedSymbol)
        setIsSubscribed(false)
      }
    }
  }, [isConnected, selectedExchange, selectedSymbol, isSubscribed, subscribeMarketData, unsubscribeMarketData])

  // 更新价格历史
  useEffect(() => {
    const currentPrice = getPrice(selectedExchange, selectedSymbol)
    if (currentPrice) {
      const now = new Date()
      setPriceHistory(prev => {
        const newHistory = [...prev, {
          time: now.toLocaleTimeString(),
          timestamp: now.getTime(),
          price: currentPrice.price,
          volume: currentPrice.volume
        }]

        // 保留最近100个数据点
        return newHistory.slice(-100)
      })
    }
  }, [prices, selectedExchange, selectedSymbol, getPrice])

  // 切换交易对
  const handleSymbolChange = (newSymbol: string) => {
    if (isSubscribed) {
      unsubscribeMarketData(selectedExchange, selectedSymbol)
    }
    setSelectedSymbol(newSymbol)
    setPriceHistory([])
    setIsSubscribed(false)
  }

  // 切换交易所
  const handleExchangeChange = (newExchange: string) => {
    if (isSubscribed) {
      unsubscribeMarketData(selectedExchange, selectedSymbol)
    }
    setSelectedExchange(newExchange)
    setPriceHistory([])
    setIsSubscribed(false)
  }

  const currentPrice = getPrice(selectedExchange, selectedSymbol)
  const currentOrderBook = getOrderBook(selectedExchange, selectedSymbol)

  // 计算价格变化
  const priceChange = priceHistory.length >= 2
    ? priceHistory[priceHistory.length - 1].price - priceHistory[priceHistory.length - 2].price
    : 0
  const priceChangePercent = priceHistory.length >= 2 && priceHistory[priceHistory.length - 2].price > 0
    ? (priceChange / priceHistory[priceHistory.length - 2].price) * 100
    : 0

  // 简单的价格趋势显示
  const renderPriceTrend = () => {
    if (priceHistory.length === 0) {
      return (
        <div style={{
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#999'
        }}>
          等待数据加载...
        </div>
      )
    }

    const maxPrice = Math.max(...priceHistory.map(p => p.price))
    const minPrice = Math.min(...priceHistory.map(p => p.price))
    const priceRange = maxPrice - minPrice

    return (
      <div style={{ height, padding: '20px', position: 'relative' }}>
        <svg width="100%" height="100%" style={{ border: '1px solid #f0f0f0' }}>
          {/* 价格线 */}
          <polyline
            fill="none"
            stroke={priceChange >= 0 ? '#52c41a' : '#ff4d4f'}
            strokeWidth="2"
            points={priceHistory.map((point, index) => {
              const x = (index / (priceHistory.length - 1)) * 100
              const y = 100 - ((point.price - minPrice) / priceRange) * 80
              return `${x}%,${y}%`
            }).join(' ')}
          />

          {/* 数据点 */}
          {priceHistory.map((point, index) => {
            const x = (index / (priceHistory.length - 1)) * 100
            const y = 100 - ((point.price - minPrice) / priceRange) * 80
            return (
              <circle
                key={index}
                cx={`${x}%`}
                cy={`${y}%`}
                r="2"
                fill={priceChange >= 0 ? '#52c41a' : '#ff4d4f'}
              />
            )
          })}
        </svg>

        {/* 价格标签 */}
        <div style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          fontSize: '12px',
          color: '#666'
        }}>
          最高: ${maxPrice.toFixed(2)}
        </div>
        <div style={{
          position: 'absolute',
          bottom: '10px',
          left: '10px',
          fontSize: '12px',
          color: '#666'
        }}>
          最低: ${minPrice.toFixed(2)}
        </div>
      </div>
    )
  }

  return (
    <div>
      {/* 控制面板 */}
      <Card size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col>
            <Space>
              <span>交易所:</span>
              <Select
                value={selectedExchange}
                onChange={handleExchangeChange}
                style={{ width: 120 }}
              >
                <Option value="binance">Binance</Option>
                <Option value="okx">OKX</Option>
                <Option value="bybit">Bybit</Option>
                <Option value="huobi">Huobi</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <span>交易对:</span>
              <Select
                value={selectedSymbol}
                onChange={handleSymbolChange}
                style={{ width: 120 }}
              >
                <Option value="BTCUSDT">BTC/USDT</Option>
                <Option value="ETHUSDT">ETH/USDT</Option>
                <Option value="BNBUSDT">BNB/USDT</Option>
                <Option value="ADAUSDT">ADA/USDT</Option>
                <Option value="SOLUSDT">SOL/USDT</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Tag color={isConnected ? 'green' : 'red'}>
              {isConnected ? '已连接' : '未连接'}
            </Tag>
          </Col>
        </Row>
      </Card>

      {/* 价格统计 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="当前价格"
              value={currentPrice?.price || 0}
              precision={2}
              prefix="$"
              valueStyle={{
                color: priceChange >= 0 ? '#52c41a' : '#ff4d4f'
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="24h变化"
              value={priceChangePercent}
              precision={2}
              suffix="%"
              valueStyle={{
                color: priceChangePercent >= 0 ? '#52c41a' : '#ff4d4f'
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="买一价"
              value={currentPrice?.bid || 0}
              precision={2}
              prefix="$"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="卖一价"
              value={currentPrice?.ask || 0}
              precision={2}
              prefix="$"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 价格图表 */}
      <Card title={`${selectedSymbol} 实时价格走势`}>
        {renderPriceTrend()}
      </Card>

      {/* 订单簿 */}
      {currentOrderBook && (
        <Row gutter={16} style={{ marginTop: '16px' }}>
          <Col span={12}>
            <Card title="买单" size="small">
              <div style={{ maxHeight: '200px', overflow: 'auto' }}>
                {currentOrderBook.bids?.slice(0, 10).map((bid, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    padding: '2px 0',
                    borderBottom: '1px solid #f0f0f0'
                  }}>
                    <span style={{ color: '#52c41a' }}>${bid[0]?.toFixed(2)}</span>
                    <span>{bid[1]?.toFixed(4)}</span>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="卖单" size="small">
              <div style={{ maxHeight: '200px', overflow: 'auto' }}>
                {currentOrderBook.asks?.slice(0, 10).map((ask, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    padding: '2px 0',
                    borderBottom: '1px solid #f0f0f0'
                  }}>
                    <span style={{ color: '#ff4d4f' }}>${ask[0]?.toFixed(2)}</span>
                    <span>{ask[1]?.toFixed(4)}</span>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        </Row>
      )}

      {/* 最近交易 */}
      <Card title="最近交易" size="small" style={{ marginTop: '16px' }}>
        <div style={{ maxHeight: '200px', overflow: 'auto' }}>
          {trades.slice(0, 20).map((trade, index) => (
            <div key={index} style={{
              display: 'flex',
              justifyContent: 'space-between',
              padding: '4px 0',
              borderBottom: '1px solid #f0f0f0'
            }}>
              <span>{new Date(trade.timestamp).toLocaleTimeString()}</span>
              <span style={{ color: trade.side === 'buy' ? '#52c41a' : '#ff4d4f' }}>
                ${trade.price?.toFixed(2)} × {trade.amount?.toFixed(4)}
              </span>
              <Tag color={trade.side === 'buy' ? 'green' : 'red'}>
                {trade.side.toUpperCase()}
              </Tag>
            </div>
          ))}
        </div>
      </Card>
    </div>
  )
}

export default MarketDataChart
