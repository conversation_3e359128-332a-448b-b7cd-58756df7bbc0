import React from 'react';
import { Card, Form, Input, Button, Switch, Select, Row, Col, message, Divider } from 'antd';
import { SaveOutlined, ReloadOutlined } from '@ant-design/icons';

const { Option } = Select;

const Settings: React.FC = () => {
  const [form] = Form.useForm();

  const handleSave = async (values: any) => {
    try {
      console.log('Settings saved:', values);
      message.success('设置保存成功');
    } catch (error) {
      message.error('设置保存失败');
    }
  };

  const handleReset = () => {
    form.resetFields();
    message.info('设置已重置');
  };

  return (
    <div>
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <h1 style={{ margin: 0, fontSize: 24, fontWeight: 'bold' }}>
            ⚙️ 系统设置
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            配置系统参数和交易所连接
          </p>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="🏦 交易所配置">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              initialValues={{
                binanceEnabled: true,
                binanceSandbox: true,
                okxEnabled: true,
                okxSandbox: true,
                notifications: true,
                autoRestart: false
              }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <h3>Binance 配置</h3>
                  <Form.Item name="binanceEnabled" valuePropName="checked">
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                  </Form.Item>
                  <Form.Item name="binanceApiKey" label="API Key">
                    <Input.Password placeholder="请输入 Binance API Key" />
                  </Form.Item>
                  <Form.Item name="binanceSecretKey" label="Secret Key">
                    <Input.Password placeholder="请输入 Binance Secret Key" />
                  </Form.Item>
                  <Form.Item name="binanceSandbox" valuePropName="checked">
                    <Switch checkedChildren="沙盒模式" unCheckedChildren="生产模式" />
                  </Form.Item>
                </Col>
                
                <Col span={12}>
                  <h3>OKX 配置</h3>
                  <Form.Item name="okxEnabled" valuePropName="checked">
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                  </Form.Item>
                  <Form.Item name="okxApiKey" label="API Key">
                    <Input.Password placeholder="请输入 OKX API Key" />
                  </Form.Item>
                  <Form.Item name="okxSecretKey" label="Secret Key">
                    <Input.Password placeholder="请输入 OKX Secret Key" />
                  </Form.Item>
                  <Form.Item name="okxPassphrase" label="Passphrase">
                    <Input.Password placeholder="请输入 OKX Passphrase" />
                  </Form.Item>
                  <Form.Item name="okxSandbox" valuePropName="checked">
                    <Switch checkedChildren="沙盒模式" unCheckedChildren="生产模式" />
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <h3>系统设置</h3>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name="notifications" valuePropName="checked" label="推送通知">
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="autoRestart" valuePropName="checked" label="自动重启">
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="logLevel" label="日志级别">
                    <Select defaultValue="info">
                      <Option value="debug">Debug</Option>
                      <Option value="info">Info</Option>
                      <Option value="warn">Warning</Option>
                      <Option value="error">Error</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  保存设置
                </Button>
                <Button style={{ marginLeft: 8 }} onClick={handleReset} icon={<ReloadOutlined />}>
                  重置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Settings;
