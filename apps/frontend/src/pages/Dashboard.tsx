import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Button, message, Progress, Tag, Space, Alert } from 'antd';
import {
  RocketOutlined,
  CalculatorOutlined,
  TrophyOutlined,
  DollarOutlined,
  PercentageOutlined,
  LineChartOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  WifiOutlined,
  DisconnectOutlined,
  RobotOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { TechnicalIndicators } from '../services/indicators/TechnicalIndicators';
import { ApiService } from '../services/api';
import { useStrategyManager } from '../hooks/useStrategyManager';
import { useRealTimeData } from '../hooks/useRealTimeData';

const Dashboard: React.FC = () => {
  const [indicators, setIndicators] = useState<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [systemHealth, setSystemHealth] = useState<any>(null);
  const [exchangeStatus, setExchangeStatus] = useState<any>(null);

  // 使用策略管理 Hook
  const {
    strategies,
    stats,
    getPerformanceStats
  } = useStrategyManager();

  // 使用实时数据 Hook
  const {
    isConnected,
    prices,
    trades,
    strategySignals,
    connectionError
  } = useRealTimeData();

  // 示例价格数据
  const samplePrices = [
    50000, 51000, 49000, 52000, 48000, 53000, 47000, 54000, 46000, 55000,
    45000, 56000, 44000, 57000, 43000, 58000, 42000, 59000, 41000, 60000
  ];

  const calculateIndicators = async () => {
    setIsCalculating(true);
    try {
      const calculator = new TechnicalIndicators();

      const startTime = performance.now();

      const results = {
        sma: calculator.sma(samplePrices, 5),
        ema: calculator.ema(samplePrices, 5),
        rsi: calculator.rsi(samplePrices, 14),
        macd: calculator.macd(samplePrices, 12, 26, 9)
      };

      const endTime = performance.now();
      const calculationTime = endTime - startTime;

      setIndicators({
        ...results,
        calculationTime: calculationTime.toFixed(2)
      });

      message.success(`技术指标计算完成！耗时: ${calculationTime.toFixed(2)}ms`);
    } catch (error) {
      message.error('计算失败');
      console.error(error);
    } finally {
      setIsCalculating(false);
    }
  };

  const loadSystemStatus = async () => {
    try {
      // 获取系统健康状态
      const healthResponse = await ApiService.getSystemHealth();
      setSystemHealth(healthResponse);

      // 获取交易所状态
      const exchangeResponse = await ApiService.getExchangeStatus();
      setExchangeStatus(exchangeResponse);
    } catch (error) {
      console.error('Failed to load system status:', error);
    }
  };

  useEffect(() => {
    calculateIndicators();
    loadSystemStatus();
  }, []);

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'ok': return '#52c41a';
      case 'degraded': return '#faad14';
      case 'critical': return '#f5222d';
      default: return '#d9d9d9';
    }
  };

  const performanceStats = getPerformanceStats();

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Space>
            <RocketOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
            <h1 style={{ margin: 0, fontSize: 24, fontWeight: 'bold' }}>
              SFQuant 量化交易平台
            </h1>
            <Tag color={isConnected ? 'green' : 'red'} icon={isConnected ? <WifiOutlined /> : <DisconnectOutlined />}>
              {isConnected ? '实时连接' : '连接断开'}
            </Tag>
          </Space>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            智能量化交易系统 - 实时监控与策略管理
          </p>
          {connectionError && (
            <Alert
              message="WebSocket 连接错误"
              description={connectionError}
              type="error"
              showIcon
              closable
              style={{ marginTop: '16px' }}
            />
          )}
        </Col>
      </Row>

      {/* 核心指标概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总策略数"
              value={stats?.total || 0}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中策略"
              value={stats?.running || 0}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总收益率"
              value={performanceStats.totalReturn}
              precision={2}
              suffix="%"
              prefix={<DollarOutlined />}
              valueStyle={{
                color: performanceStats.totalReturn >= 0 ? '#52c41a' : '#ff4d4f'
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="系统状态"
              value={systemHealth?.status || 'unknown'}
              prefix={
                systemHealth?.status === 'healthy' ?
                <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
                <ExclamationCircleOutlined style={{ color: '#faad14' }} />
              }
              valueStyle={{
                color: getHealthColor(systemHealth?.status),
                textTransform: 'uppercase'
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 实时数据统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="实时价格数据"
              value={prices.length}
              prefix={<LineChartOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="交易记录"
              value={trades.length}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="策略信号"
              value={strategySignals.length}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均胜率"
              value={performanceStats.averageWinRate}
              precision={1}
              suffix="%"
              prefix={<PercentageOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 交易所状态 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="🏦 交易所连接状态">
            <Space size="large">
              {exchangeStatus?.data && Object.entries(exchangeStatus.data).map(([exchange, info]: [string, any]) => (
                <div key={exchange} style={{ textAlign: 'center' }}>
                  <div style={{ marginBottom: 8 }}>
                    <Tag color={info.status === 'configured' ? 'green' : 'red'}>
                      {exchange.toUpperCase()}
                    </Tag>
                  </div>
                  <div style={{ fontSize: 12, color: '#666' }}>
                    {info.sandbox ? '沙盒模式' : '生产模式'}
                  </div>
                </div>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 技术指标计算演示 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card
            title="🧮 技术指标计算演示"
            extra={
              <Button
                type="primary"
                icon={<CalculatorOutlined />}
                loading={isCalculating}
                onClick={calculateIndicators}
              >
                重新计算
              </Button>
            }
          >
            {indicators && (
              <Row gutter={[16, 16]}>
                <Col span={6}>
                  <Statistic
                    title="SMA (5期)"
                    value={indicators.sma[indicators.sma.length - 1]?.toFixed(2) || 0}
                    prefix="$"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="EMA (5期)"
                    value={indicators.ema[indicators.ema.length - 1]?.toFixed(2) || 0}
                    prefix="$"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="RSI (14期)"
                    value={indicators.rsi[indicators.rsi.length - 1]?.toFixed(2) || 0}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="计算耗时"
                    value={indicators.calculationTime}
                    suffix="ms"
                    valueStyle={{ color: '#fa8c16' }}
                  />
                </Col>
              </Row>
            )}
          </Card>
        </Col>
      </Row>

      {/* 最近活动 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="📊 最近策略信号" size="small">
            <div style={{ maxHeight: '300px', overflow: 'auto' }}>
              {strategySignals.slice(0, 10).map((signal, index) => (
                <div key={index} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  <Space>
                    <Tag color={signal.action === 'buy' ? 'green' : signal.action === 'sell' ? 'red' : 'default'}>
                      {signal.action.toUpperCase()}
                    </Tag>
                    <span>{signal.symbol}</span>
                    <span style={{ fontSize: '12px', color: '#666' }}>
                      置信度: {signal.confidence}%
                    </span>
                  </Space>
                  <span style={{ fontSize: '12px', color: '#666' }}>
                    {new Date(signal.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              ))}
              {strategySignals.length === 0 && (
                <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                  暂无策略信号
                </div>
              )}
            </div>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="💹 最近交易" size="small">
            <div style={{ maxHeight: '300px', overflow: 'auto' }}>
              {trades.slice(0, 10).map((trade, index) => (
                <div key={index} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  <Space>
                    <Tag color={trade.side === 'buy' ? 'green' : 'red'}>
                      {trade.side.toUpperCase()}
                    </Tag>
                    <span>{trade.symbol}</span>
                    <span style={{ fontSize: '12px', color: '#666' }}>
                      {trade.exchange}
                    </span>
                  </Space>
                  <Space>
                    <span>${trade.price?.toFixed(2)}</span>
                    <span style={{ fontSize: '12px', color: '#666' }}>
                      {new Date(trade.timestamp).toLocaleTimeString()}
                    </span>
                  </Space>
                </div>
              ))}
              {trades.length === 0 && (
                <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                  暂无交易记录
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 功能特性 */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="📱 PWA 功能">
            <ul style={{ textAlign: 'left', paddingLeft: 20 }}>
              <li>✅ Service Worker 缓存</li>
              <li>✅ 离线访问支持</li>
              <li>✅ 应用安装提示</li>
              <li>✅ 自动更新机制</li>
              <li>✅ 推送通知支持</li>
            </ul>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="⚡ 性能特性">
            <ul style={{ textAlign: 'left', paddingLeft: 20 }}>
              <li>🧮 JavaScript 技术指标计算</li>
              <li>🔄 Web Workers 多线程处理</li>
              <li>💾 IndexedDB 本地存储</li>
              <li>📡 WebSocket 实时数据</li>
              <li>🎯 React Query 状态管理</li>
            </ul>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
