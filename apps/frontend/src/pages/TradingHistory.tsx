import React, { useState } from 'react';
import { Card, Table, Tag, DatePicker, Select, Button, Space, Row, Col } from 'antd';
import { SearchOutlined, DownloadOutlined } from '@ant-design/icons';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface Trade {
  id: string;
  symbol: string;
  type: 'buy' | 'sell';
  amount: number;
  price: number;
  total: number;
  strategy: string;
  timestamp: number;
  status: 'completed' | 'pending' | 'failed';
}

const TradingHistory: React.FC = () => {
  const [trades] = useState<Trade[]>([
    {
      id: '1',
      symbol: 'BTC/USDT',
      type: 'buy',
      amount: 0.1,
      price: 52000,
      total: 5200,
      strategy: '套利策略',
      timestamp: Date.now() - 3600000,
      status: 'completed'
    },
    {
      id: '2',
      symbol: 'ETH/USDT',
      type: 'sell',
      amount: 2.5,
      price: 3200,
      total: 8000,
      strategy: 'AI策略',
      timestamp: Date.now() - 7200000,
      status: 'completed'
    }
  ]);

  const columns = [
    {
      title: '交易ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '交易对',
      dataIndex: 'symbol',
      key: 'symbol',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === 'buy' ? 'green' : 'red'}>
          {type === 'buy' ? '买入' : '卖出'}
        </Tag>
      )
    },
    {
      title: '数量',
      dataIndex: 'amount',
      key: 'amount',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `$${price.toLocaleString()}`
    },
    {
      title: '总额',
      dataIndex: 'total',
      key: 'total',
      render: (total: number) => `$${total.toLocaleString()}`
    },
    {
      title: '策略',
      dataIndex: 'strategy',
      key: 'strategy',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors = {
          completed: 'green',
          pending: 'orange',
          failed: 'red'
        };
        const labels = {
          completed: '已完成',
          pending: '进行中',
          failed: '失败'
        };
        return <Tag color={colors[status as keyof typeof colors]}>{labels[status as keyof typeof labels]}</Tag>;
      }
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) => new Date(timestamp).toLocaleString()
    }
  ];

  return (
    <div>
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <h1 style={{ margin: 0, fontSize: 24, fontWeight: 'bold' }}>
            📈 交易历史
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            查看和分析历史交易记录
          </p>
        </Col>
      </Row>

      <Card
        title="交易记录"
        extra={
          <Space>
            <RangePicker />
            <Select defaultValue="all" style={{ width: 120 }}>
              <Option value="all">全部策略</Option>
              <Option value="arbitrage">套利策略</Option>
              <Option value="ai">AI策略</Option>
            </Select>
            <Button icon={<SearchOutlined />}>搜索</Button>
            <Button icon={<DownloadOutlined />}>导出</Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={trades}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>
    </div>
  );
};

export default TradingHistory;
