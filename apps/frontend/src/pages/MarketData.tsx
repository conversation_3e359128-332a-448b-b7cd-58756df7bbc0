import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Select, Table, Tag, Button, Space, message, Statistic, Tabs } from 'antd';
import { ReloadOutlined, WifiOutlined, DisconnectOutlined, LineChartOutlined } from '@ant-design/icons';
import { useRealTimeData } from '../hooks/useRealTimeData';
import MarketDataChart from '../components/charts/MarketDataChart';

const { Option } = Select;
const { TabPane } = Tabs;

interface MarketDataItem {
  symbol: string;
  price: number;
  change24h: number;
  volume: number;
  timestamp: number;
}

const MarketData: React.FC = () => {
  const {
    isConnected,
    prices,
    orderBooks,
    trades,
    connectionError,
    subscribeMarketData,
    unsubscribeMarketData,
    connect,
    disconnect
  } = useRealTimeData();

  const [selectedExchange, setSelectedExchange] = useState('binance');
  const [selectedSymbol, setSelectedSymbol] = useState('BTCUSDT');
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);

  // 订阅市场数据
  useEffect(() => {
    if (isConnected) {
      // 订阅一些热门交易对
      const popularPairs = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT'];
      popularPairs.forEach(symbol => {
        subscribeMarketData(selectedExchange, symbol);
      });
    }
  }, [isConnected, selectedExchange, subscribeMarketData]);

  const columns = [
    {
      title: '交易对',
      dataIndex: 'symbol',
      key: 'symbol',
      render: (symbol: string) => (
        <span style={{ fontWeight: 'bold' }}>{symbol}</span>
      )
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => (
        <span style={{ fontFamily: 'monospace' }}>
          ${price.toFixed(2)}
        </span>
      )
    },
    {
      title: '24h 变化',
      dataIndex: 'change24h',
      key: 'change24h',
      render: (change: number) => (
        <Tag color={change >= 0 ? 'green' : 'red'}>
          {change >= 0 ? '+' : ''}{change.toFixed(2)}%
        </Tag>
      )
    },
    {
      title: '24h 成交量',
      dataIndex: 'volume',
      key: 'volume',
      render: (volume: number) => (
        <span style={{ fontFamily: 'monospace' }}>
          {volume.toLocaleString()}
        </span>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) => (
        <span style={{ color: '#666', fontSize: '12px' }}>
          {new Date(timestamp).toLocaleTimeString()}
        </span>
      )
    }
  ];

  // 转换实时数据为表格格式
  const marketData: MarketDataItem[] = prices.map(price => ({
    symbol: price.symbol,
    price: price.price,
    change24h: price.change24h,
    volume: price.volume,
    timestamp: price.timestamp
  }));

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Space>
            <LineChartOutlined style={{ fontSize: '24px' }} />
            <h1 style={{ margin: 0, fontSize: 24, fontWeight: 'bold' }}>
              实时市场数据
            </h1>
            <Tag color={isConnected ? 'green' : 'red'}>
              {isConnected ? '实时连接' : '连接断开'}
            </Tag>
          </Space>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            实时市场数据监控与分析
          </p>
          {connectionError && (
            <div style={{
              marginTop: '8px',
              padding: '8px',
              background: '#fff2f0',
              border: '1px solid #ffccc7',
              borderRadius: '4px',
              color: '#a8071a'
            }}>
              连接错误: {connectionError}
            </div>
          )}
        </Col>
      </Row>

      {/* 连接状态 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="WebSocket 状态"
              value={isConnected ? '已连接' : '未连接'}
              prefix={isConnected ? <WifiOutlined /> : <DisconnectOutlined />}
              valueStyle={{
                color: isConnected ? '#52c41a' : '#f5222d'
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="监控交易对"
              value={prices.length}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="实时交易"
              value={trades.length}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="订单簿数据"
              value={orderBooks.length}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="数据概览" key="overview">
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col span={24}>
              <Card title="📈 市场数据控制面板">
                <Space size="middle" style={{ marginBottom: 16 }}>
                  <span>交易所:</span>
                  <Select
                    value={selectedExchange}
                    onChange={setSelectedExchange}
                    style={{ width: 120 }}
                  >
                    <Option value="binance">Binance</Option>
                    <Option value="okx">OKX</Option>
                    <Option value="bybit">Bybit</Option>
                    <Option value="huobi">Huobi</Option>
                  </Select>

                  <span>交易对:</span>
                  <Select
                    value={selectedSymbol}
                    onChange={setSelectedSymbol}
                    style={{ width: 120 }}
                  >
                    <Option value="BTCUSDT">BTC/USDT</Option>
                    <Option value="ETHUSDT">ETH/USDT</Option>
                    <Option value="BNBUSDT">BNB/USDT</Option>
                    <Option value="ADAUSDT">ADA/USDT</Option>
                  </Select>

                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    loading={loading}
                    onClick={() => window.location.reload()}
                  >
                    刷新页面
                  </Button>

                  <Button
                    type={isConnected ? 'default' : 'primary'}
                    icon={isConnected ? <DisconnectOutlined /> : <WifiOutlined />}
                    onClick={isConnected ? disconnect : connect}
                  >
                    {isConnected ? '断开连接' : '重新连接'}
                  </Button>
                </Space>
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="💹 实时行情">
                <Table
                  columns={columns}
                  dataSource={marketData}
                  rowKey="symbol"
                  pagination={false}
                  size="middle"
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="价格图表" key="charts">
          <MarketDataChart symbol={selectedSymbol} exchange={selectedExchange} />
        </TabPane>

        <TabPane tab="多币种监控" key="multi">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <MarketDataChart symbol="BTCUSDT" exchange={selectedExchange} height={300} />
            </Col>
            <Col span={12}>
              <MarketDataChart symbol="ETHUSDT" exchange={selectedExchange} height={300} />
            </Col>
            <Col span={12}>
              <MarketDataChart symbol="BNBUSDT" exchange={selectedExchange} height={300} />
            </Col>
            <Col span={12}>
              <MarketDataChart symbol="ADAUSDT" exchange={selectedExchange} height={300} />
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="实时数据流" key="stream">
          <Card title="实时价格数据流">
            <div style={{ maxHeight: '600px', overflow: 'auto' }}>
              {prices.length > 0 ? (
                prices.map((price, index) => (
                  <div key={`${price.exchange}-${price.symbol}-${index}`} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 12px',
                    borderBottom: '1px solid #f0f0f0',
                    background: index % 2 === 0 ? '#fafafa' : 'white'
                  }}>
                    <Space>
                      <Tag color="blue">{price.exchange}</Tag>
                      <span style={{ fontWeight: 'bold' }}>{price.symbol}</span>
                    </Space>
                    <Space>
                      <span style={{
                        color: price.change24h >= 0 ? '#52c41a' : '#ff4d4f',
                        fontWeight: 'bold'
                      }}>
                        ${price.price?.toFixed(2)}
                      </span>
                      <span style={{
                        color: price.change24h >= 0 ? '#52c41a' : '#ff4d4f',
                        fontSize: '12px'
                      }}>
                        {price.change24h >= 0 ? '+' : ''}{price.change24h?.toFixed(2)}%
                      </span>
                      <span style={{ color: '#666', fontSize: '12px' }}>
                        {new Date(price.timestamp).toLocaleTimeString()}
                      </span>
                    </Space>
                  </div>
                ))
              ) : (
                <div style={{
                  textAlign: 'center',
                  padding: '40px',
                  color: '#999'
                }}>
                  {isConnected ? '等待数据...' : '请检查网络连接'}
                </div>
              )}
            </div>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default MarketData;
