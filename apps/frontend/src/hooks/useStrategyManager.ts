import { useState, useEffect, useCallback } from 'react'
import { ApiService } from '../services/api'
import { message } from 'antd'

export interface Strategy {
  id: string
  name: string
  type: 'arbitrage' | 'grid' | 'trend' | 'dca' | 'ai'
  status: 'running' | 'stopped' | 'error'
  config: any
  createdAt: string
  lastUpdate: string
  performance?: {
    totalReturn: number
    totalTrades: number
    winRate: number
  }
  strategySpecific?: any
}

export interface StrategyStats {
  total: number
  running: number
  stopped: number
  error: number
  byType: {
    arbitrage: number
    grid: number
    trend: number
    dca: number
    ai: number
  }
  strategies: Strategy[]
}

export interface ArbitrageConfig {
  name: string
  symbol: string
  exchanges: string[]
  minProfitPercent: number
  maxPositionSize?: number
  checkInterval?: number
}

export interface GridConfig {
  name: string
  symbol: string
  exchange: string
  basePrice: number
  gridSpacing: number
  gridLevels: number
  orderAmount: number
  upperLimit?: number
  lowerLimit?: number
}

export function useStrategyManager() {
  const [strategies, setStrategies] = useState<Strategy[]>([])
  const [stats, setStats] = useState<StrategyStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 获取所有策略
  const fetchStrategies = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await ApiService.getStrategyManager()
      if (response.success) {
        setStats(response.data)
        setStrategies(response.data.strategies || [])
      } else {
        throw new Error(response.message || '获取策略失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取策略失败'
      setError(errorMessage)
      message.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  // 获取运行中的策略
  const fetchRunningStrategies = useCallback(async () => {
    try {
      const response = await ApiService.getRunningStrategies()
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || '获取运行中策略失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取运行中策略失败'
      message.error(errorMessage)
      return []
    }
  }, [])

  // 创建套利策略
  const createArbitrageStrategy = useCallback(async (config: ArbitrageConfig) => {
    try {
      setLoading(true)
      
      const response = await ApiService.createArbitrageStrategy(config)
      if (response.success) {
        message.success(`套利策略 "${config.name}" 创建成功`)
        await fetchStrategies() // 刷新策略列表
        return response.data
      } else {
        throw new Error(response.message || '创建套利策略失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建套利策略失败'
      setError(errorMessage)
      message.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [fetchStrategies])

  // 创建网格策略
  const createGridStrategy = useCallback(async (config: GridConfig) => {
    try {
      setLoading(true)
      
      const response = await ApiService.createGridStrategy(config)
      if (response.success) {
        message.success(`网格策略 "${config.name}" 创建成功`)
        await fetchStrategies() // 刷新策略列表
        return response.data
      } else {
        throw new Error(response.message || '创建网格策略失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建网格策略失败'
      setError(errorMessage)
      message.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [fetchStrategies])

  // 启动策略
  const startStrategy = useCallback(async (strategyId: string) => {
    try {
      const response = await ApiService.startStrategyManager(strategyId)
      if (response.success) {
        message.success('策略启动成功')
        await fetchStrategies() // 刷新策略列表
        return true
      } else {
        throw new Error(response.message || '启动策略失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '启动策略失败'
      message.error(errorMessage)
      return false
    }
  }, [fetchStrategies])

  // 停止策略
  const stopStrategy = useCallback(async (strategyId: string) => {
    try {
      const response = await ApiService.stopStrategyManager(strategyId)
      if (response.success) {
        message.success('策略停止成功')
        await fetchStrategies() // 刷新策略列表
        return true
      } else {
        throw new Error(response.message || '停止策略失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '停止策略失败'
      message.error(errorMessage)
      return false
    }
  }, [fetchStrategies])

  // 删除策略
  const deleteStrategy = useCallback(async (strategyId: string) => {
    try {
      const response = await ApiService.deleteStrategyManager(strategyId)
      if (response.success) {
        message.success('策略删除成功')
        await fetchStrategies() // 刷新策略列表
        return true
      } else {
        throw new Error(response.message || '删除策略失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除策略失败'
      message.error(errorMessage)
      return false
    }
  }, [fetchStrategies])

  // 获取策略详情
  const getStrategyDetails = useCallback(async (strategyId: string) => {
    try {
      const response = await ApiService.getStrategyManagerDetails(strategyId)
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || '获取策略详情失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取策略详情失败'
      message.error(errorMessage)
      return null
    }
  }, [])

  // 批量操作
  const startAllStrategies = useCallback(async () => {
    const stoppedStrategies = strategies.filter(s => s.status === 'stopped')
    const results = await Promise.allSettled(
      stoppedStrategies.map(s => startStrategy(s.id))
    )
    
    const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length
    message.info(`批量启动完成: ${successCount}/${stoppedStrategies.length} 个策略启动成功`)
  }, [strategies, startStrategy])

  const stopAllStrategies = useCallback(async () => {
    const runningStrategies = strategies.filter(s => s.status === 'running')
    const results = await Promise.allSettled(
      runningStrategies.map(s => stopStrategy(s.id))
    )
    
    const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length
    message.info(`批量停止完成: ${successCount}/${runningStrategies.length} 个策略停止成功`)
  }, [strategies, stopStrategy])

  // 过滤和搜索
  const getStrategiesByType = useCallback((type: string) => {
    return strategies.filter(s => s.type === type)
  }, [strategies])

  const getStrategiesByStatus = useCallback((status: string) => {
    return strategies.filter(s => s.status === status)
  }, [strategies])

  const searchStrategies = useCallback((keyword: string) => {
    if (!keyword.trim()) return strategies
    
    const lowerKeyword = keyword.toLowerCase()
    return strategies.filter(s => 
      s.name.toLowerCase().includes(lowerKeyword) ||
      s.type.toLowerCase().includes(lowerKeyword) ||
      s.id.toLowerCase().includes(lowerKeyword)
    )
  }, [strategies])

  // 统计信息
  const getPerformanceStats = useCallback(() => {
    const strategiesWithPerformance = strategies.filter(s => s.performance)
    
    if (strategiesWithPerformance.length === 0) {
      return {
        totalReturn: 0,
        totalTrades: 0,
        averageWinRate: 0,
        profitableStrategies: 0
      }
    }

    const totalReturn = strategiesWithPerformance.reduce((sum, s) => sum + (s.performance?.totalReturn || 0), 0)
    const totalTrades = strategiesWithPerformance.reduce((sum, s) => sum + (s.performance?.totalTrades || 0), 0)
    const averageWinRate = strategiesWithPerformance.reduce((sum, s) => sum + (s.performance?.winRate || 0), 0) / strategiesWithPerformance.length
    const profitableStrategies = strategiesWithPerformance.filter(s => (s.performance?.totalReturn || 0) > 0).length

    return {
      totalReturn,
      totalTrades,
      averageWinRate,
      profitableStrategies
    }
  }, [strategies])

  // 自动刷新
  useEffect(() => {
    fetchStrategies()
    
    // 设置定时刷新
    const interval = setInterval(fetchStrategies, 30000) // 每30秒刷新一次
    
    return () => clearInterval(interval)
  }, [fetchStrategies])

  return {
    // 数据状态
    strategies,
    stats,
    loading,
    error,
    
    // 基础操作
    fetchStrategies,
    fetchRunningStrategies,
    getStrategyDetails,
    
    // 策略创建
    createArbitrageStrategy,
    createGridStrategy,
    
    // 策略控制
    startStrategy,
    stopStrategy,
    deleteStrategy,
    
    // 批量操作
    startAllStrategies,
    stopAllStrategies,
    
    // 过滤和搜索
    getStrategiesByType,
    getStrategiesByStatus,
    searchStrategies,
    
    // 统计信息
    getPerformanceStats
  }
}
