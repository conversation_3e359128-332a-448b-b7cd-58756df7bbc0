import { useState, useEffect, useCallback, useRef } from 'react'
import { WebSocketService } from '../services/api'

export interface RealTimePrice {
  symbol: string
  exchange: string
  price: number
  bid: number
  ask: number
  volume: number
  change24h: number
  timestamp: number
}

export interface RealTimeOrderBook {
  symbol: string
  exchange: string
  bids: [number, number][]
  asks: [number, number][]
  timestamp: number
}

export interface RealTimeTrade {
  symbol: string
  exchange: string
  price: number
  amount: number
  side: 'buy' | 'sell'
  timestamp: number
}

export interface StrategySignal {
  strategyId: string
  action: 'buy' | 'sell' | 'hold'
  symbol: string
  amount: number
  price?: number
  reason: string
  confidence: number
  timestamp: number
}

export interface UseRealTimeDataOptions {
  autoConnect?: boolean
  reconnectAttempts?: number
  reconnectInterval?: number
}

export function useRealTimeData(options: UseRealTimeDataOptions = {}) {
  const {
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectInterval = 5000
  } = options

  // WebSocket 实例
  const wsRef = useRef<WebSocketService | null>(null)
  
  // 连接状态
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  
  // 实时数据状态
  const [prices, setPrices] = useState<Map<string, RealTimePrice>>(new Map())
  const [orderBooks, setOrderBooks] = useState<Map<string, RealTimeOrderBook>>(new Map())
  const [trades, setTrades] = useState<RealTimeTrade[]>([])
  const [strategySignals, setStrategySignals] = useState<StrategySignal[]>([])
  
  // 订阅状态
  const [subscriptions, setSubscriptions] = useState<Set<string>>(new Set())

  // 初始化 WebSocket 连接
  const connect = useCallback(async () => {
    try {
      if (wsRef.current) {
        wsRef.current.disconnect()
      }

      wsRef.current = new WebSocketService('/ws')
      
      // 设置事件监听
      wsRef.current.onopen = () => {
        console.log('🔗 实时数据 WebSocket 已连接')
        setIsConnected(true)
        setConnectionError(null)
      }

      wsRef.current.onclose = () => {
        console.log('🔌 实时数据 WebSocket 已断开')
        setIsConnected(false)
      }

      wsRef.current.onerror = (error) => {
        console.error('❌ 实时数据 WebSocket 错误:', error)
        setConnectionError(error.message || '连接错误')
        setIsConnected(false)
      }

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          handleWebSocketMessage(data)
        } catch (error) {
          console.error('❌ 解析 WebSocket 消息失败:', error)
        }
      }

      await wsRef.current.connect()
      
    } catch (error) {
      console.error('❌ WebSocket 连接失败:', error)
      setConnectionError(error instanceof Error ? error.message : '连接失败')
      setIsConnected(false)
    }
  }, [])

  // 断开连接
  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.disconnect()
      wsRef.current = null
    }
    setIsConnected(false)
    setSubscriptions(new Set())
  }, [])

  // 处理 WebSocket 消息
  const handleWebSocketMessage = useCallback((data: any) => {
    const { type, data: payload } = data

    switch (type) {
      case 'price_update':
        if (payload) {
          const priceData = payload as RealTimePrice
          const key = `${priceData.exchange}:${priceData.symbol}`
          setPrices(prev => new Map(prev.set(key, priceData)))
        }
        break

      case 'orderbook_update':
        if (payload) {
          const orderBookData = payload as RealTimeOrderBook
          const key = `${orderBookData.exchange}:${orderBookData.symbol}`
          setOrderBooks(prev => new Map(prev.set(key, orderBookData)))
        }
        break

      case 'trade_update':
        if (payload) {
          const tradeData = payload as RealTimeTrade
          setTrades(prev => [tradeData, ...prev.slice(0, 99)]) // 保留最近100条交易
        }
        break

      case 'execution_signal':
        if (payload) {
          const signal = payload as StrategySignal
          setStrategySignals(prev => [signal, ...prev.slice(0, 49)]) // 保留最近50个信号
        }
        break

      case 'execution_completed':
        console.log('✅ 策略执行完成:', payload)
        break

      case 'pong':
        // 心跳响应
        break

      default:
        console.log('📨 未知消息类型:', type, payload)
    }
  }, [])

  // 订阅市场数据
  const subscribeMarketData = useCallback((exchange: string, symbol: string) => {
    if (!wsRef.current || !isConnected) {
      console.warn('⚠️ WebSocket 未连接，无法订阅')
      return
    }

    const subscription = `market-data:${exchange}:${symbol}`
    
    wsRef.current.send({
      type: 'subscribe',
      channel: 'market-data',
      data: { exchange, symbol }
    })

    setSubscriptions(prev => new Set(prev.add(subscription)))
    console.log(`📊 已订阅市场数据: ${exchange}:${symbol}`)
  }, [isConnected])

  // 取消订阅市场数据
  const unsubscribeMarketData = useCallback((exchange: string, symbol: string) => {
    if (!wsRef.current || !isConnected) {
      return
    }

    const subscription = `market-data:${exchange}:${symbol}`
    
    wsRef.current.send({
      type: 'unsubscribe',
      channel: 'market-data',
      data: { exchange, symbol }
    })

    setSubscriptions(prev => {
      const newSet = new Set(prev)
      newSet.delete(subscription)
      return newSet
    })
    console.log(`📊 已取消订阅市场数据: ${exchange}:${symbol}`)
  }, [isConnected])

  // 订阅交易信号
  const subscribeTrading = useCallback(() => {
    if (!wsRef.current || !isConnected) {
      console.warn('⚠️ WebSocket 未连接，无法订阅')
      return
    }

    wsRef.current.send({
      type: 'subscribe',
      channel: 'trading'
    })

    setSubscriptions(prev => new Set(prev.add('trading')))
    console.log('🎯 已订阅交易信号')
  }, [isConnected])

  // 取消订阅交易信号
  const unsubscribeTrading = useCallback(() => {
    if (!wsRef.current || !isConnected) {
      return
    }

    wsRef.current.send({
      type: 'unsubscribe',
      channel: 'trading'
    })

    setSubscriptions(prev => {
      const newSet = new Set(prev)
      newSet.delete('trading')
      return newSet
    })
    console.log('🎯 已取消订阅交易信号')
  }, [isConnected])

  // 发送心跳
  const sendHeartbeat = useCallback(() => {
    if (wsRef.current && isConnected) {
      wsRef.current.send({ type: 'ping' })
    }
  }, [isConnected])

  // 获取特定交易对的价格
  const getPrice = useCallback((exchange: string, symbol: string): RealTimePrice | null => {
    const key = `${exchange}:${symbol}`
    return prices.get(key) || null
  }, [prices])

  // 获取特定交易对的订单簿
  const getOrderBook = useCallback((exchange: string, symbol: string): RealTimeOrderBook | null => {
    const key = `${exchange}:${symbol}`
    return orderBooks.get(key) || null
  }, [orderBooks])

  // 清理数据
  const clearData = useCallback(() => {
    setPrices(new Map())
    setOrderBooks(new Map())
    setTrades([])
    setStrategySignals([])
  }, [])

  // 自动连接
  useEffect(() => {
    if (autoConnect) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [autoConnect, connect, disconnect])

  // 心跳定时器
  useEffect(() => {
    if (!isConnected) return

    const heartbeatInterval = setInterval(sendHeartbeat, 30000) // 每30秒发送心跳

    return () => {
      clearInterval(heartbeatInterval)
    }
  }, [isConnected, sendHeartbeat])

  return {
    // 连接状态
    isConnected,
    connectionError,
    
    // 数据状态
    prices: Array.from(prices.values()),
    orderBooks: Array.from(orderBooks.values()),
    trades,
    strategySignals,
    subscriptions: Array.from(subscriptions),
    
    // 连接控制
    connect,
    disconnect,
    
    // 订阅控制
    subscribeMarketData,
    unsubscribeMarketData,
    subscribeTrading,
    unsubscribeTrading,
    
    // 数据获取
    getPrice,
    getOrderBook,
    
    // 工具方法
    clearData,
    sendHeartbeat
  }
}
