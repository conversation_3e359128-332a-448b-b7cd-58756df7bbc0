import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import Layout from './components/layout/Layout';
import Dashboard from './pages/Dashboard';
import StrategyManagement from './pages/StrategyManagement';
import MarketData from './pages/MarketData';
import TradingHistory from './pages/TradingHistory';
import Settings from './pages/Settings';
import './App.css';

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/strategies" element={<StrategyManagement />} />
            <Route path="/market-data" element={<MarketData />} />
            <Route path="/trading" element={<TradingHistory />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </Layout>
      </Router>
    </ConfigProvider>
  );
}

export default App;
