<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFQuant 前端测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.disconnected {
            background-color: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .metric {
            text-align: center;
            padding: 20px;
            background: #fafafa;
            border-radius: 6px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .metric-label {
            font-size: 14px;
            color: #666;
            margin-top: 8px;
        }
        .log {
            background: #f6f6f6;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 SFQuant 前端功能测试</h1>
            <p>测试前端与 API Gateway 的连接和实时数据功能</p>
            <div>
                WebSocket 状态: <span id="wsStatus" class="status disconnected">未连接</span>
                API 状态: <span id="apiStatus" class="status disconnected">未知</span>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>📊 系统指标</h3>
                <div class="metric">
                    <div class="metric-value" id="totalStrategies">0</div>
                    <div class="metric-label">总策略数</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="runningStrategies">0</div>
                    <div class="metric-label">运行中策略</div>
                </div>
            </div>

            <div class="card">
                <h3>💹 实时数据</h3>
                <div class="metric">
                    <div class="metric-value" id="priceUpdates">0</div>
                    <div class="metric-label">价格更新</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="tradeSignals">0</div>
                    <div class="metric-label">交易信号</div>
                </div>
            </div>

            <div class="card">
                <h3>🔧 操作面板</h3>
                <button onclick="testApiConnection()">测试 API 连接</button>
                <button onclick="connectWebSocket()">连接 WebSocket</button>
                <button onclick="disconnectWebSocket()">断开 WebSocket</button>
                <button onclick="createTestStrategy()">创建测试策略</button>
                <button onclick="clearLogs()">清空日志</button>
            </div>
        </div>

        <div class="card">
            <h3>📝 实时日志</h3>
            <div id="logs" class="log">等待操作...</div>
        </div>
    </div>

    <script>
        let ws = null;
        let priceUpdateCount = 0;
        let tradeSignalCount = 0;

        const API_BASE = 'http://localhost:8080';
        const WS_URL = 'ws://localhost:8080/ws';

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(elementId, connected) {
            const element = document.getElementById(elementId);
            element.textContent = connected ? '已连接' : '未连接';
            element.className = `status ${connected ? 'connected' : 'disconnected'}`;
        }

        function updateMetric(elementId, value) {
            document.getElementById(elementId).textContent = value;
        }

        async function testApiConnection() {
            log('🔍 测试 API 连接...');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    log('✅ API 连接成功');
                    updateStatus('apiStatus', true);
                    
                    // 获取策略数据
                    const strategyResponse = await fetch(`${API_BASE}/api/v1/strategy-manager`);
                    const strategyData = await strategyResponse.json();
                    
                    if (strategyData.success) {
                        updateMetric('totalStrategies', strategyData.data.total || 0);
                        updateMetric('runningStrategies', strategyData.data.running || 0);
                        log(`📊 策略数据: 总计 ${strategyData.data.total || 0}, 运行中 ${strategyData.data.running || 0}`);
                    }
                } else {
                    log('❌ API 连接失败: ' + data.message);
                    updateStatus('apiStatus', false);
                }
            } catch (error) {
                log('❌ API 连接错误: ' + error.message);
                updateStatus('apiStatus', false);
            }
        }

        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('⚠️ WebSocket 已经连接');
                return;
            }

            log('🔌 连接 WebSocket...');
            ws = new WebSocket(WS_URL);

            ws.onopen = function() {
                log('✅ WebSocket 连接成功');
                updateStatus('wsStatus', true);
                
                // 订阅频道
                ws.send(JSON.stringify({
                    type: 'subscribe',
                    channel: 'market-data'
                }));
                
                ws.send(JSON.stringify({
                    type: 'subscribe',
                    channel: 'trading'
                }));
                
                log('📡 已订阅市场数据和交易频道');
            };

            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                } catch (error) {
                    log('❌ 解析 WebSocket 消息失败: ' + error.message);
                }
            };

            ws.onclose = function() {
                log('🔌 WebSocket 连接已断开');
                updateStatus('wsStatus', false);
            };

            ws.onerror = function(error) {
                log('❌ WebSocket 错误: ' + error.message);
                updateStatus('wsStatus', false);
            };
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                log('🔌 主动断开 WebSocket 连接');
                updateStatus('wsStatus', false);
            }
        }

        function handleWebSocketMessage(data) {
            const { type, data: payload } = data;

            switch (type) {
                case 'price_update':
                    priceUpdateCount++;
                    updateMetric('priceUpdates', priceUpdateCount);
                    if (payload) {
                        log(`💹 价格更新: ${payload.symbol} = $${payload.price?.toFixed(2)} (${payload.exchange})`);
                    }
                    break;

                case 'execution_signal':
                    tradeSignalCount++;
                    updateMetric('tradeSignals', tradeSignalCount);
                    if (payload) {
                        log(`🎯 交易信号: ${payload.action.toUpperCase()} ${payload.symbol} @ $${payload.price?.toFixed(2)}`);
                    }
                    break;

                case 'pong':
                    log('💓 心跳响应');
                    break;

                default:
                    log(`📨 收到消息: ${type}`);
            }
        }

        async function createTestStrategy() {
            log('🎯 创建测试套利策略...');
            try {
                const strategyData = {
                    name: '测试套利策略_' + Date.now(),
                    symbol: 'BTCUSDT',
                    exchanges: ['binance', 'okx'],
                    minProfitPercent: 0.5,
                    maxPositionSize: 1000
                };

                const response = await fetch(`${API_BASE}/api/v1/strategy-manager/arbitrage`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(strategyData)
                });

                const result = await response.json();
                
                if (result.success) {
                    log('✅ 测试策略创建成功: ' + result.data.id);
                    // 刷新策略数据
                    testApiConnection();
                } else {
                    log('❌ 策略创建失败: ' + result.message);
                }
            } catch (error) {
                log('❌ 策略创建错误: ' + error.message);
            }
        }

        function clearLogs() {
            document.getElementById('logs').textContent = '日志已清空...\n';
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            log('🚀 SFQuant 前端测试页面已加载');
            testApiConnection();
        };
    </script>
</body>
</html>
