# 🧪 SFQuant UI 测试指南

## 📋 概述

本指南介绍如何使用MCP (Model Context Protocol) 进行SFQuant前端UI测试，验证已实现的功能。

## 🚀 快速开始

### 1. 启动应用
```bash
cd apps/frontend
pnpm dev
```

### 2. 运行快速验证测试
```bash
# 使用测试脚本（推荐）
./run-ui-tests.sh quick

# 或直接使用Playwright
npx playwright test quick-validation.spec.ts --headed --project=chromium
```

### 3. 查看测试报告
```bash
npx playwright show-report
```

## 📁 测试文件结构

```
apps/frontend/
├── tests/e2e/                     # E2E测试目录
│   ├── pages/                     # 页面对象模型
│   │   ├── BasePage.ts            # 基础页面类
│   │   ├── DashboardPage.ts       # 仪表盘页面
│   │   ├── StrategyPage.ts        # 策略管理页面
│   │   └── MarketDataPage.ts      # 市场数据页面
│   ├── 01-basic-functionality.spec.ts    # 基础功能测试
│   ├── 02-dashboard.spec.ts              # 仪表盘测试
│   ├── 03-strategy-management.spec.ts    # 策略管理测试
│   ├── 04-market-data.spec.ts           # 市场数据测试
│   ├── 05-integration.spec.ts           # 集成测试
│   ├── quick-validation.spec.ts         # 快速验证测试
│   ├── global-setup.ts                  # 全局设置
│   ├── global-teardown.ts               # 全局清理
│   ├── test-runner.ts                   # 测试运行器
│   └── report-generator.ts              # 报告生成器
├── playwright.config.ts           # Playwright配置
├── run-ui-tests.sh               # 测试运行脚本
├── UI_TEST_REPORT.md             # 测试报告
└── UI_TESTING_GUIDE.md           # 本指南
```

## 🎯 测试类型

### 1. 快速验证测试 (推荐)
验证核心功能是否正常工作：
```bash
./run-ui-tests.sh quick
```

**测试内容**：
- ✅ 应用启动和页面加载
- ✅ 导航菜单功能
- ✅ 页面路由切换
- ✅ 仪表盘基础功能
- ✅ 策略管理界面
- ✅ 市场数据显示
- ✅ 响应式设计
- ✅ PWA功能
- ✅ 性能检查

### 2. 基础功能测试
全面测试应用基础功能：
```bash
./run-ui-tests.sh basic
```

### 3. 专项功能测试
```bash
# 仪表盘功能测试
./run-ui-tests.sh dashboard

# 策略管理测试
./run-ui-tests.sh strategy

# 市场数据测试
./run-ui-tests.sh market-data

# 集成测试
./run-ui-tests.sh integration
```

### 4. 完整测试套件
```bash
# 单浏览器完整测试
./run-ui-tests.sh full

# 跨浏览器测试
./run-ui-tests.sh all-browsers
```

## 🔧 配置说明

### Playwright配置 (`playwright.config.ts`)
- **测试目录**: `./tests/e2e`
- **基础URL**: `http://localhost:3001`
- **浏览器**: Chromium, Firefox, WebKit
- **移动端**: Mobile Chrome, Mobile Safari
- **超时设置**: 30秒
- **重试次数**: CI环境2次，本地0次

### 测试环境要求
- Node.js 18+
- 前端应用运行在 `http://localhost:3001`
- Playwright浏览器已安装

## 📊 测试报告

### HTML报告
运行测试后自动生成，包含：
- 测试结果概览
- 失败测试详情
- 截图和视频录制
- 性能数据

### 查看报告
```bash
npx playwright show-report
```

### 报告文件位置
- HTML报告: `playwright-report/index.html`
- 测试结果: `test-results/`
- 截图: `test-results/screenshots/`
- 视频: `test-results/videos/`

## 🐛 常见问题

### 1. 应用未启动
**错误**: `❌ 应用未运行`
**解决**: 
```bash
cd apps/frontend
pnpm dev
```

### 2. Playwright未安装
**错误**: `Playwright未安装`
**解决**: 
```bash
npx playwright install
```

### 3. 测试选择器失败
**错误**: `strict mode violation: locator resolved to 2 elements`
**解决**: 使用更精确的选择器或`.first()`方法

### 4. WebSocket连接错误
**错误**: `WebSocket 连接错误`
**解决**: 确保后端WebSocket服务正常运行

## 🎨 自定义测试

### 创建新测试
```typescript
import { test, expect } from '@playwright/test';

test('我的自定义测试', async ({ page }) => {
  await page.goto('/');
  
  // 测试逻辑
  await expect(page.locator('h1')).toBeVisible();
});
```

### 使用页面对象模型
```typescript
import { DashboardPage } from './pages/DashboardPage';

test('使用页面对象', async ({ page }) => {
  const dashboardPage = new DashboardPage(page);
  await dashboardPage.goto();
  await dashboardPage.checkDashboardElements();
});
```

## 📈 性能测试

测试自动检查：
- 页面加载时间 (< 5秒)
- DOM元素数量 (< 2000)
- 内存使用情况
- 网络请求性能

## 🔄 持续集成

### GitHub Actions示例
```yaml
name: UI Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: npx playwright install
      - run: npm run dev &
      - run: ./run-ui-tests.sh quick
```

## 📝 最佳实践

### 1. 测试编写
- 使用描述性的测试名称
- 每个测试保持独立
- 使用页面对象模型
- 添加适当的等待和断言

### 2. 选择器策略
- 优先使用`data-testid`属性
- 避免依赖CSS类名
- 使用语义化选择器

### 3. 测试维护
- 定期更新测试用例
- 保持测试代码简洁
- 及时修复失败的测试

## 🎯 验证清单

运行UI测试后，确认以下功能正常：

- [ ] 应用启动和页面加载
- [ ] 导航菜单和路由
- [ ] 仪表盘数据显示
- [ ] 策略管理功能
- [ ] 市场数据图表
- [ ] 响应式设计
- [ ] PWA功能
- [ ] 性能表现
- [ ] 错误处理
- [ ] 跨浏览器兼容性

## 🆘 获取帮助

如果遇到问题：

1. 查看测试报告中的错误详情
2. 检查截图和视频录制
3. 查看控制台日志
4. 参考本指南的常见问题部分

---

**Happy Testing! 🎉**
