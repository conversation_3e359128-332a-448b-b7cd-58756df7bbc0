import { test, expect } from '@playwright/test';
import { MarketDataPage } from './pages/MarketDataPage';

test.describe('市场数据功能测试', () => {
  let marketDataPage: MarketDataPage;

  test.beforeEach(async ({ page }) => {
    marketDataPage = new MarketDataPage(page);
    await marketDataPage.goto();
  });

  test('市场数据页面基础功能', async ({ page }) => {
    await test.step('检查页面基础元素', async () => {
      await marketDataPage.checkMarketDataElements();
    });

    await test.step('检查价格图表', async () => {
      await marketDataPage.checkPriceChart();
    });

    await test.step('检查市场数据表格', async () => {
      await marketDataPage.checkMarketDataTable();
    });

    await test.step('截图记录', async () => {
      await marketDataPage.takeScreenshot('market-data-overview');
    });
  });

  test('数据可视化功能', async ({ page }) => {
    await test.step('检查图表渲染', async () => {
      await marketDataPage.checkDataVisualization();
    });

    await test.step('测试图表交互', async () => {
      const charts = page.locator('svg, canvas, .chart');
      const chartCount = await charts.count();
      
      if (chartCount > 0) {
        for (let i = 0; i < Math.min(chartCount, 3); i++) {
          const chart = charts.nth(i);
          
          // 测试悬停交互
          await chart.hover();
          await page.waitForTimeout(500);
          
          // 测试点击交互
          await chart.click();
          await page.waitForTimeout(300);
          
          console.log(`✅ 图表 ${i + 1} 交互测试完成`);
        }
      }
    });

    await test.step('检查图表数据更新', async () => {
      // 记录初始图表状态
      const initialChartData = await page.locator('svg path, svg circle').count();
      console.log(`初始图表数据点: ${initialChartData}`);
      
      // 刷新数据
      await marketDataPage.refreshMarketData();
      
      // 检查图表是否重新渲染
      const updatedChartData = await page.locator('svg path, svg circle').count();
      console.log(`更新后图表数据点: ${updatedChartData}`);
    });
  });

  test('实时数据功能', async ({ page }) => {
    await test.step('检查实时数据更新', async () => {
      await marketDataPage.checkRealTimeUpdates();
    });

    await test.step('测试WebSocket连接', async () => {
      await marketDataPage.checkWebSocketConnection();
    });

    await test.step('监控数据变化', async () => {
      // 监控价格数据变化
      const priceElements = page.locator('.price, .ant-statistic-content-value');
      const initialPrices = await priceElements.allTextContents();
      
      console.log('初始价格数据:', initialPrices.slice(0, 5));
      
      // 等待数据更新
      await page.waitForTimeout(5000);
      
      const updatedPrices = await priceElements.allTextContents();
      console.log('更新后价格数据:', updatedPrices.slice(0, 5));
      
      // 比较数据是否有变化
      const hasChanges = initialPrices.some((price, index) => 
        price !== updatedPrices[index]
      );
      
      if (hasChanges) {
        console.log('✅ 检测到实时数据更新');
      } else {
        console.log('⚠️ 未检测到数据变化（可能是静态数据）');
      }
    });
  });

  test('市场数据表格功能', async ({ page }) => {
    await test.step('检查表格结构', async () => {
      const table = page.locator('.ant-table');
      await expect(table).toBeVisible();
      
      // 检查表头
      const headers = page.locator('.ant-table-thead th');
      const headerCount = await headers.count();
      console.log(`表格列数: ${headerCount}`);
      
      // 检查表格内容
      const rows = page.locator('.ant-table-tbody tr');
      const rowCount = await rows.count();
      console.log(`表格行数: ${rowCount}`);
      
      if (rowCount > 0) {
        // 检查第一行数据
        const firstRow = rows.first();
        const cellData = await firstRow.locator('td').allTextContents();
        console.log('第一行数据:', cellData);
      }
    });

    await test.step('测试表格排序', async () => {
      // 查找可排序的列头
      const sortableHeaders = page.locator('.ant-table-column-sorter');
      const sortableCount = await sortableHeaders.count();
      
      if (sortableCount > 0) {
        // 测试第一个可排序列
        await sortableHeaders.first().click();
        await page.waitForTimeout(1000);
        
        // 再次点击测试反向排序
        await sortableHeaders.first().click();
        await page.waitForTimeout(1000);
        
        console.log('✅ 表格排序功能测试完成');
      }
    });

    await test.step('测试表格筛选', async () => {
      // 查找筛选按钮
      const filterButtons = page.locator('.ant-table-filter-trigger');
      const filterCount = await filterButtons.count();
      
      if (filterCount > 0) {
        // 测试第一个筛选器
        await filterButtons.first().click();
        await page.waitForTimeout(500);
        
        // 关闭筛选面板
        await page.click('body');
        await page.waitForTimeout(300);
        
        console.log('✅ 表格筛选功能测试完成');
      }
    });
  });

  test('交易所和交易对选择', async ({ page }) => {
    await test.step('测试交易所选择', async () => {
      const exchangeSelector = page.locator('.ant-select:has(.ant-select-selector:has-text("交易所"))');
      
      if (await exchangeSelector.count() > 0) {
        await exchangeSelector.click();
        
        // 检查可选交易所
        const options = page.locator('.ant-select-item');
        const optionCount = await options.count();
        console.log(`可选交易所数量: ${optionCount}`);
        
        if (optionCount > 0) {
          // 选择第一个交易所
          const firstOption = options.first();
          const optionText = await firstOption.textContent();
          await firstOption.click();
          
          console.log(`选择交易所: ${optionText}`);
          await page.waitForTimeout(1000);
        }
      }
    });

    await test.step('测试交易对选择', async () => {
      const pairSelector = page.locator('.ant-select:has(.ant-select-selector:has-text("交易对"))');
      
      if (await pairSelector.count() > 0) {
        await pairSelector.click();
        
        // 检查可选交易对
        const pairOptions = page.locator('.ant-select-item');
        const pairCount = await pairOptions.count();
        console.log(`可选交易对数量: ${pairCount}`);
        
        if (pairCount > 0) {
          // 选择第一个交易对
          const firstPair = pairOptions.first();
          const pairText = await firstPair.textContent();
          await firstPair.click();
          
          console.log(`选择交易对: ${pairText}`);
          await page.waitForTimeout(1000);
        }
      }
    });
  });

  test('订单簿和交易记录', async ({ page }) => {
    await test.step('检查订单簿', async () => {
      await marketDataPage.checkOrderBook();
    });

    await test.step('检查最近交易', async () => {
      await marketDataPage.checkRecentTrades();
    });

    await test.step('测试订单簿交互', async () => {
      const orderBookItems = page.locator('.order-book-item, .bid, .ask');
      const itemCount = await orderBookItems.count();
      
      if (itemCount > 0) {
        // 测试悬停效果
        for (let i = 0; i < Math.min(itemCount, 5); i++) {
          const item = orderBookItems.nth(i);
          await item.hover();
          await page.waitForTimeout(200);
        }
        
        console.log('✅ 订单簿交互测试完成');
      }
    });
  });

  test('市场数据响应式设计', async ({ page }) => {
    await test.step('测试响应式布局', async () => {
      await marketDataPage.checkResponsiveMarketData();
    });

    await test.step('不同设备截图', async () => {
      const viewports = [
        { width: 1920, height: 1080, name: 'desktop' },
        { width: 768, height: 1024, name: 'tablet' },
        { width: 375, height: 667, name: 'mobile' }
      ];
      
      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        await page.waitForTimeout(500);
        await marketDataPage.takeScreenshot(`market-data-${viewport.name}`);
      }
      
      // 恢复桌面布局
      await page.setViewportSize({ width: 1920, height: 1080 });
    });
  });

  test('市场数据性能测试', async ({ page }) => {
    await test.step('页面加载性能', async () => {
      const startTime = Date.now();
      await marketDataPage.goto();
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      console.log(`市场数据页面加载时间: ${loadTime}ms`);
      expect(loadTime).toBeLessThan(8000); // 8秒内加载完成
    });

    await test.step('图表渲染性能', async () => {
      const startTime = Date.now();
      
      // 等待图表渲染完成
      await page.waitForSelector('svg, canvas', { timeout: 5000 });
      
      const renderTime = Date.now() - startTime;
      console.log(`图表渲染时间: ${renderTime}ms`);
      expect(renderTime).toBeLessThan(3000);
    });

    await test.step('数据刷新性能', async () => {
      const startTime = Date.now();
      await marketDataPage.refreshMarketData();
      const refreshTime = Date.now() - startTime;
      
      console.log(`数据刷新时间: ${refreshTime}ms`);
      expect(refreshTime).toBeLessThan(5000);
    });
  });

  test('市场数据完整流程', async ({ page }) => {
    await test.step('执行完整测试流程', async () => {
      await marketDataPage.testMarketDataFlow();
    });

    await test.step('验证数据一致性', async () => {
      // 检查图表和表格数据是否一致
      const chartPrices = page.locator('svg text, .chart-price');
      const tablePrices = page.locator('.ant-table-tbody .price');
      
      const chartPriceCount = await chartPrices.count();
      const tablePriceCount = await tablePrices.count();
      
      console.log(`图表价格数据点: ${chartPriceCount}`);
      console.log(`表格价格数据行: ${tablePriceCount}`);
      
      // 基础一致性检查
      if (chartPriceCount > 0 && tablePriceCount > 0) {
        console.log('✅ 图表和表格都包含价格数据');
      }
    });

    await test.step('最终状态截图', async () => {
      await marketDataPage.takeScreenshot('market-data-final-state');
    });
  });
});
