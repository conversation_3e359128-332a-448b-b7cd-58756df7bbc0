import { test, expect } from '@playwright/test';
import { DashboardPage } from './pages/DashboardPage';
import { StrategyPage } from './pages/StrategyPage';
import { MarketDataPage } from './pages/MarketDataPage';

test.describe('SFQuant 基础功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 每个测试前的设置
    await page.goto('/');
  });

  test('应用启动和基础导航', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/SFQuant/);
    
    // 检查主要导航元素
    const sidebar = page.locator('.ant-layout-sider');
    await expect(sidebar).toBeVisible();
    
    // 检查菜单项
    const menuItems = [
      '仪表盘',
      '策略管理', 
      '市场数据',
      '交易历史',
      '系统设置'
    ];
    
    for (const item of menuItems) {
      const menuItem = page.locator(`.ant-menu-item:has-text("${item}")`);
      await expect(menuItem).toBeVisible();
    }
  });

  test('页面路由导航测试', async ({ page }) => {
    const routes = [
      { path: '/', title: '仪表盘' },
      { path: '/strategies', title: '策略管理' },
      { path: '/market-data', title: '市场数据' },
      { path: '/trading', title: '交易历史' },
      { path: '/settings', title: '系统设置' }
    ];
    
    for (const route of routes) {
      await page.goto(route.path);
      await page.waitForLoadState('networkidle');
      
      // 检查URL
      expect(page.url()).toContain(route.path);
      
      // 检查页面内容加载
      const content = page.locator('.ant-layout-content');
      await expect(content).toBeVisible();
      
      console.log(`✅ 路由 ${route.path} 导航成功`);
    }
  });

  test('响应式布局测试', async ({ page }) => {
    const viewports = [
      { width: 1920, height: 1080, name: '桌面大屏' },
      { width: 1366, height: 768, name: '桌面标准' },
      { width: 1024, height: 768, name: '平板横屏' },
      { width: 768, height: 1024, name: '平板竖屏' },
      { width: 375, height: 667, name: '手机' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(500);
      
      // 检查布局容器
      const layout = page.locator('.ant-layout');
      await expect(layout).toBeVisible();
      
      // 检查内容区域
      const content = page.locator('.ant-layout-content');
      await expect(content).toBeVisible();
      
      // 在小屏幕上检查侧边栏行为
      if (viewport.width < 768) {
        const sider = page.locator('.ant-layout-sider');
        // 移动端侧边栏可能被折叠
        const isCollapsed = await sider.evaluate(el => 
          el.classList.contains('ant-layout-sider-collapsed')
        );
        console.log(`${viewport.name}: 侧边栏${isCollapsed ? '已折叠' : '展开'}`);
      }
      
      console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}) 布局正常`);
    }
    
    // 恢复默认视口
    await page.setViewportSize({ width: 1920, height: 1080 });
  });

  test('侧边栏折叠功能', async ({ page }) => {
    const sidebar = page.locator('.ant-layout-sider');
    const toggleButton = page.locator('button[aria-label*="fold"], button[aria-label*="unfold"]');
    
    // 检查初始状态
    await expect(sidebar).toBeVisible();
    
    // 如果有折叠按钮，测试折叠功能
    if (await toggleButton.count() > 0) {
      // 点击折叠按钮
      await toggleButton.click();
      await page.waitForTimeout(300);
      
      // 再次点击展开
      await toggleButton.click();
      await page.waitForTimeout(300);
      
      console.log('✅ 侧边栏折叠功能正常');
    }
  });

  test('用户界面交互测试', async ({ page }) => {
    // 测试头部用户菜单
    const userAvatar = page.locator('.ant-avatar');
    if (await userAvatar.count() > 0) {
      await userAvatar.click();
      
      // 检查下拉菜单
      const dropdown = page.locator('.ant-dropdown');
      await expect(dropdown).toBeVisible();
      
      // 点击其他地方关闭菜单
      await page.click('body');
      await expect(dropdown).toBeHidden();
    }
    
    // 测试通知按钮
    const notificationButton = page.locator('button:has(.anticon-bell)');
    if (await notificationButton.count() > 0) {
      await expect(notificationButton).toBeVisible();
      
      // 检查通知徽章
      const badge = notificationButton.locator('.ant-badge');
      if (await badge.count() > 0) {
        console.log('✅ 通知徽章显示正常');
      }
    }
  });

  test('错误处理和加载状态', async ({ page }) => {
    // 检查加载状态
    const loadingSpinners = page.locator('.ant-spin');
    
    // 刷新页面观察加载状态
    await page.reload();
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 检查是否有错误消息
    const errorMessages = page.locator('.ant-message-error, .ant-notification-error, .ant-alert-error');
    const errorCount = await errorMessages.count();
    
    if (errorCount > 0) {
      console.log(`⚠️ 发现 ${errorCount} 个错误消息`);
      for (let i = 0; i < errorCount; i++) {
        const error = errorMessages.nth(i);
        const errorText = await error.textContent();
        console.log(`错误 ${i + 1}: ${errorText}`);
      }
    } else {
      console.log('✅ 未发现错误消息');
    }
  });

  test('PWA功能检测', async ({ page }) => {
    // 检查Service Worker注册
    const swRegistration = await page.evaluate(() => {
      return 'serviceWorker' in navigator;
    });
    
    expect(swRegistration).toBe(true);
    console.log('✅ Service Worker支持检测通过');
    
    // 检查PWA manifest
    const manifestLink = page.locator('link[rel="manifest"]');
    await expect(manifestLink).toHaveCount(1);
    
    // 检查PWA图标
    const appleIcon = page.locator('link[rel="apple-touch-icon"]');
    if (await appleIcon.count() > 0) {
      console.log('✅ PWA图标配置正常');
    }
    
    // 检查主题色
    const themeColor = page.locator('meta[name="theme-color"]');
    await expect(themeColor).toHaveCount(1);
    
    const themeColorValue = await themeColor.getAttribute('content');
    console.log(`PWA主题色: ${themeColorValue}`);
  });

  test('性能基础检测', async ({ page }) => {
    // 测量页面加载时间
    const startTime = Date.now();
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    console.log(`页面加载时间: ${loadTime}ms`);
    
    // 检查是否有大量DOM元素
    const elementCount = await page.locator('*').count();
    console.log(`DOM元素数量: ${elementCount}`);
    
    // 基础性能断言
    expect(loadTime).toBeLessThan(10000); // 10秒内加载完成
    expect(elementCount).toBeLessThan(5000); // DOM元素不超过5000个
  });
});
