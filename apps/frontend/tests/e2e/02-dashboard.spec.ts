import { test, expect } from '@playwright/test';
import { DashboardPage } from './pages/DashboardPage';

test.describe('仪表盘功能测试', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
  });

  test('仪表盘页面基础元素检查', async ({ page }) => {
    await test.step('检查页面标题和基础元素', async () => {
      await dashboardPage.checkDashboardElements();
    });

    await test.step('检查系统统计数据', async () => {
      await dashboardPage.checkSystemStats();
    });

    await test.step('截图记录', async () => {
      await dashboardPage.takeScreenshot('dashboard-overview');
    });
  });

  test('WebSocket连接状态检查', async ({ page }) => {
    await test.step('检查WebSocket连接状态', async () => {
      const isConnected = await dashboardPage.checkWebSocketStatus();
      
      if (isConnected) {
        console.log('✅ WebSocket连接正常');
      } else {
        console.log('⚠️ WebSocket连接断开或未启动');
        
        // 检查是否有连接错误提示
        const alertCount = await dashboardPage.checkAlertMessages();
        if (alertCount > 0) {
          console.log(`发现 ${alertCount} 个警告或错误消息`);
        }
      }
    });

    await test.step('检查实时数据推送', async () => {
      // 等待一段时间观察数据是否更新
      await page.waitForTimeout(3000);
      
      // 检查是否有实时数据更新
      const priceElements = page.locator('.ant-statistic-content-value');
      if (await priceElements.count() > 0) {
        console.log('✅ 价格数据元素存在');
      }
    });
  });

  test('技术指标计算功能', async ({ page }) => {
    await test.step('执行技术指标计算', async () => {
      await dashboardPage.calculateTechnicalIndicators();
    });

    await test.step('检查计算结果', async () => {
      await dashboardPage.checkTechnicalIndicatorsResults();
    });

    await test.step('截图记录计算结果', async () => {
      await dashboardPage.takeScreenshot('technical-indicators-results');
    });
  });

  test('仪表盘数据刷新', async ({ page }) => {
    await test.step('记录初始状态', async () => {
      const initialStats = await page.locator('.ant-statistic-content-value').allTextContents();
      console.log('初始统计数据:', initialStats);
    });

    await test.step('刷新数据', async () => {
      await dashboardPage.refreshData();
    });

    await test.step('验证数据刷新', async () => {
      const refreshedStats = await page.locator('.ant-statistic-content-value').allTextContents();
      console.log('刷新后统计数据:', refreshedStats);
      
      // 检查页面是否重新加载成功
      await dashboardPage.checkDashboardElements();
    });
  });

  test('仪表盘响应式设计', async ({ page }) => {
    await test.step('测试响应式布局', async () => {
      await dashboardPage.checkResponsiveDashboard();
    });

    await test.step('移动端布局截图', async () => {
      await page.setViewportSize({ width: 375, height: 667 });
      await dashboardPage.takeScreenshot('dashboard-mobile');
      
      // 恢复桌面布局
      await page.setViewportSize({ width: 1920, height: 1080 });
    });
  });

  test('仪表盘交互功能', async ({ page }) => {
    await test.step('测试卡片交互', async () => {
      // 检查统计卡片是否可点击或有悬停效果
      const statCards = page.locator('.ant-statistic');
      const cardCount = await statCards.count();
      
      for (let i = 0; i < Math.min(cardCount, 4); i++) {
        const card = statCards.nth(i);
        await card.hover();
        await page.waitForTimeout(500);
      }
    });

    await test.step('测试按钮交互', async () => {
      // 测试所有可见按钮
      const buttons = page.locator('button:visible');
      const buttonCount = await buttons.count();
      
      console.log(`发现 ${buttonCount} 个可见按钮`);
      
      // 测试前几个按钮的悬停效果
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i);
        const buttonText = await button.textContent();
        
        if (buttonText && !buttonText.includes('折叠')) {
          await button.hover();
          await page.waitForTimeout(300);
          console.log(`✅ 按钮 "${buttonText}" 悬停效果正常`);
        }
      }
    });
  });

  test('仪表盘性能监控', async ({ page }) => {
    await test.step('监控页面性能', async () => {
      // 测量页面渲染时间
      const startTime = Date.now();
      await dashboardPage.goto();
      await page.waitForLoadState('networkidle');
      const renderTime = Date.now() - startTime;
      
      console.log(`仪表盘渲染时间: ${renderTime}ms`);
      expect(renderTime).toBeLessThan(5000); // 5秒内完成渲染
    });

    await test.step('检查内存使用', async () => {
      // 检查页面元素数量
      const elementCount = await page.locator('*').count();
      console.log(`仪表盘DOM元素数量: ${elementCount}`);
      
      // 检查图片数量
      const imageCount = await page.locator('img').count();
      console.log(`图片数量: ${imageCount}`);
      
      // 基础性能断言
      expect(elementCount).toBeLessThan(2000);
    });
  });

  test('仪表盘错误处理', async ({ page }) => {
    await test.step('检查错误状态处理', async () => {
      // 检查是否有错误消息
      const errorAlerts = page.locator('.ant-alert-error');
      const errorCount = await errorAlerts.count();
      
      if (errorCount > 0) {
        console.log(`发现 ${errorCount} 个错误提示`);
        for (let i = 0; i < errorCount; i++) {
          const error = errorAlerts.nth(i);
          const errorText = await error.textContent();
          console.log(`错误 ${i + 1}: ${errorText}`);
        }
      }
    });

    await test.step('测试网络错误恢复', async () => {
      // 模拟网络中断
      await page.route('**/api/**', route => route.abort());
      
      // 尝试刷新数据
      await dashboardPage.refreshData();
      
      // 检查错误处理
      await page.waitForTimeout(2000);
      
      // 恢复网络
      await page.unroute('**/api/**');
      
      console.log('✅ 网络错误处理测试完成');
    });
  });

  test('仪表盘数据完整性', async ({ page }) => {
    await test.step('验证统计数据格式', async () => {
      const statValues = page.locator('.ant-statistic-content-value');
      const valueCount = await statValues.count();
      
      for (let i = 0; i < valueCount; i++) {
        const value = statValues.nth(i);
        const valueText = await value.textContent();
        
        // 检查数值格式是否合理
        if (valueText) {
          console.log(`统计值 ${i + 1}: ${valueText}`);
          
          // 基础格式验证
          expect(valueText.trim()).not.toBe('');
          expect(valueText).not.toContain('undefined');
          expect(valueText).not.toContain('null');
        }
      }
    });

    await test.step('验证时间戳和日期', async () => {
      // 检查页面上的时间显示
      const timeElements = page.locator('[data-testid*="time"], .time, .timestamp');
      const timeCount = await timeElements.count();
      
      if (timeCount > 0) {
        for (let i = 0; i < timeCount; i++) {
          const timeElement = timeElements.nth(i);
          const timeText = await timeElement.textContent();
          console.log(`时间显示 ${i + 1}: ${timeText}`);
        }
      }
    });
  });
});
