import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 开始全局测试设置...');
  
  // 启动浏览器进行预检查
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // 等待应用启动
    console.log('⏳ 等待应用启动...');
    await page.goto('http://localhost:3001', { 
      waitUntil: 'networkidle',
      timeout: 60000 
    });
    
    // 检查应用是否正常加载
    await page.waitForSelector('h1', { timeout: 10000 });
    console.log('✅ 应用启动成功');
    
    // 检查基础路由
    const routes = ['/', '/strategies', '/market-data'];
    for (const route of routes) {
      await page.goto(`http://localhost:3001${route}`);
      await page.waitForLoadState('networkidle');
      console.log(`✅ 路由 ${route} 可访问`);
    }
    
  } catch (error) {
    console.error('❌ 全局设置失败:', error);
    throw error;
  } finally {
    await browser.close();
  }
  
  console.log('🎉 全局测试设置完成');
}

export default globalSetup;
