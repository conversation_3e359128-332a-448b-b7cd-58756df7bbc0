import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class DashboardPage extends BasePage {
  readonly pageTitle: Locator;
  readonly systemStats: Locator;
  readonly strategyStats: Locator;
  readonly marketDataSection: Locator;
  readonly technicalIndicators: Locator;
  readonly calculateButton: Locator;
  readonly wsConnectionStatus: Locator;
  readonly alertMessages: Locator;

  constructor(page: Page) {
    super(page);
    this.pageTitle = page.locator('h1:has-text("SFQuant")');
    this.systemStats = page.locator('[data-testid="system-stats"], .ant-statistic');
    this.strategyStats = page.locator('[data-testid="strategy-stats"]');
    this.marketDataSection = page.locator('[data-testid="market-data-section"]');
    this.technicalIndicators = page.locator('[data-testid="technical-indicators"]');
    this.calculateButton = page.locator('button:has-text("计算技术指标")');
    this.wsConnectionStatus = page.locator('.ant-tag:has-text("连接"), .ant-tag:has-text("断开")');
    this.alertMessages = page.locator('.ant-alert');
  }

  async goto() {
    await super.goto('/');
  }

  async checkDashboardElements() {
    // 检查页面标题
    await expect(this.pageTitle).toBeVisible();
    await expect(this.pageTitle).toContainText('SFQuant');
    
    // 检查WebSocket连接状态
    await expect(this.wsConnectionStatus).toBeVisible();
    
    // 检查统计卡片
    const statCards = this.page.locator('.ant-statistic');
    await expect(statCards).toHaveCount(4, { timeout: 10000 });
  }

  async checkSystemStats() {
    // 检查系统统计数据
    const stats = [
      '活跃策略',
      '总收益',
      '今日交易',
      '系统状态'
    ];
    
    for (const stat of stats) {
      const statElement = this.page.locator(`.ant-statistic:has(.ant-statistic-title:has-text("${stat}"))`);
      await expect(statElement).toBeVisible();
    }
  }

  async calculateTechnicalIndicators() {
    // 点击计算技术指标按钮
    await this.calculateButton.click();
    
    // 等待计算完成
    await this.page.waitForTimeout(2000);
    
    // 检查是否显示了计算结果
    const successMessage = this.page.locator('.ant-message-success, .ant-notification-success');
    await expect(successMessage).toBeVisible({ timeout: 10000 });
  }

  async checkTechnicalIndicatorsResults() {
    // 检查技术指标结果显示
    const indicators = [
      'SMA',
      'EMA', 
      'RSI',
      'MACD'
    ];
    
    for (const indicator of indicators) {
      const indicatorCard = this.page.locator(`.ant-card:has(.ant-card-head-title:has-text("${indicator}"))`);
      await expect(indicatorCard).toBeVisible();
    }
  }

  async checkWebSocketStatus() {
    // 检查WebSocket连接状态
    const wsStatus = this.wsConnectionStatus;
    await expect(wsStatus).toBeVisible();
    
    // 获取连接状态文本
    const statusText = await wsStatus.textContent();
    console.log(`WebSocket状态: ${statusText}`);
    
    return statusText?.includes('连接');
  }

  async checkAlertMessages() {
    // 检查是否有错误或警告消息
    const alerts = this.alertMessages;
    const alertCount = await alerts.count();
    
    if (alertCount > 0) {
      for (let i = 0; i < alertCount; i++) {
        const alert = alerts.nth(i);
        const alertText = await alert.textContent();
        console.log(`Alert消息: ${alertText}`);
      }
    }
    
    return alertCount;
  }

  async refreshData() {
    // 刷新页面数据
    await this.page.reload();
    await this.waitForPageLoad();
  }

  async checkResponsiveDashboard() {
    // 检查仪表板的响应式布局
    await this.checkResponsiveLayout();
    
    // 在不同屏幕尺寸下检查关键元素
    const viewports = [
      { width: 1920, height: 1080, name: '桌面' },
      { width: 1024, height: 768, name: '平板' },
      { width: 375, height: 667, name: '手机' }
    ];
    
    for (const viewport of viewports) {
      await this.page.setViewportSize(viewport);
      await this.page.waitForTimeout(500);
      
      // 检查标题是否可见
      await expect(this.pageTitle).toBeVisible();
      
      console.log(`✅ ${viewport.name}布局检查通过`);
    }
    
    // 恢复桌面布局
    await this.page.setViewportSize({ width: 1920, height: 1080 });
  }
}
