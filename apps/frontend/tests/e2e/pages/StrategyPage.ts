import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class StrategyPage extends BasePage {
  readonly pageTitle: Locator;
  readonly createStrategyButton: Locator;
  readonly strategyTable: Locator;
  readonly strategyModal: Locator;
  readonly strategyForm: Locator;
  readonly statsCards: Locator;
  readonly batchOperationButtons: Locator;

  constructor(page: Page) {
    super(page);
    this.pageTitle = page.locator('h1, h2').filter({ hasText: /策略管理|Strategy/ });
    this.createStrategyButton = page.locator('button:has-text("创建策略"), button:has-text("新建策略")');
    this.strategyTable = page.locator('.ant-table');
    this.strategyModal = page.locator('.ant-modal');
    this.strategyForm = page.locator('.ant-form');
    this.statsCards = page.locator('.ant-statistic');
    this.batchOperationButtons = page.locator('button:has-text("批量"), button:has-text("全部")');
  }

  async goto() {
    await super.goto('/strategies');
  }

  async checkStrategyPageElements() {
    // 检查页面基本元素
    await expect(this.createStrategyButton).toBeVisible();
    await expect(this.strategyTable).toBeVisible();
    
    // 检查统计卡片
    await expect(this.statsCards).toHaveCount(4, { timeout: 10000 });
  }

  async checkStrategyStats() {
    // 检查策略统计数据
    const expectedStats = [
      '总策略数',
      '运行中策略', 
      '总收益',
      '平均胜率'
    ];
    
    for (const stat of expectedStats) {
      const statCard = this.page.locator(`.ant-statistic:has(.ant-statistic-title:has-text("${stat}"))`);
      await expect(statCard).toBeVisible();
      
      // 检查数值显示
      const value = statCard.locator('.ant-statistic-content-value');
      await expect(value).toBeVisible();
    }
  }

  async openCreateStrategyModal() {
    await this.createStrategyButton.click();
    await expect(this.strategyModal).toBeVisible();
    await expect(this.strategyForm).toBeVisible();
  }

  async fillStrategyForm(strategyData: {
    name: string;
    description?: string;
    type: string;
    tradingPair: string;
    exchange: string;
    capital?: string;
    riskLevel: string;
  }) {
    // 填写策略名称
    await this.page.locator('input[placeholder*="策略名称"]').fill(strategyData.name);
    
    // 填写描述（可选）
    if (strategyData.description) {
      await this.page.locator('textarea[placeholder*="描述"]').fill(strategyData.description);
    }
    
    // 选择策略类型
    await this.page.locator('.ant-select:has(.ant-select-selector:has-text("策略类型"))').click();
    await this.page.locator(`.ant-select-item:has-text("${strategyData.type}")`).click();
    
    // 填写交易对
    await this.page.locator('input[placeholder*="交易对"]').fill(strategyData.tradingPair);
    
    // 选择交易所
    await this.page.locator('.ant-select:has(.ant-select-selector:has-text("交易所"))').click();
    await this.page.locator(`.ant-select-item:has-text("${strategyData.exchange}")`).click();
    
    // 填写投入资金（可选）
    if (strategyData.capital) {
      await this.page.locator('input[placeholder*="投入资金"]').fill(strategyData.capital);
    }
    
    // 选择风险等级
    await this.page.locator('.ant-select:has(.ant-select-selector:has-text("风险等级"))').click();
    await this.page.locator(`.ant-select-item:has-text("${strategyData.riskLevel}")`).click();
  }

  async submitStrategyForm() {
    const submitButton = this.strategyModal.locator('button:has-text("确定"), button:has-text("创建")');
    await submitButton.click();
    
    // 等待模态框关闭
    await expect(this.strategyModal).toBeHidden({ timeout: 10000 });
  }

  async cancelStrategyForm() {
    const cancelButton = this.strategyModal.locator('button:has-text("取消")');
    await cancelButton.click();
    
    // 等待模态框关闭
    await expect(this.strategyModal).toBeHidden();
  }

  async checkFormValidation() {
    // 打开创建策略模态框
    await this.openCreateStrategyModal();
    
    // 尝试提交空表单
    const submitButton = this.strategyModal.locator('button:has-text("确定"), button:has-text("创建")');
    await submitButton.click();
    
    // 检查验证错误消息
    const errorMessages = this.page.locator('.ant-form-item-explain-error');
    await expect(errorMessages).toHaveCount(3, { timeout: 5000 }); // 必填字段：名称、交易对、类型
    
    // 关闭模态框
    await this.cancelStrategyForm();
  }

  async checkStrategyTable() {
    // 检查表格结构
    const tableHeaders = [
      '策略名称',
      '类型', 
      '状态',
      '收益率',
      '胜率',
      '交易次数',
      '操作'
    ];
    
    for (const header of tableHeaders) {
      const headerElement = this.page.locator(`.ant-table-thead th:has-text("${header}")`);
      await expect(headerElement).toBeVisible();
    }
    
    // 检查空状态或数据行
    const tableBody = this.page.locator('.ant-table-tbody');
    const rows = tableBody.locator('tr');
    const rowCount = await rows.count();
    
    if (rowCount === 1) {
      // 检查空状态
      const emptyText = this.page.locator('.ant-empty-description');
      await expect(emptyText).toBeVisible();
    } else {
      // 检查数据行
      console.log(`策略表格包含 ${rowCount} 行数据`);
    }
  }

  async testCreateStrategy() {
    const testStrategy = {
      name: `测试策略_${Date.now()}`,
      description: '这是一个测试策略',
      type: '套利策略',
      tradingPair: 'BTC/USDT',
      exchange: 'Binance',
      capital: '5000',
      riskLevel: '中等风险'
    };
    
    // 打开创建策略模态框
    await this.openCreateStrategyModal();
    
    // 填写表单
    await this.fillStrategyForm(testStrategy);
    
    // 提交表单
    await this.submitStrategyForm();
    
    // 检查成功消息
    const successMessage = this.page.locator('.ant-message-success, .ant-notification-success');
    await expect(successMessage).toBeVisible({ timeout: 10000 });
    
    return testStrategy;
  }

  async checkBatchOperations() {
    // 检查批量操作按钮
    const batchButtons = this.batchOperationButtons;
    const buttonCount = await batchButtons.count();
    
    if (buttonCount > 0) {
      for (let i = 0; i < buttonCount; i++) {
        const button = batchButtons.nth(i);
        await expect(button).toBeVisible();
        
        const buttonText = await button.textContent();
        console.log(`批量操作按钮: ${buttonText}`);
      }
    }
  }

  async checkStrategySignals() {
    // 检查策略信号展示区域
    const signalsSection = this.page.locator('[data-testid="strategy-signals"], .strategy-signals');
    
    if (await signalsSection.count() > 0) {
      await expect(signalsSection).toBeVisible();
      console.log('✅ 策略信号区域可见');
    }
  }

  async refreshStrategies() {
    // 刷新策略数据
    const refreshButton = this.page.locator('button:has-text("刷新"), .ant-btn-icon-only');
    
    if (await refreshButton.count() > 0) {
      await refreshButton.first().click();
      await this.waitForPageLoad();
    } else {
      await this.page.reload();
      await this.waitForPageLoad();
    }
  }
}
