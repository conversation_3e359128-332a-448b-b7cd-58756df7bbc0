import { Page, Locator, expect } from '@playwright/test';

export class BasePage {
  readonly page: Page;
  readonly header: Locator;
  readonly sidebar: Locator;
  readonly content: Locator;
  readonly loadingSpinner: Locator;

  constructor(page: Page) {
    this.page = page;
    this.header = page.locator('header, .ant-layout-header');
    this.sidebar = page.locator('.ant-layout-sider');
    this.content = page.locator('.ant-layout-content');
    this.loadingSpinner = page.locator('.ant-spin');
  }

  async goto(path: string = '/') {
    await this.page.goto(path);
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    // 等待页面加载完成
    await this.page.waitForLoadState('networkidle');
    
    // 等待加载动画消失
    await this.loadingSpinner.waitFor({ state: 'hidden', timeout: 5000 }).catch(() => {
      // 如果没有加载动画，忽略错误
    });
  }

  async navigateToPage(menuText: string) {
    // 点击侧边栏菜单项
    await this.sidebar.locator(`text=${menuText}`).click();
    await this.waitForPageLoad();
  }

  async checkPageTitle(expectedTitle: string) {
    await expect(this.page).toHaveTitle(new RegExp(expectedTitle, 'i'));
  }

  async checkPageHeading(expectedHeading: string) {
    const heading = this.page.locator('h1, h2').first();
    await expect(heading).toContainText(expectedHeading);
  }

  async takeScreenshot(name: string) {
    await this.page.screenshot({ 
      path: `test-results/screenshots/${name}.png`,
      fullPage: true 
    });
  }

  async checkResponsiveLayout() {
    // 检查桌面布局
    await this.page.setViewportSize({ width: 1920, height: 1080 });
    await expect(this.sidebar).toBeVisible();
    
    // 检查平板布局
    await this.page.setViewportSize({ width: 768, height: 1024 });
    await this.page.waitForTimeout(500);
    
    // 检查移动端布局
    await this.page.setViewportSize({ width: 375, height: 667 });
    await this.page.waitForTimeout(500);
    
    // 恢复桌面布局
    await this.page.setViewportSize({ width: 1920, height: 1080 });
  }

  async checkWebSocketConnection() {
    // 检查WebSocket连接状态指示器
    const wsStatus = this.page.locator('[data-testid="ws-status"], .status, text=连接');
    if (await wsStatus.count() > 0) {
      await expect(wsStatus.first()).toBeVisible();
    }
  }

  async waitForElement(selector: string, timeout: number = 5000) {
    await this.page.waitForSelector(selector, { timeout });
  }

  async clickButton(text: string) {
    await this.page.locator(`button:has-text("${text}")`).click();
  }

  async fillInput(label: string, value: string) {
    const input = this.page.locator(`input[placeholder*="${label}"], input[aria-label*="${label}"]`);
    await input.fill(value);
  }

  async selectOption(label: string, value: string) {
    const select = this.page.locator(`.ant-select:has(.ant-select-selector:has-text("${label}"))`);
    await select.click();
    await this.page.locator(`.ant-select-item:has-text("${value}")`).click();
  }
}
