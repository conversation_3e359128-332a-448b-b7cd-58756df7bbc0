import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class MarketDataPage extends BasePage {
  readonly pageTitle: Locator;
  readonly priceChart: Locator;
  readonly marketDataTable: Locator;
  readonly exchangeSelector: Locator;
  readonly tradingPairSelector: Locator;
  readonly refreshButton: Locator;
  readonly realTimeIndicator: Locator;
  readonly orderBookSection: Locator;
  readonly tradesSection: Locator;

  constructor(page: Page) {
    super(page);
    this.pageTitle = page.locator('h1, h2').filter({ hasText: /市场数据|Market Data/ });
    this.priceChart = page.locator('[data-testid="price-chart"], .market-chart, svg');
    this.marketDataTable = page.locator('.ant-table');
    this.exchangeSelector = page.locator('.ant-select:has(.ant-select-selector:has-text("交易所"))');
    this.tradingPairSelector = page.locator('.ant-select:has(.ant-select-selector:has-text("交易对"))');
    this.refreshButton = page.locator('button:has-text("刷新")');
    this.realTimeIndicator = page.locator('.ant-tag:has-text("实时"), .real-time-indicator');
    this.orderBookSection = page.locator('[data-testid="order-book"], .order-book');
    this.tradesSection = page.locator('[data-testid="recent-trades"], .recent-trades');
  }

  async goto() {
    await super.goto('/market-data');
  }

  async checkMarketDataElements() {
    // 检查页面基本元素
    await expect(this.pageTitle).toBeVisible();
    
    // 检查图表区域
    await expect(this.priceChart).toBeVisible({ timeout: 10000 });
    
    // 检查数据表格
    await expect(this.marketDataTable).toBeVisible();
  }

  async checkPriceChart() {
    // 检查价格图表
    await expect(this.priceChart).toBeVisible();
    
    // 检查SVG图表元素
    const svgElements = this.priceChart.locator('svg');
    if (await svgElements.count() > 0) {
      await expect(svgElements.first()).toBeVisible();
      console.log('✅ SVG价格图表渲染正常');
    }
    
    // 检查图表数据点
    const dataPoints = this.priceChart.locator('circle, path, line');
    const pointCount = await dataPoints.count();
    console.log(`图表包含 ${pointCount} 个数据元素`);
  }

  async checkMarketDataTable() {
    // 检查市场数据表格
    const expectedColumns = [
      '交易对',
      '最新价格',
      '24h变化',
      '24h成交量',
      '更新时间'
    ];
    
    for (const column of expectedColumns) {
      const columnHeader = this.page.locator(`.ant-table-thead th:has-text("${column}")`);
      if (await columnHeader.count() > 0) {
        await expect(columnHeader).toBeVisible();
      }
    }
    
    // 检查数据行
    const tableRows = this.page.locator('.ant-table-tbody tr');
    const rowCount = await tableRows.count();
    
    if (rowCount > 0) {
      console.log(`市场数据表格包含 ${rowCount} 行数据`);
      
      // 检查第一行数据
      const firstRow = tableRows.first();
      await expect(firstRow).toBeVisible();
    } else {
      // 检查空状态
      const emptyState = this.page.locator('.ant-empty');
      await expect(emptyState).toBeVisible();
    }
  }

  async selectExchange(exchangeName: string) {
    // 选择交易所
    if (await this.exchangeSelector.count() > 0) {
      await this.exchangeSelector.click();
      await this.page.locator(`.ant-select-item:has-text("${exchangeName}")`).click();
      await this.waitForPageLoad();
    }
  }

  async selectTradingPair(pairName: string) {
    // 选择交易对
    if (await this.tradingPairSelector.count() > 0) {
      await this.tradingPairSelector.click();
      await this.page.locator(`.ant-select-item:has-text("${pairName}")`).click();
      await this.waitForPageLoad();
    }
  }

  async refreshMarketData() {
    // 刷新市场数据
    if (await this.refreshButton.count() > 0) {
      await this.refreshButton.click();
      await this.waitForPageLoad();
    } else {
      await this.page.reload();
      await this.waitForPageLoad();
    }
  }

  async checkRealTimeUpdates() {
    // 检查实时更新指示器
    if (await this.realTimeIndicator.count() > 0) {
      await expect(this.realTimeIndicator).toBeVisible();
      console.log('✅ 实时数据指示器可见');
    }
    
    // 检查数据是否在更新
    const priceElements = this.page.locator('.price, .ant-statistic-content-value');
    if (await priceElements.count() > 0) {
      const initialPrice = await priceElements.first().textContent();
      
      // 等待一段时间后检查价格是否变化
      await this.page.waitForTimeout(3000);
      const updatedPrice = await priceElements.first().textContent();
      
      console.log(`初始价格: ${initialPrice}, 更新后价格: ${updatedPrice}`);
    }
  }

  async checkOrderBook() {
    // 检查订单簿
    if (await this.orderBookSection.count() > 0) {
      await expect(this.orderBookSection).toBeVisible();
      
      // 检查买单和卖单
      const buyOrders = this.orderBookSection.locator('.buy-orders, .bids');
      const sellOrders = this.orderBookSection.locator('.sell-orders, .asks');
      
      if (await buyOrders.count() > 0) {
        await expect(buyOrders).toBeVisible();
      }
      
      if (await sellOrders.count() > 0) {
        await expect(sellOrders).toBeVisible();
      }
      
      console.log('✅ 订单簿区域检查完成');
    }
  }

  async checkRecentTrades() {
    // 检查最近交易
    if (await this.tradesSection.count() > 0) {
      await expect(this.tradesSection).toBeVisible();
      
      // 检查交易记录
      const tradeItems = this.tradesSection.locator('.trade-item, tr');
      const tradeCount = await tradeItems.count();
      
      if (tradeCount > 0) {
        console.log(`最近交易记录: ${tradeCount} 条`);
      }
    }
  }

  async checkDataVisualization() {
    // 检查数据可视化组件
    const chartComponents = [
      '.market-chart',
      '.price-chart', 
      '.volume-chart',
      'svg',
      'canvas'
    ];
    
    for (const component of chartComponents) {
      const elements = this.page.locator(component);
      const count = await elements.count();
      
      if (count > 0) {
        await expect(elements.first()).toBeVisible();
        console.log(`✅ ${component} 可视化组件正常显示`);
      }
    }
  }

  async testMarketDataFlow() {
    // 测试完整的市场数据流程
    console.log('🔄 开始测试市场数据流程...');
    
    // 1. 检查页面加载
    await this.checkMarketDataElements();
    
    // 2. 检查价格图表
    await this.checkPriceChart();
    
    // 3. 检查数据表格
    await this.checkMarketDataTable();
    
    // 4. 测试数据刷新
    await this.refreshMarketData();
    
    // 5. 检查实时更新
    await this.checkRealTimeUpdates();
    
    // 6. 检查订单簿（如果存在）
    await this.checkOrderBook();
    
    // 7. 检查最近交易（如果存在）
    await this.checkRecentTrades();
    
    console.log('✅ 市场数据流程测试完成');
  }

  async checkResponsiveMarketData() {
    // 检查市场数据页面的响应式设计
    await this.checkResponsiveLayout();
    
    const viewports = [
      { width: 1920, height: 1080, name: '桌面' },
      { width: 1024, height: 768, name: '平板' },
      { width: 375, height: 667, name: '手机' }
    ];
    
    for (const viewport of viewports) {
      await this.page.setViewportSize(viewport);
      await this.page.waitForTimeout(500);
      
      // 检查关键元素在不同屏幕尺寸下的可见性
      await expect(this.pageTitle).toBeVisible();
      
      if (viewport.width >= 768) {
        // 桌面和平板显示图表
        await expect(this.priceChart).toBeVisible();
      }
      
      console.log(`✅ ${viewport.name}布局检查通过`);
    }
    
    // 恢复桌面布局
    await this.page.setViewportSize({ width: 1920, height: 1080 });
  }
}
