import fs from 'fs';
import path from 'path';

interface TestResult {
  title: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
  screenshots?: string[];
}

interface TestSuite {
  name: string;
  tests: TestResult[];
  duration: number;
  passed: number;
  failed: number;
  skipped: number;
}

class ReportGenerator {
  private results: TestSuite[] = [];
  private outputDir: string;

  constructor(outputDir: string = 'test-results') {
    this.outputDir = outputDir;
    this.ensureOutputDir();
  }

  private ensureOutputDir(): void {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  addTestSuite(suite: TestSuite): void {
    this.results.push(suite);
  }

  generateHTMLReport(): string {
    const reportPath = path.join(this.outputDir, 'ui-test-report.html');
    const html = this.createHTMLContent();
    
    fs.writeFileSync(reportPath, html, 'utf8');
    console.log(`📄 HTML报告已生成: ${reportPath}`);
    
    return reportPath;
  }

  generateJSONReport(): string {
    const reportPath = path.join(this.outputDir, 'ui-test-results.json');
    const jsonData = {
      timestamp: new Date().toISOString(),
      summary: this.getSummary(),
      suites: this.results
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(jsonData, null, 2), 'utf8');
    console.log(`📄 JSON报告已生成: ${reportPath}`);
    
    return reportPath;
  }

  generateMarkdownReport(): string {
    const reportPath = path.join(this.outputDir, 'UI_TEST_REPORT.md');
    const markdown = this.createMarkdownContent();
    
    fs.writeFileSync(reportPath, markdown, 'utf8');
    console.log(`📄 Markdown报告已生成: ${reportPath}`);
    
    return reportPath;
  }

  private getSummary() {
    const totalTests = this.results.reduce((sum, suite) => sum + suite.tests.length, 0);
    const totalPassed = this.results.reduce((sum, suite) => sum + suite.passed, 0);
    const totalFailed = this.results.reduce((sum, suite) => sum + suite.failed, 0);
    const totalSkipped = this.results.reduce((sum, suite) => sum + suite.skipped, 0);
    const totalDuration = this.results.reduce((sum, suite) => sum + suite.duration, 0);

    return {
      total: totalTests,
      passed: totalPassed,
      failed: totalFailed,
      skipped: totalSkipped,
      duration: totalDuration,
      passRate: totalTests > 0 ? (totalPassed / totalTests * 100).toFixed(2) : '0'
    };
  }

  private createHTMLContent(): string {
    const summary = this.getSummary();
    
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFQuant UI 测试报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .header { background: #1890ff; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
        .content { padding: 20px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 6px; text-align: center; border-left: 4px solid #1890ff; }
        .stat-value { font-size: 2em; font-weight: bold; color: #1890ff; }
        .stat-label { color: #666; margin-top: 5px; }
        .suite { margin-bottom: 30px; border: 1px solid #e8e8e8; border-radius: 6px; }
        .suite-header { background: #fafafa; padding: 15px; border-bottom: 1px solid #e8e8e8; font-weight: bold; }
        .test-item { padding: 10px 15px; border-bottom: 1px solid #f0f0f0; display: flex; justify-content: space-between; align-items: center; }
        .test-item:last-child { border-bottom: none; }
        .status { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .passed { background: #f6ffed; color: #52c41a; }
        .failed { background: #fff2f0; color: #ff4d4f; }
        .skipped { background: #fffbe6; color: #faad14; }
        .error { background: #fff2f0; padding: 10px; margin-top: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .timestamp { color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 SFQuant UI 测试报告</h1>
            <div class="timestamp">生成时间: ${new Date().toLocaleString('zh-CN')}</div>
        </div>
        
        <div class="content">
            <div class="summary">
                <div class="stat-card">
                    <div class="stat-value">${summary.total}</div>
                    <div class="stat-label">总测试数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #52c41a">${summary.passed}</div>
                    <div class="stat-label">通过</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #ff4d4f">${summary.failed}</div>
                    <div class="stat-label">失败</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${summary.passRate}%</div>
                    <div class="stat-label">通过率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${(summary.duration / 1000).toFixed(1)}s</div>
                    <div class="stat-label">总耗时</div>
                </div>
            </div>

            ${this.results.map(suite => `
                <div class="suite">
                    <div class="suite-header">
                        📋 ${suite.name} 
                        <span style="float: right; font-weight: normal;">
                            ${suite.passed}/${suite.tests.length} 通过 | ${(suite.duration / 1000).toFixed(1)}s
                        </span>
                    </div>
                    ${suite.tests.map(test => `
                        <div class="test-item">
                            <div>
                                <span class="status ${test.status}">${this.getStatusIcon(test.status)} ${test.status.toUpperCase()}</span>
                                <span style="margin-left: 10px;">${test.title}</span>
                                ${test.error ? `<div class="error">${test.error}</div>` : ''}
                            </div>
                            <div style="color: #666; font-size: 12px;">
                                ${(test.duration / 1000).toFixed(2)}s
                            </div>
                        </div>
                    `).join('')}
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>`;
  }

  private createMarkdownContent(): string {
    const summary = this.getSummary();
    
    return `# 🚀 SFQuant UI 测试报告

**生成时间**: ${new Date().toLocaleString('zh-CN')}

## 📊 测试概览

| 指标 | 数值 |
|------|------|
| 总测试数 | ${summary.total} |
| ✅ 通过 | ${summary.passed} |
| ❌ 失败 | ${summary.failed} |
| ⏭️ 跳过 | ${summary.skipped} |
| 📈 通过率 | ${summary.passRate}% |
| ⏱️ 总耗时 | ${(summary.duration / 1000).toFixed(1)}s |

## 📋 详细结果

${this.results.map(suite => `
### ${suite.name}

**统计**: ${suite.passed}/${suite.tests.length} 通过 | 耗时: ${(suite.duration / 1000).toFixed(1)}s

${suite.tests.map(test => {
  const icon = this.getStatusIcon(test.status);
  const duration = (test.duration / 1000).toFixed(2);
  let result = `- ${icon} **${test.title}** (${duration}s)`;
  
  if (test.error) {
    result += `\n  \`\`\`\n  ${test.error}\n  \`\`\``;
  }
  
  return result;
}).join('\n')}
`).join('\n')}

## 🎯 测试结论

${summary.failed === 0 
  ? '🎉 **所有测试通过！** SFQuant UI功能验证成功。' 
  : `⚠️ **发现 ${summary.failed} 个失败测试**，请检查详细错误信息并修复。`
}

---
*报告由 SFQuant UI 测试系统自动生成*`;
  }

  private getStatusIcon(status: string): string {
    switch (status) {
      case 'passed': return '✅';
      case 'failed': return '❌';
      case 'skipped': return '⏭️';
      default: return '❓';
    }
  }

  generateAllReports(): void {
    console.log('\n📄 生成测试报告...');
    
    this.generateHTMLReport();
    this.generateJSONReport();
    this.generateMarkdownReport();
    
    console.log('\n✅ 所有报告生成完成！');
    console.log(`📁 报告目录: ${this.outputDir}`);
  }
}

export { ReportGenerator, TestResult, TestSuite };
