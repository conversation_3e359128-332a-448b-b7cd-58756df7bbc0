#!/usr/bin/env node

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import path from 'path';

interface TestConfig {
  name: string;
  command: string;
  description: string;
  timeout?: number;
}

const testConfigs: TestConfig[] = [
  {
    name: 'basic',
    command: 'npx playwright test 01-basic-functionality.spec.ts',
    description: '基础功能测试',
    timeout: 300000 // 5分钟
  },
  {
    name: 'dashboard',
    command: 'npx playwright test 02-dashboard.spec.ts',
    description: '仪表盘功能测试',
    timeout: 300000
  },
  {
    name: 'strategy',
    command: 'npx playwright test 03-strategy-management.spec.ts',
    description: '策略管理功能测试',
    timeout: 300000
  },
  {
    name: 'market-data',
    command: 'npx playwright test 04-market-data.spec.ts',
    description: '市场数据功能测试',
    timeout: 300000
  },
  {
    name: 'integration',
    command: 'npx playwright test 05-integration.spec.ts',
    description: '集成测试',
    timeout: 600000 // 10分钟
  },
  {
    name: 'all',
    command: 'npx playwright test',
    description: '运行所有测试',
    timeout: 900000 // 15分钟
  }
];

class TestRunner {
  private testResults: { [key: string]: any } = {};

  async runTest(config: TestConfig): Promise<boolean> {
    console.log(`\n🚀 开始运行: ${config.description}`);
    console.log(`命令: ${config.command}`);
    console.log('=' .repeat(50));

    const startTime = Date.now();

    try {
      const output = execSync(config.command, {
        cwd: process.cwd(),
        encoding: 'utf8',
        timeout: config.timeout || 300000,
        stdio: 'inherit'
      });

      const duration = Date.now() - startTime;
      
      this.testResults[config.name] = {
        success: true,
        duration,
        description: config.description
      };

      console.log(`\n✅ ${config.description} 完成 (${duration}ms)`);
      return true;

    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      this.testResults[config.name] = {
        success: false,
        duration,
        description: config.description,
        error: error.message
      };

      console.error(`\n❌ ${config.description} 失败 (${duration}ms)`);
      console.error('错误信息:', error.message);
      return false;
    }
  }

  async runTests(testNames: string[] = ['all']): Promise<void> {
    console.log('🧪 SFQuant UI 测试运行器');
    console.log('=' .repeat(50));

    // 检查Playwright是否已安装
    if (!this.checkPlaywrightInstallation()) {
      console.error('❌ Playwright 未正确安装');
      process.exit(1);
    }

    // 检查应用是否运行
    if (!await this.checkApplicationRunning()) {
      console.error('❌ 应用未运行，请先启动应用: pnpm dev');
      process.exit(1);
    }

    const testsToRun = testNames.includes('all') 
      ? testConfigs.filter(config => config.name !== 'all')
      : testConfigs.filter(config => testNames.includes(config.name));

    if (testsToRun.length === 0) {
      console.error('❌ 未找到指定的测试');
      this.showUsage();
      process.exit(1);
    }

    console.log(`\n📋 将运行 ${testsToRun.length} 个测试套件:`);
    testsToRun.forEach(config => {
      console.log(`  - ${config.description}`);
    });

    const overallStartTime = Date.now();
    let successCount = 0;

    for (const config of testsToRun) {
      const success = await this.runTest(config);
      if (success) successCount++;
    }

    const overallDuration = Date.now() - overallStartTime;

    // 生成测试报告
    this.generateReport(successCount, testsToRun.length, overallDuration);
  }

  private checkPlaywrightInstallation(): boolean {
    try {
      execSync('npx playwright --version', { stdio: 'ignore' });
      return true;
    } catch {
      return false;
    }
  }

  private async checkApplicationRunning(): Promise<boolean> {
    try {
      const response = await fetch('http://localhost:3001');
      return response.ok;
    } catch {
      return false;
    }
  }

  private generateReport(successCount: number, totalCount: number, duration: number): void {
    console.log('\n' + '=' .repeat(50));
    console.log('📊 测试报告');
    console.log('=' .repeat(50));

    console.log(`总测试数: ${totalCount}`);
    console.log(`成功: ${successCount}`);
    console.log(`失败: ${totalCount - successCount}`);
    console.log(`总耗时: ${(duration / 1000).toFixed(2)}秒`);

    console.log('\n📋 详细结果:');
    Object.entries(this.testResults).forEach(([name, result]) => {
      const status = result.success ? '✅' : '❌';
      const time = (result.duration / 1000).toFixed(2);
      console.log(`  ${status} ${result.description} (${time}s)`);
      
      if (!result.success && result.error) {
        console.log(`     错误: ${result.error}`);
      }
    });

    if (successCount === totalCount) {
      console.log('\n🎉 所有测试通过！');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查错误信息');
    }

    console.log('\n📁 测试报告和截图保存在: test-results/');
    console.log('🌐 查看详细报告: npx playwright show-report');
  }

  private showUsage(): void {
    console.log('\n使用方法:');
    console.log('  npm run test:e2e [测试名称]');
    console.log('\n可用的测试:');
    testConfigs.forEach(config => {
      console.log(`  ${config.name.padEnd(12)} - ${config.description}`);
    });
    console.log('\n示例:');
    console.log('  npm run test:e2e basic      # 运行基础功能测试');
    console.log('  npm run test:e2e dashboard  # 运行仪表盘测试');
    console.log('  npm run test:e2e all        # 运行所有测试');
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const testNames = args.length > 0 ? args : ['all'];

  const runner = new TestRunner();
  await runner.runTests(testNames);
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('测试运行器错误:', error);
    process.exit(1);
  });
}

export { TestRunner, testConfigs };
