import { test, expect } from '@playwright/test';
import { DashboardPage } from './pages/DashboardPage';
import { StrategyPage } from './pages/StrategyPage';
import { MarketDataPage } from './pages/MarketDataPage';

test.describe('SFQuant 集成测试', () => {
  test('完整用户流程测试', async ({ page }) => {
    await test.step('1. 访问仪表盘', async () => {
      const dashboardPage = new DashboardPage(page);
      await dashboardPage.goto();
      await dashboardPage.checkDashboardElements();
      
      console.log('✅ 仪表盘访问成功');
    });

    await test.step('2. 导航到策略管理', async () => {
      await page.click('text=策略管理');
      await page.waitForLoadState('networkidle');
      
      const strategyPage = new StrategyPage(page);
      await strategyPage.checkStrategyPageElements();
      
      console.log('✅ 策略管理页面导航成功');
    });

    await test.step('3. 创建测试策略', async () => {
      const strategyPage = new StrategyPage(page);
      
      // 尝试创建策略
      await strategyPage.openCreateStrategyModal();
      
      const testStrategy = {
        name: `集成测试策略_${Date.now()}`,
        description: '集成测试创建的策略',
        type: '套利策略',
        tradingPair: 'BTC/USDT',
        exchange: 'Binance',
        capital: '2000',
        riskLevel: '中等风险'
      };
      
      await strategyPage.fillStrategyForm(testStrategy);
      await strategyPage.submitStrategyForm();
      
      console.log('✅ 策略创建流程完成');
    });

    await test.step('4. 查看市场数据', async () => {
      await page.click('text=市场数据');
      await page.waitForLoadState('networkidle');
      
      const marketDataPage = new MarketDataPage(page);
      await marketDataPage.checkMarketDataElements();
      await marketDataPage.checkPriceChart();
      
      console.log('✅ 市场数据页面访问成功');
    });

    await test.step('5. 返回仪表盘验证', async () => {
      await page.click('text=仪表盘');
      await page.waitForLoadState('networkidle');
      
      const dashboardPage = new DashboardPage(page);
      await dashboardPage.checkDashboardElements();
      
      // 检查是否有新的策略数据
      await dashboardPage.checkSystemStats();
      
      console.log('✅ 完整流程测试完成');
    });
  });

  test('跨页面数据一致性测试', async ({ page }) => {
    let dashboardStrategyCount = 0;
    let strategyPageCount = 0;

    await test.step('获取仪表盘策略数据', async () => {
      const dashboardPage = new DashboardPage(page);
      await dashboardPage.goto();
      
      // 获取仪表盘显示的策略数量
      const strategyCountElement = page.locator('.ant-statistic:has(.ant-statistic-title:has-text("活跃策略")) .ant-statistic-content-value');
      if (await strategyCountElement.count() > 0) {
        const countText = await strategyCountElement.textContent();
        dashboardStrategyCount = parseInt(countText?.replace(/\D/g, '') || '0');
      }
      
      console.log(`仪表盘显示策略数量: ${dashboardStrategyCount}`);
    });

    await test.step('获取策略页面数据', async () => {
      const strategyPage = new StrategyPage(page);
      await strategyPage.goto();
      
      // 获取策略页面显示的策略数量
      const strategyCountElement = page.locator('.ant-statistic:has(.ant-statistic-title:has-text("总策略数")) .ant-statistic-content-value');
      if (await strategyCountElement.count() > 0) {
        const countText = await strategyCountElement.textContent();
        strategyPageCount = parseInt(countText?.replace(/\D/g, '') || '0');
      }
      
      console.log(`策略页面显示策略数量: ${strategyPageCount}`);
    });

    await test.step('验证数据一致性', async () => {
      // 数据可能不完全一致（活跃策略 vs 总策略），但应该在合理范围内
      console.log(`数据一致性检查: 仪表盘=${dashboardStrategyCount}, 策略页面=${strategyPageCount}`);
      
      // 基础合理性检查
      expect(dashboardStrategyCount).toBeGreaterThanOrEqual(0);
      expect(strategyPageCount).toBeGreaterThanOrEqual(0);
      expect(strategyPageCount).toBeGreaterThanOrEqual(dashboardStrategyCount);
    });
  });

  test('WebSocket连接状态一致性', async ({ page }) => {
    const pages = [
      { name: '仪表盘', path: '/' },
      { name: '策略管理', path: '/strategies' },
      { name: '市场数据', path: '/market-data' }
    ];

    const connectionStates: { [key: string]: boolean } = {};

    for (const pageInfo of pages) {
      await test.step(`检查${pageInfo.name}WebSocket状态`, async () => {
        await page.goto(pageInfo.path);
        await page.waitForLoadState('networkidle');
        
        // 检查WebSocket连接状态指示器
        const wsStatus = page.locator('.ant-tag:has-text("连接"), .ant-tag:has-text("断开"), .status');
        
        if (await wsStatus.count() > 0) {
          const statusText = await wsStatus.first().textContent();
          const isConnected = statusText?.includes('连接') || statusText?.includes('实时');
          connectionStates[pageInfo.name] = isConnected || false;
          
          console.log(`${pageInfo.name} WebSocket状态: ${statusText}`);
        } else {
          connectionStates[pageInfo.name] = false;
          console.log(`${pageInfo.name} 未找到WebSocket状态指示器`);
        }
      });
    }

    await test.step('验证连接状态一致性', async () => {
      const states = Object.values(connectionStates);
      const allSame = states.every(state => state === states[0]);
      
      if (allSame) {
        console.log('✅ 所有页面WebSocket状态一致');
      } else {
        console.log('⚠️ 不同页面WebSocket状态不一致');
        console.log('状态详情:', connectionStates);
      }
    });
  });

  test('应用状态持久化测试', async ({ page }) => {
    let initialUrl = '';
    let initialData: any = {};

    await test.step('记录初始状态', async () => {
      await page.goto('/strategies');
      await page.waitForLoadState('networkidle');
      
      initialUrl = page.url();
      
      // 记录一些页面状态
      const strategyStats = await page.locator('.ant-statistic-content-value').allTextContents();
      initialData.strategyStats = strategyStats;
      
      console.log('初始URL:', initialUrl);
      console.log('初始策略统计:', strategyStats);
    });

    await test.step('页面刷新测试', async () => {
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // 验证URL保持不变
      expect(page.url()).toBe(initialUrl);
      
      // 验证数据重新加载
      const refreshedStats = await page.locator('.ant-statistic-content-value').allTextContents();
      console.log('刷新后策略统计:', refreshedStats);
      
      // 数据应该重新加载（可能相同也可能不同）
      expect(refreshedStats.length).toBe(initialData.strategyStats.length);
    });

    await test.step('浏览器前进后退测试', async () => {
      // 导航到其他页面
      await page.goto('/market-data');
      await page.waitForLoadState('networkidle');
      
      // 使用浏览器后退
      await page.goBack();
      await page.waitForLoadState('networkidle');
      
      // 验证返回到策略页面
      expect(page.url()).toContain('/strategies');
      
      // 使用浏览器前进
      await page.goForward();
      await page.waitForLoadState('networkidle');
      
      // 验证前进到市场数据页面
      expect(page.url()).toContain('/market-data');
    });
  });

  test('错误恢复和容错性测试', async ({ page }) => {
    await test.step('网络中断恢复测试', async () => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // 模拟网络中断
      await page.route('**/*', route => route.abort());
      
      // 尝试导航
      await page.click('text=策略管理').catch(() => {
        console.log('网络中断时导航失败（预期行为）');
      });
      
      // 恢复网络
      await page.unroute('**/*');
      
      // 重新尝试导航
      await page.goto('/strategies');
      await page.waitForLoadState('networkidle');
      
      // 验证页面正常加载
      const content = page.locator('.ant-layout-content');
      await expect(content).toBeVisible();
      
      console.log('✅ 网络恢复后页面正常');
    });

    await test.step('API错误处理测试', async () => {
      // 模拟API错误
      await page.route('**/api/**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal Server Error' })
        });
      });
      
      await page.goto('/strategies');
      await page.waitForLoadState('networkidle');
      
      // 检查错误处理
      await page.waitForTimeout(2000);
      
      // 恢复API
      await page.unroute('**/api/**');
      
      // 刷新页面验证恢复
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      console.log('✅ API错误恢复测试完成');
    });
  });

  test('性能和资源使用测试', async ({ page }) => {
    const performanceData: any = {};

    await test.step('测量页面加载性能', async () => {
      const pages = ['/', '/strategies', '/market-data'];
      
      for (const path of pages) {
        const startTime = Date.now();
        await page.goto(path);
        await page.waitForLoadState('networkidle');
        const loadTime = Date.now() - startTime;
        
        performanceData[path] = { loadTime };
        console.log(`${path} 加载时间: ${loadTime}ms`);
        
        // 性能断言
        expect(loadTime).toBeLessThan(10000);
      }
    });

    await test.step('检查内存使用', async () => {
      // 检查DOM元素数量
      const elementCount = await page.locator('*').count();
      console.log(`DOM元素总数: ${elementCount}`);
      
      // 检查图片资源
      const imageCount = await page.locator('img').count();
      console.log(`图片数量: ${imageCount}`);
      
      // 基础资源使用断言
      expect(elementCount).toBeLessThan(3000);
      expect(imageCount).toBeLessThan(50);
    });

    await test.step('性能总结', async () => {
      console.log('性能测试总结:', performanceData);
      
      const avgLoadTime = Object.values(performanceData)
        .map((data: any) => data.loadTime)
        .reduce((a: number, b: number) => a + b, 0) / Object.keys(performanceData).length;
      
      console.log(`平均页面加载时间: ${avgLoadTime.toFixed(2)}ms`);
      expect(avgLoadTime).toBeLessThan(5000);
    });
  });
});
