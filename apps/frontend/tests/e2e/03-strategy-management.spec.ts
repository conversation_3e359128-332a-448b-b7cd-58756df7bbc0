import { test, expect } from '@playwright/test';
import { StrategyPage } from './pages/StrategyPage';

test.describe('策略管理功能测试', () => {
  let strategyPage: StrategyPage;

  test.beforeEach(async ({ page }) => {
    strategyPage = new StrategyPage(page);
    await strategyPage.goto();
  });

  test('策略管理页面基础功能', async ({ page }) => {
    await test.step('检查页面基础元素', async () => {
      await strategyPage.checkStrategyPageElements();
    });

    await test.step('检查策略统计数据', async () => {
      await strategyPage.checkStrategyStats();
    });

    await test.step('检查策略表格', async () => {
      await strategyPage.checkStrategyTable();
    });

    await test.step('截图记录', async () => {
      await strategyPage.takeScreenshot('strategy-management-overview');
    });
  });

  test('策略创建表单验证', async ({ page }) => {
    await test.step('测试表单验证', async () => {
      await strategyPage.checkFormValidation();
    });

    await test.step('测试表单取消功能', async () => {
      await strategyPage.openCreateStrategyModal();
      await strategyPage.cancelStrategyForm();
    });
  });

  test('策略创建完整流程', async ({ page }) => {
    await test.step('创建测试策略', async () => {
      const testStrategy = await strategyPage.testCreateStrategy();
      console.log('创建的测试策略:', testStrategy);
    });

    await test.step('验证策略创建结果', async () => {
      // 刷新页面检查策略是否被保存
      await strategyPage.refreshStrategies();
      
      // 检查表格是否有数据更新
      await strategyPage.checkStrategyTable();
    });

    await test.step('截图记录创建结果', async () => {
      await strategyPage.takeScreenshot('strategy-created');
    });
  });

  test('策略表单字段测试', async ({ page }) => {
    await test.step('打开创建策略表单', async () => {
      await strategyPage.openCreateStrategyModal();
    });

    await test.step('测试所有表单字段', async () => {
      // 测试策略名称字段
      await page.locator('input[placeholder*="策略名称"]').fill('测试策略名称');
      await page.locator('input[placeholder*="策略名称"]').clear();
      
      // 测试描述字段
      const descriptionField = page.locator('textarea[placeholder*="描述"]');
      if (await descriptionField.count() > 0) {
        await descriptionField.fill('这是一个测试描述');
        await descriptionField.clear();
      }
      
      // 测试策略类型下拉框
      const strategyTypeSelect = page.locator('.ant-select:has(.ant-select-selector:has-text("策略类型"))');
      if (await strategyTypeSelect.count() > 0) {
        await strategyTypeSelect.click();
        
        // 检查可选项
        const options = page.locator('.ant-select-item');
        const optionCount = await options.count();
        console.log(`策略类型选项数量: ${optionCount}`);
        
        if (optionCount > 0) {
          await options.first().click();
        }
      }
      
      // 测试交易对字段
      await page.locator('input[placeholder*="交易对"]').fill('BTC/USDT');
      
      // 测试交易所下拉框
      const exchangeSelect = page.locator('.ant-select:has(.ant-select-selector:has-text("交易所"))');
      if (await exchangeSelect.count() > 0) {
        await exchangeSelect.click();
        
        const exchangeOptions = page.locator('.ant-select-item');
        const exchangeCount = await exchangeOptions.count();
        console.log(`交易所选项数量: ${exchangeCount}`);
        
        if (exchangeCount > 0) {
          await exchangeOptions.first().click();
        }
      }
      
      // 测试投入资金字段
      const capitalField = page.locator('input[placeholder*="投入资金"]');
      if (await capitalField.count() > 0) {
        await capitalField.fill('1000');
      }
      
      // 测试风险等级下拉框
      const riskSelect = page.locator('.ant-select:has(.ant-select-selector:has-text("风险等级"))');
      if (await riskSelect.count() > 0) {
        await riskSelect.click();
        
        const riskOptions = page.locator('.ant-select-item');
        const riskCount = await riskOptions.count();
        console.log(`风险等级选项数量: ${riskCount}`);
        
        if (riskCount > 0) {
          await riskOptions.first().click();
        }
      }
    });

    await test.step('关闭表单', async () => {
      await strategyPage.cancelStrategyForm();
    });
  });

  test('策略批量操作功能', async ({ page }) => {
    await test.step('检查批量操作按钮', async () => {
      await strategyPage.checkBatchOperations();
    });

    await test.step('测试批量操作交互', async () => {
      const batchButtons = page.locator('button:has-text("批量"), button:has-text("全部")');
      const buttonCount = await batchButtons.count();
      
      for (let i = 0; i < buttonCount; i++) {
        const button = batchButtons.nth(i);
        const buttonText = await button.textContent();
        
        // 悬停测试
        await button.hover();
        await page.waitForTimeout(300);
        
        console.log(`✅ 批量操作按钮 "${buttonText}" 交互正常`);
      }
    });
  });

  test('策略信号和实时数据', async ({ page }) => {
    await test.step('检查策略信号区域', async () => {
      await strategyPage.checkStrategySignals();
    });

    await test.step('检查实时数据更新', async () => {
      // 等待一段时间观察数据更新
      await page.waitForTimeout(3000);
      
      // 检查WebSocket连接状态
      await strategyPage.checkWebSocketConnection();
    });

    await test.step('测试数据刷新', async () => {
      await strategyPage.refreshStrategies();
    });
  });

  test('策略管理响应式设计', async ({ page }) => {
    await test.step('测试不同屏幕尺寸', async () => {
      const viewports = [
        { width: 1920, height: 1080, name: '桌面大屏' },
        { width: 1024, height: 768, name: '平板' },
        { width: 375, height: 667, name: '手机' }
      ];
      
      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        await page.waitForTimeout(500);
        
        // 检查关键元素可见性
        await expect(strategyPage.createStrategyButton).toBeVisible();
        await expect(strategyPage.strategyTable).toBeVisible();
        
        // 在移动端检查表格是否可滚动
        if (viewport.width < 768) {
          const tableWrapper = page.locator('.ant-table-wrapper');
          const hasScroll = await tableWrapper.evaluate(el => 
            el.scrollWidth > el.clientWidth
          );
          console.log(`${viewport.name}: 表格${hasScroll ? '可' : '不可'}滚动`);
        }
        
        console.log(`✅ ${viewport.name}布局检查通过`);
      }
      
      // 恢复桌面布局
      await page.setViewportSize({ width: 1920, height: 1080 });
    });

    await test.step('移动端截图', async () => {
      await page.setViewportSize({ width: 375, height: 667 });
      await strategyPage.takeScreenshot('strategy-management-mobile');
      await page.setViewportSize({ width: 1920, height: 1080 });
    });
  });

  test('策略管理错误处理', async ({ page }) => {
    await test.step('测试网络错误处理', async () => {
      // 模拟API错误
      await page.route('**/api/strategies**', route => route.abort());
      
      // 尝试创建策略
      await strategyPage.openCreateStrategyModal();
      
      const testStrategy = {
        name: '网络错误测试策略',
        type: '套利策略',
        tradingPair: 'BTC/USDT',
        exchange: 'Binance',
        riskLevel: '低风险'
      };
      
      await strategyPage.fillStrategyForm(testStrategy);
      await strategyPage.submitStrategyForm();
      
      // 检查错误处理
      await page.waitForTimeout(2000);
      
      // 恢复网络
      await page.unroute('**/api/strategies**');
      
      // 关闭可能的错误模态框
      const modal = page.locator('.ant-modal:visible');
      if (await modal.count() > 0) {
        const cancelButton = modal.locator('button:has-text("取消"), button:has-text("关闭")');
        if (await cancelButton.count() > 0) {
          await cancelButton.click();
        }
      }
    });

    await test.step('检查表单验证错误', async () => {
      await strategyPage.openCreateStrategyModal();
      
      // 提交空表单
      const submitButton = page.locator('.ant-modal button:has-text("确定"), .ant-modal button:has-text("创建")');
      await submitButton.click();
      
      // 检查验证错误
      const errorMessages = page.locator('.ant-form-item-explain-error');
      const errorCount = await errorMessages.count();
      
      if (errorCount > 0) {
        console.log(`表单验证错误数量: ${errorCount}`);
        for (let i = 0; i < errorCount; i++) {
          const error = errorMessages.nth(i);
          const errorText = await error.textContent();
          console.log(`验证错误 ${i + 1}: ${errorText}`);
        }
      }
      
      await strategyPage.cancelStrategyForm();
    });
  });

  test('策略管理性能测试', async ({ page }) => {
    await test.step('页面加载性能', async () => {
      const startTime = Date.now();
      await strategyPage.goto();
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      console.log(`策略管理页面加载时间: ${loadTime}ms`);
      expect(loadTime).toBeLessThan(5000);
    });

    await test.step('表单操作性能', async () => {
      const startTime = Date.now();
      await strategyPage.openCreateStrategyModal();
      const modalOpenTime = Date.now() - startTime;
      
      console.log(`策略创建模态框打开时间: ${modalOpenTime}ms`);
      expect(modalOpenTime).toBeLessThan(1000);
      
      await strategyPage.cancelStrategyForm();
    });
  });
});
