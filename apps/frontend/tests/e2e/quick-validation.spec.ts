import { test, expect } from '@playwright/test';

test.describe('SFQuant 快速UI验证', () => {
  test('应用基础功能验证', async ({ page }) => {
    console.log('🚀 开始SFQuant UI快速验证...');

    await test.step('1. 访问应用首页', async () => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // 检查页面标题
      await expect(page).toHaveTitle(/SFQuant/);
      console.log('✅ 页面标题正确');
      
      // 检查主要布局元素
      const mainLayout = page.locator('.ant-layout').first();
      await expect(mainLayout).toBeVisible();
      console.log('✅ 主布局加载成功');
    });

    await test.step('2. 验证导航菜单', async () => {
      // 检查侧边栏
      const sidebar = page.locator('.ant-layout-sider');
      await expect(sidebar).toBeVisible();
      
      // 检查菜单项
      const menuItems = ['仪表盘', '策略管理', '市场数据'];
      for (const item of menuItems) {
        const menuItem = page.locator(`text=${item}`);
        await expect(menuItem).toBeVisible();
        console.log(`✅ 菜单项 "${item}" 可见`);
      }
    });

    await test.step('3. 测试页面导航', async () => {
      // 导航到策略管理页面
      await page.click('text=策略管理');
      await page.waitForLoadState('networkidle');
      expect(page.url()).toContain('/strategies');
      console.log('✅ 策略管理页面导航成功');
      
      // 导航到市场数据页面
      await page.click('text=市场数据');
      await page.waitForLoadState('networkidle');
      expect(page.url()).toContain('/market-data');
      console.log('✅ 市场数据页面导航成功');
      
      // 返回仪表盘
      await page.click('text=仪表盘');
      await page.waitForLoadState('networkidle');
      expect(page.url()).toMatch(/\/$|\/$/);
      console.log('✅ 仪表盘页面导航成功');
    });

    await test.step('4. 验证仪表盘内容', async () => {
      // 检查页面标题
      const pageTitle = page.locator('h1, h2').filter({ hasText: /SFQuant/ });
      await expect(pageTitle).toBeVisible();
      console.log('✅ 仪表盘标题显示正常');
      
      // 检查统计卡片
      const statCards = page.locator('.ant-statistic');
      const cardCount = await statCards.count();
      expect(cardCount).toBeGreaterThan(0);
      console.log(`✅ 发现 ${cardCount} 个统计卡片`);
      
      // 检查WebSocket连接状态
      const wsStatus = page.locator('.ant-tag').filter({ hasText: /连接|断开/ });
      if (await wsStatus.count() > 0) {
        const statusText = await wsStatus.first().textContent();
        console.log(`📡 WebSocket状态: ${statusText}`);
      }
    });

    await test.step('5. 验证策略管理功能', async () => {
      await page.click('text=策略管理');
      await page.waitForLoadState('networkidle');
      
      // 检查创建策略按钮
      const createButton = page.locator('button').filter({ hasText: /创建策略|新建策略/ });
      await expect(createButton).toBeVisible();
      console.log('✅ 创建策略按钮可见');
      
      // 检查策略表格
      const strategyTable = page.locator('.ant-table');
      await expect(strategyTable).toBeVisible();
      console.log('✅ 策略表格显示正常');
      
      // 测试创建策略模态框
      await createButton.click();
      const modal = page.locator('.ant-modal');
      await expect(modal).toBeVisible();
      console.log('✅ 策略创建模态框打开成功');
      
      // 关闭模态框
      const cancelButton = modal.locator('button').filter({ hasText: /取消|关闭/ });
      if (await cancelButton.count() > 0) {
        await cancelButton.click();
        await expect(modal).toBeHidden();
        console.log('✅ 模态框关闭成功');
      }
    });

    await test.step('6. 验证市场数据功能', async () => {
      await page.click('text=市场数据');
      await page.waitForLoadState('networkidle');
      
      // 检查图表区域
      const chartArea = page.locator('svg, canvas, .chart').first();
      if (await chartArea.count() > 0) {
        await expect(chartArea).toBeVisible();
        console.log('✅ 市场数据图表显示正常');
      }
      
      // 检查数据表格
      const dataTable = page.locator('.ant-table');
      await expect(dataTable).toBeVisible();
      console.log('✅ 市场数据表格显示正常');
    });

    await test.step('7. 验证响应式设计', async () => {
      // 测试平板尺寸
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.waitForTimeout(500);
      
      const layout = page.locator('.ant-layout').first();
      await expect(layout).toBeVisible();
      console.log('✅ 平板尺寸布局正常');
      
      // 测试手机尺寸
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(500);
      
      await expect(layout).toBeVisible();
      console.log('✅ 手机尺寸布局正常');
      
      // 恢复桌面尺寸
      await page.setViewportSize({ width: 1920, height: 1080 });
      await page.waitForTimeout(500);
    });

    await test.step('8. 性能检查', async () => {
      // 检查页面加载性能
      const startTime = Date.now();
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      console.log(`⏱️ 页面加载时间: ${loadTime}ms`);
      expect(loadTime).toBeLessThan(5000); // 5秒内加载完成
      
      // 检查DOM元素数量
      const elementCount = await page.locator('*').count();
      console.log(`📊 DOM元素数量: ${elementCount}`);
      expect(elementCount).toBeLessThan(2000);
    });

    await test.step('9. 错误检查', async () => {
      // 检查控制台错误
      const errors: string[] = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });
      
      // 刷新页面触发可能的错误
      await page.reload();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // 检查页面上的错误提示
      const errorAlerts = page.locator('.ant-alert-error, .ant-message-error');
      const errorCount = await errorAlerts.count();
      
      if (errorCount > 0) {
        console.log(`⚠️ 发现 ${errorCount} 个页面错误提示`);
      } else {
        console.log('✅ 未发现页面错误提示');
      }
      
      console.log(`📝 控制台错误数量: ${errors.length}`);
    });

    console.log('🎉 SFQuant UI快速验证完成！');
  });

  test('PWA功能验证', async ({ page }) => {
    await test.step('检查PWA配置', async () => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // 检查Service Worker
      const swSupport = await page.evaluate(() => 'serviceWorker' in navigator);
      expect(swSupport).toBe(true);
      console.log('✅ Service Worker支持正常');
      
      // 检查manifest文件
      const manifestLinks = page.locator('link[rel="manifest"]');
      const manifestCount = await manifestLinks.count();
      expect(manifestCount).toBeGreaterThan(0);
      console.log(`✅ 发现 ${manifestCount} 个manifest链接`);
      
      // 检查PWA图标
      const appleIcon = page.locator('link[rel="apple-touch-icon"]');
      if (await appleIcon.count() > 0) {
        console.log('✅ PWA图标配置正常');
      }
      
      // 检查主题色
      const themeColor = page.locator('meta[name="theme-color"]');
      const themeCount = await themeColor.count();
      expect(themeCount).toBeGreaterThan(0);
      console.log('✅ PWA主题色配置正常');
    });
  });

  test('技术指标计算验证', async ({ page }) => {
    await test.step('测试技术指标功能', async () => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // 查找技术指标计算按钮
      const calculateButton = page.locator('button').filter({ hasText: /计算技术指标|技术指标/ });
      
      if (await calculateButton.count() > 0) {
        await calculateButton.click();
        await page.waitForTimeout(3000);
        
        // 检查是否有成功消息
        const successMessage = page.locator('.ant-message-success, .ant-notification-success');
        if (await successMessage.count() > 0) {
          console.log('✅ 技术指标计算成功');
        }
        
        // 检查指标结果显示
        const indicatorCards = page.locator('.ant-card').filter({ hasText: /SMA|EMA|RSI|MACD/ });
        const cardCount = await indicatorCards.count();
        if (cardCount > 0) {
          console.log(`✅ 发现 ${cardCount} 个技术指标结果卡片`);
        }
      } else {
        console.log('ℹ️ 未找到技术指标计算按钮');
      }
    });
  });
});
