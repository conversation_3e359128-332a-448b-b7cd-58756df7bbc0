{"name": "@sfquant/frontend", "version": "0.1.0", "description": "SFQuant PWA Frontend Application", "type": "module", "scripts": {"dev": "vite --port 3001", "build": "npm run build:wasm && tsc && vite build", "build:wasm": "cd ../../wasm && wasm-pack build --target web --out-dir ../apps/frontend/public/wasm", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "pwa:audit": "lighthouse http://localhost:3001 --only-categories=pwa --chrome-flags='--headless'", "analyze": "npm run build && npx vite-bundle-analyzer dist"}, "dependencies": {"@ant-design/charts": "^2.0.0", "@ant-design/icons": "^5.2.0", "@tanstack/react-query": "^5.0.0", "@types/react-router-dom": "^5.3.3", "antd": "^5.12.0", "axios": "^1.9.0", "comlink": "^4.4.0", "dayjs": "^1.11.0", "idb": "^8.0.0", "lodash-es": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "workbox-window": "^7.0.0", "zustand": "^4.4.0"}, "devDependencies": {"@playwright/test": "^1.52.0", "@types/lodash-es": "^4.17.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "@vitest/ui": "^1.0.0", "c8": "^8.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "lighthouse": "^11.0.0", "playwright": "^1.52.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-bundle-analyzer": "^0.7.0", "vite-plugin-pwa": "^0.17.0", "vite-plugin-windicss": "^1.9.0", "vitest": "^1.0.0", "windicss": "^3.5.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}