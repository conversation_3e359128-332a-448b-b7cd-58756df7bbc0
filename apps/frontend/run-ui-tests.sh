#!/bin/bash

# SFQuant UI 测试运行脚本
# 使用方法: ./run-ui-tests.sh [test-type]
# test-type: quick | full | basic | dashboard | strategy | market-data

set -e

echo "🚀 SFQuant UI 测试运行器"
echo "=========================="

# 检查参数
TEST_TYPE=${1:-quick}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查应用是否运行
check_app_running() {
    echo -e "${BLUE}🔍 检查应用状态...${NC}"
    
    if curl -s http://localhost:3001 > /dev/null; then
        echo -e "${GREEN}✅ 应用正在运行 (http://localhost:3001)${NC}"
        return 0
    else
        echo -e "${RED}❌ 应用未运行${NC}"
        echo -e "${YELLOW}请先启动应用: pnpm dev${NC}"
        return 1
    fi
}

# 检查Playwright安装
check_playwright() {
    echo -e "${BLUE}🔍 检查Playwright安装...${NC}"
    
    if npx playwright --version > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Playwright已安装${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️ Playwright未安装，正在安装...${NC}"
        npx playwright install
        return $?
    fi
}

# 运行测试
run_tests() {
    local test_type=$1
    echo -e "${BLUE}🧪 运行测试类型: ${test_type}${NC}"
    
    case $test_type in
        "quick")
            echo -e "${BLUE}运行快速验证测试...${NC}"
            npx playwright test quick-validation.spec.ts --project=chromium
            ;;
        "basic")
            echo -e "${BLUE}运行基础功能测试...${NC}"
            npx playwright test 01-basic-functionality.spec.ts --project=chromium
            ;;
        "dashboard")
            echo -e "${BLUE}运行仪表盘测试...${NC}"
            npx playwright test 02-dashboard.spec.ts --project=chromium
            ;;
        "strategy")
            echo -e "${BLUE}运行策略管理测试...${NC}"
            npx playwright test 03-strategy-management.spec.ts --project=chromium
            ;;
        "market-data")
            echo -e "${BLUE}运行市场数据测试...${NC}"
            npx playwright test 04-market-data.spec.ts --project=chromium
            ;;
        "integration")
            echo -e "${BLUE}运行集成测试...${NC}"
            npx playwright test 05-integration.spec.ts --project=chromium
            ;;
        "full")
            echo -e "${BLUE}运行完整测试套件...${NC}"
            npx playwright test --project=chromium
            ;;
        "all-browsers")
            echo -e "${BLUE}运行跨浏览器测试...${NC}"
            npx playwright test
            ;;
        *)
            echo -e "${RED}❌ 未知的测试类型: $test_type${NC}"
            show_usage
            exit 1
            ;;
    esac
}

# 显示使用说明
show_usage() {
    echo -e "${YELLOW}使用方法:${NC}"
    echo "  ./run-ui-tests.sh [test-type]"
    echo ""
    echo -e "${YELLOW}可用的测试类型:${NC}"
    echo "  quick        - 快速验证测试 (推荐)"
    echo "  basic        - 基础功能测试"
    echo "  dashboard    - 仪表盘功能测试"
    echo "  strategy     - 策略管理测试"
    echo "  market-data  - 市场数据测试"
    echo "  integration  - 集成测试"
    echo "  full         - 完整测试套件 (单浏览器)"
    echo "  all-browsers - 跨浏览器测试"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo "  ./run-ui-tests.sh quick"
    echo "  ./run-ui-tests.sh dashboard"
    echo "  ./run-ui-tests.sh full"
}

# 生成测试报告
generate_report() {
    echo -e "${BLUE}📊 生成测试报告...${NC}"
    
    if [ -d "test-results" ]; then
        echo -e "${GREEN}✅ 测试结果目录存在${NC}"
        
        # 检查是否有HTML报告
        if [ -f "playwright-report/index.html" ]; then
            echo -e "${GREEN}📄 HTML报告已生成${NC}"
            echo -e "${BLUE}🌐 查看报告: npx playwright show-report${NC}"
        fi
        
        # 检查截图
        screenshot_count=$(find test-results -name "*.png" 2>/dev/null | wc -l)
        if [ $screenshot_count -gt 0 ]; then
            echo -e "${GREEN}📸 生成了 $screenshot_count 张截图${NC}"
        fi
        
        # 检查视频
        video_count=$(find test-results -name "*.webm" 2>/dev/null | wc -l)
        if [ $video_count -gt 0 ]; then
            echo -e "${GREEN}🎥 生成了 $video_count 个测试视频${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ 未找到测试结果目录${NC}"
    fi
}

# 主函数
main() {
    # 显示帮助信息
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_usage
        exit 0
    fi
    
    # 检查前置条件
    if ! check_app_running; then
        exit 1
    fi
    
    if ! check_playwright; then
        echo -e "${RED}❌ Playwright安装失败${NC}"
        exit 1
    fi
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 运行测试
    echo -e "${BLUE}🚀 开始运行测试...${NC}"
    
    if run_tests "$TEST_TYPE"; then
        echo -e "${GREEN}✅ 测试运行完成${NC}"
        test_result=0
    else
        echo -e "${RED}❌ 测试运行失败${NC}"
        test_result=1
    fi
    
    # 计算耗时
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo -e "${BLUE}⏱️ 测试耗时: ${duration}秒${NC}"
    
    # 生成报告
    generate_report
    
    # 显示结果总结
    echo ""
    echo "=========================="
    if [ $test_result -eq 0 ]; then
        echo -e "${GREEN}🎉 测试执行成功！${NC}"
    else
        echo -e "${RED}💥 测试执行失败！${NC}"
    fi
    echo -e "${BLUE}📁 测试结果: test-results/${NC}"
    echo -e "${BLUE}📄 详细报告: npx playwright show-report${NC}"
    echo "=========================="
    
    exit $test_result
}

# 运行主函数
main "$@"
