# ===========================================
# SFQuant API Gateway 环境配置
# ===========================================

# 基础配置
NODE_ENV=development
PORT=8080
HOST=0.0.0.0

# ===========================================
# 数据库配置
# ===========================================

# PostgreSQL 主数据库
DATABASE_URL=postgresql://sfquant:sfquant_password_2024@localhost:5433/sfquant

# Redis 缓存和消息队列
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# InfluxDB 时序数据库 (用于市场数据)
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=sfquant-influxdb-token-2024
INFLUXDB_ORG=sfquant
INFLUXDB_BUCKET=market_data

# ===========================================
# 安全配置
# ===========================================

# JWT 认证配置
JWT_SECRET=sfquant-super-secret-jwt-key-2024-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 数据加密配置
ENCRYPTION_KEY=sfquant-encryption-key-32-chars-2024
ENCRYPTION_ALGORITHM=aes-256-gcm

# API 安全配置
API_KEY_SECRET=sfquant-api-key-secret-2024

# ===========================================
# 网络和性能配置
# ===========================================

# CORS 配置
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:3003,http://localhost:5173

# 速率限制配置
RATE_LIMIT_MAX=1000
RATE_LIMIT_WINDOW=1 minute
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# WebSocket 配置
WS_HEARTBEAT_INTERVAL=15000
WS_HEARTBEAT_TIMEOUT=30000
WS_MAX_CONNECTIONS=1000
WS_COMPRESSION=true

# ===========================================
# 交易所 API 配置
# ===========================================

# 币安交易所配置
BINANCE_API_KEY=your-binance-api-key-here
BINANCE_SECRET_KEY=your-binance-secret-key-here
BINANCE_SANDBOX=true
BINANCE_TESTNET_URL=https://testnet.binance.vision

# OKX 交易所配置
OKX_API_KEY=your-okx-api-key-here
OKX_SECRET_KEY=your-okx-secret-key-here
OKX_PASSPHRASE=your-okx-passphrase-here
OKX_SANDBOX=true

# Bybit 交易所配置
BYBIT_API_KEY=your-bybit-api-key-here
BYBIT_SECRET_KEY=your-bybit-secret-key-here
BYBIT_SANDBOX=true

# Huobi 交易所配置
HUOBI_API_KEY=your-huobi-api-key-here
HUOBI_SECRET_KEY=your-huobi-secret-key-here
HUOBI_SANDBOX=true

# ===========================================
# 外部数据源配置
# ===========================================

# 市场数据 API
COINGECKO_API_KEY=your-coingecko-api-key-here
COINMARKETCAP_API_KEY=your-coinmarketcap-api-key-here
CRYPTOCOMPARE_API_KEY=your-cryptocompare-api-key-here

# 区块链 RPC 配置
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-infura-key
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your-infura-key
BSC_RPC_URL=https://bsc-dataseed.binance.org/
ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc

# ===========================================
# 性能和监控配置
# ===========================================

# 性能监控配置
ENABLE_METRICS=true
METRICS_INTERVAL=10000
METRICS_RETENTION_DAYS=30

# 缓存配置
CACHE_TTL=300
CACHE_MAX_SIZE=1000
CACHE_STRATEGY=lru

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_FILES=10
LOG_MAX_SIZE=10m

# ===========================================
# 通知和告警配置
# ===========================================

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
EMAIL_FROM=<EMAIL>

# Webhook 告警配置
ALERT_WEBHOOK_URL=https://your-webhook-url.com
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/webhook

# 电报机器人配置
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-telegram-chat-id

# ===========================================
# 文件和存储配置
# ===========================================

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads
ALLOWED_FILE_TYPES=.json,.csv,.txt,.log

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# ===========================================
# 安全和限制配置
# ===========================================

# 安全功能开关
ENABLE_HELMET=true
ENABLE_RATE_LIMIT=true
ENABLE_CORS=true
ENABLE_COMPRESSION=true

# 请求限制
MAX_REQUEST_SIZE=1mb
MAX_CONCURRENT_REQUESTS=100

# 会话配置
SESSION_SECRET=sfquant-session-secret-2024
SESSION_MAX_AGE=86400000

# ===========================================
# 开发和调试配置
# ===========================================

# 调试配置
DEBUG=sfquant:*
ENABLE_SWAGGER=true
ENABLE_PLAYGROUND=true

# 开发工具
HOT_RELOAD=true
WATCH_FILES=true
AUTO_RESTART=true

# 开发配置
ENABLE_SWAGGER=true
ENABLE_PLAYGROUND=false
