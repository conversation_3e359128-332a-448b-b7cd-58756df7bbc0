{"name": "@sfquant/api-gateway", "version": "1.0.0", "description": "SFQuant API Gateway - High-performance cryptocurrency quantitative trading system", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "tsx watch src/server.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "docker:build": "docker build -t sfquant-api-gateway .", "docker:run": "docker run -p 8080:8080 sfquant-api-gateway"}, "keywords": ["fastify", "typescript", "cryptocurrency", "quantitative-trading", "api-gateway", "websocket", "high-performance"], "author": "SFQuant Team", "license": "MIT", "dependencies": {"@fastify/cookie": "^9.2.0", "@fastify/cors": "^8.4.0", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^7.2.4", "@fastify/rate-limit": "^8.0.3", "@fastify/swagger": "^8.12.0", "@fastify/swagger-ui": "^2.1.0", "@fastify/websocket": "^8.3.1", "@prisma/client": "^5.6.0", "bcryptjs": "^2.4.3", "ccxt": "^4.1.0", "fastify": "^4.24.3", "fastify-plugin": "^4.5.1", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "prisma": "^5.6.0", "ws": "^8.14.2", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.7", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.8.10", "@types/ws": "^8.5.8", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "esbuild-register": "^3.6.0", "eslint": "^8.53.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "tsx": "^4.1.4", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}