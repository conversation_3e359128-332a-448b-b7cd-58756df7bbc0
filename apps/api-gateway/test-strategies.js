// 使用内置的 fetch API (Node.js 18+)

const API_BASE = 'http://localhost:8080';

console.log('🧪 Testing SFQuant Strategy Management...\n');

async function testStrategyManagement() {
  try {
    // 1. 测试获取所有策略
    console.log('📋 Testing: Get all strategies');
    const allStrategiesResponse = await fetch(`${API_BASE}/api/v1/strategy-manager`);
    const allStrategiesData = await allStrategiesResponse.json();
    console.log('✅ All strategies:', JSON.stringify(allStrategiesData, null, 2));
    console.log('');

    // 2. 测试创建套利策略
    console.log('💰 Testing: Create arbitrage strategy');
    const arbitrageConfig = {
      name: 'BTC/USDT Arbitrage Test',
      symbol: 'BTCUSDT',
      exchanges: ['binance', 'okx'],
      minProfitPercent: 0.5,
      maxPositionSize: 1000,
      checkInterval: 5000
    };

    const createArbitrageResponse = await fetch(`${API_BASE}/api/v1/strategy-manager/arbitrage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(arbitrageConfig)
    });

    const arbitrageData = await createArbitrageResponse.json();
    console.log('✅ Arbitrage strategy created:', JSON.stringify(arbitrageData, null, 2));
    const arbitrageId = arbitrageData.data?.id;
    console.log('');

    // 3. 测试创建网格交易策略
    console.log('📊 Testing: Create grid trading strategy');
    const gridConfig = {
      name: 'ETH/USDT Grid Test',
      symbol: 'ETHUSDT',
      exchange: 'binance',
      basePrice: 2000,
      gridSpacing: 1.0,
      gridLevels: 10,
      orderAmount: 0.1,
      upperLimit: 2200,
      lowerLimit: 1800
    };

    const createGridResponse = await fetch(`${API_BASE}/api/v1/strategy-manager/grid`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(gridConfig)
    });

    const gridData = await createGridResponse.json();
    console.log('✅ Grid strategy created:', JSON.stringify(gridData, null, 2));
    const gridId = gridData.data?.id;
    console.log('');

    // 4. 测试获取策略详情
    if (arbitrageId) {
      console.log('🔍 Testing: Get arbitrage strategy details');
      const arbitrageDetailsResponse = await fetch(`${API_BASE}/api/v1/strategy-manager/${arbitrageId}`);
      const arbitrageDetailsData = await arbitrageDetailsResponse.json();
      console.log('✅ Arbitrage strategy details:', JSON.stringify(arbitrageDetailsData, null, 2));
      console.log('');
    }

    if (gridId) {
      console.log('🔍 Testing: Get grid strategy details');
      const gridDetailsResponse = await fetch(`${API_BASE}/api/v1/strategy-manager/${gridId}`);
      const gridDetailsData = await gridDetailsResponse.json();
      console.log('✅ Grid strategy details:', JSON.stringify(gridDetailsData, null, 2));
      console.log('');
    }

    // 5. 测试启动策略
    if (arbitrageId) {
      console.log('🚀 Testing: Start arbitrage strategy');
      const startArbitrageResponse = await fetch(`${API_BASE}/api/v1/strategy-manager/${arbitrageId}/start`, {
        method: 'POST'
      });
      const startArbitrageData = await startArbitrageResponse.json();
      console.log('✅ Arbitrage strategy start result:', JSON.stringify(startArbitrageData, null, 2));
      console.log('');
    }

    if (gridId) {
      console.log('🚀 Testing: Start grid strategy');
      const startGridResponse = await fetch(`${API_BASE}/api/v1/strategy-manager/${gridId}/start`, {
        method: 'POST'
      });
      const startGridData = await startGridResponse.json();
      console.log('✅ Grid strategy start result:', JSON.stringify(startGridData, null, 2));
      console.log('');
    }

    // 6. 等待一段时间让策略运行
    console.log('⏳ Waiting 10 seconds for strategies to run...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    // 7. 测试获取运行中的策略
    console.log('🏃 Testing: Get running strategies');
    const runningStrategiesResponse = await fetch(`${API_BASE}/api/v1/strategy-manager/running`);
    const runningStrategiesData = await runningStrategiesResponse.json();
    console.log('✅ Running strategies:', JSON.stringify(runningStrategiesData, null, 2));
    console.log('');

    // 8. 测试停止策略
    if (arbitrageId) {
      console.log('⏹️ Testing: Stop arbitrage strategy');
      const stopArbitrageResponse = await fetch(`${API_BASE}/api/v1/strategy-manager/${arbitrageId}/stop`, {
        method: 'POST'
      });
      const stopArbitrageData = await stopArbitrageResponse.json();
      console.log('✅ Arbitrage strategy stop result:', JSON.stringify(stopArbitrageData, null, 2));
      console.log('');
    }

    if (gridId) {
      console.log('⏹️ Testing: Stop grid strategy');
      const stopGridResponse = await fetch(`${API_BASE}/api/v1/strategy-manager/${gridId}/stop`, {
        method: 'POST'
      });
      const stopGridData = await stopGridResponse.json();
      console.log('✅ Grid strategy stop result:', JSON.stringify(stopGridData, null, 2));
      console.log('');
    }

    // 9. 测试删除策略
    if (arbitrageId) {
      console.log('🗑️ Testing: Delete arbitrage strategy');
      const deleteArbitrageResponse = await fetch(`${API_BASE}/api/v1/strategy-manager/${arbitrageId}`, {
        method: 'DELETE'
      });
      const deleteArbitrageData = await deleteArbitrageResponse.json();
      console.log('✅ Arbitrage strategy delete result:', JSON.stringify(deleteArbitrageData, null, 2));
      console.log('');
    }

    if (gridId) {
      console.log('🗑️ Testing: Delete grid strategy');
      const deleteGridResponse = await fetch(`${API_BASE}/api/v1/strategy-manager/${gridId}`, {
        method: 'DELETE'
      });
      const deleteGridData = await deleteGridResponse.json();
      console.log('✅ Grid strategy delete result:', JSON.stringify(deleteGridData, null, 2));
      console.log('');
    }

    // 10. 最终检查所有策略
    console.log('📋 Testing: Final check - Get all strategies');
    const finalStrategiesResponse = await fetch(`${API_BASE}/api/v1/strategy-manager`);
    const finalStrategiesData = await finalStrategiesResponse.json();
    console.log('✅ Final strategies:', JSON.stringify(finalStrategiesData, null, 2));

    console.log('\n🎉 Strategy management testing completed successfully!');

  } catch (error) {
    console.error('❌ Error during strategy testing:', error);
  }
}

// 运行测试
testStrategyManagement();
