const WebSocket = require('ws')

// WebSocket 测试客户端
function testWebSocket() {
  console.log('🔌 连接到 WebSocket 服务器...')
  
  const ws = new WebSocket('ws://localhost:8080/ws')

  ws.on('open', function open() {
    console.log('✅ WebSocket 连接已建立')
    
    // 发送测试消息
    const testMessage = {
      type: 'test',
      message: 'Hello from WebSocket client!',
      timestamp: Date.now()
    }
    
    console.log('📤 发送测试消息:', testMessage)
    ws.send(JSON.stringify(testMessage))
  })

  ws.on('message', function message(data) {
    try {
      const parsed = JSON.parse(data.toString())
      console.log('📥 收到服务器消息:', parsed)
      
      if (parsed.type === 'welcome') {
        console.log(`🎉 欢迎消息: ${parsed.message}`)
        console.log(`🆔 客户端ID: ${parsed.clientId}`)
      } else if (parsed.type === 'price_update') {
        console.log(`💰 价格更新: ${parsed.symbol} = $${parsed.price}`)
      } else if (parsed.type === 'echo') {
        console.log('🔄 回显消息确认收到')
      }
    } catch (error) {
      console.error('❌ 解析消息失败:', error)
    }
  })

  ws.on('error', function error(err) {
    console.error('❌ WebSocket 错误:', err)
  })

  ws.on('close', function close() {
    console.log('🔌 WebSocket 连接已关闭')
  })

  // 10秒后关闭连接
  setTimeout(() => {
    console.log('⏰ 10秒测试完成，关闭连接...')
    ws.close()
  }, 10000)
}

// 运行测试
testWebSocket()
