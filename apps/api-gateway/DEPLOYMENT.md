# 🚀 SFQuant API Gateway 部署指南

## 📋 部署概述

本指南将帮助您在不同环境中部署 SFQuant API Gateway，包括开发环境、测试环境和生产环境。

## 🛠️ 环境要求

### 基础要求
- **Node.js**: >= 18.0.0
- **npm/pnpm**: 最新版本
- **PostgreSQL**: >= 13.0
- **Redis**: >= 6.0
- **操作系统**: Linux/macOS/Windows

### 推荐配置

#### 开发环境
```
CPU: 2 cores
Memory: 4GB
Storage: 20GB SSD
```

#### 生产环境
```
CPU: 4+ cores
Memory: 8+ GB
Storage: 100+ GB SSD
Network: 1Gbps+
```

## 🔧 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-org/sfquant.git
cd sfquant/apps/api-gateway
```

### 2. 安装依赖
```bash
# 使用 pnpm (推荐)
pnpm install

# 或使用 npm
npm install
```

### 3. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 4. 数据库设置
```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev
```

### 5. 启动服务
```bash
# 开发模式
pnpm dev

# 生产模式
pnpm build
pnpm start
```

## 🐳 Docker 部署

### 1. 创建 Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制 package 文件
COPY package*.json ./
COPY pnpm-lock.yaml ./

# 安装依赖
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm build

# 暴露端口
EXPOSE 8080

# 启动应用
CMD ["pnpm", "start"]
```

### 2. 创建 docker-compose.yml
```yaml
version: '3.8'

services:
  api-gateway:
    build: .
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/sfquant
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=sfquant
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 3. 启动容器
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f api-gateway

# 停止服务
docker-compose down
```

## ☸️ Kubernetes 部署

### 1. 创建 ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-gateway-config
data:
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  PORT: "8080"
```

### 2. 创建 Secret
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: api-gateway-secrets
type: Opaque
stringData:
  DATABASE_URL: "************************************/sfquant"
  REDIS_URL: "redis://redis:6379"
  JWT_SECRET: "your-jwt-secret"
```

### 3. 创建 Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: sfquant/api-gateway:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: api-gateway-config
        - secretRef:
            name: api-gateway-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 4. 创建 Service
```yaml
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-service
spec:
  selector:
    app: api-gateway
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

## 🌐 Nginx 反向代理

### 配置文件 (/etc/nginx/sites-available/sfquant)
```nginx
upstream api_gateway {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}

server {
    listen 80;
    server_name api.sfquant.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.sfquant.com;
    
    # SSL 配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 代理配置
    location / {
        proxy_pass http://api_gateway;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # WebSocket 支持
    location /ws {
        proxy_pass http://api_gateway;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 📊 监控配置

### 1. Prometheus 配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 5s
```

### 2. Grafana 仪表板
```json
{
  "dashboard": {
    "title": "SFQuant API Gateway",
    "panels": [
      {
        "title": "QPS",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[1m])"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))"
          }
        ]
      }
    ]
  }
}
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
# 只允许必要端口
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw enable
```

### 2. SSL/TLS 配置
```bash
# 使用 Let's Encrypt
certbot --nginx -d api.sfquant.com
```

### 3. 环境变量安全
```bash
# 使用 systemd 环境文件
sudo vim /etc/systemd/system/api-gateway.service

[Unit]
Description=SFQuant API Gateway
After=network.target

[Service]
Type=simple
User=sfquant
WorkingDirectory=/opt/sfquant/api-gateway
EnvironmentFile=/etc/sfquant/api-gateway.env
ExecStart=/usr/bin/node dist/server.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 📈 性能优化

### 1. PM2 集群模式
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'api-gateway',
    script: 'dist/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 8080
    },
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
```

### 2. 启动 PM2
```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js

# 保存配置
pm2 save

# 设置开机启动
pm2 startup
```

## 🧪 部署验证

### 1. 健康检查
```bash
curl http://localhost:8080/health
```

### 2. 性能测试
```bash
# 使用 ab 工具
ab -n 1000 -c 10 http://localhost:8080/health

# 使用 wrk 工具
wrk -t12 -c400 -d30s http://localhost:8080/health
```

### 3. 功能测试
```bash
# 测试 API 端点
curl http://localhost:8080/demo/metrics
curl http://localhost:8080/demo/market-data/BTCUSDT

# 测试 WebSocket
node test-websocket.js
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
```bash
lsof -i :8080
kill -9 <PID>
```

2. **数据库连接失败**
```bash
# 检查数据库状态
systemctl status postgresql
# 检查连接
psql -h localhost -U postgres -d sfquant
```

3. **Redis 连接失败**
```bash
# 检查 Redis 状态
systemctl status redis
# 测试连接
redis-cli ping
```

4. **内存不足**
```bash
# 检查内存使用
free -h
# 检查进程内存
ps aux --sort=-%mem | head
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看日志文件
2. 检查环境配置
3. 参考故障排除指南
4. 联系技术支持团队

---

**🎯 祝您部署顺利！**
