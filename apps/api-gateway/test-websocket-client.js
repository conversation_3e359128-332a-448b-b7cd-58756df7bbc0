const WebSocket = require('ws');

console.log('🔌 Testing SFQuant WebSocket connections...\n');

// 测试主 WebSocket 连接
function testMainWebSocket() {
  console.log('📡 Testing main WebSocket connection...');
  const ws = new WebSocket('ws://localhost:8080/ws');

  ws.on('open', function open() {
    console.log('✅ Main WebSocket connected');
    
    // 发送订阅消息
    ws.send(JSON.stringify({
      type: 'subscribe',
      channel: 'market-data'
    }));
    
    // 发送 ping
    setTimeout(() => {
      ws.send(JSON.stringify({
        type: 'ping'
      }));
    }, 1000);
  });

  ws.on('message', function message(data) {
    const msg = JSON.parse(data.toString());
    console.log('📨 Main WebSocket received:', msg);
  });

  ws.on('error', function error(err) {
    console.error('❌ Main WebSocket error:', err.message);
  });

  ws.on('close', function close() {
    console.log('❌ Main WebSocket disconnected');
  });

  // 5秒后关闭连接
  setTimeout(() => {
    ws.close();
  }, 5000);
}

// 测试市场数据 WebSocket 连接
function testMarketDataWebSocket() {
  console.log('📊 Testing market data WebSocket connection...');
  const ws = new WebSocket('ws://localhost:8080/ws/market-data');

  ws.on('open', function open() {
    console.log('✅ Market data WebSocket connected');
  });

  ws.on('message', function message(data) {
    const msg = JSON.parse(data.toString());
    console.log('📨 Market data WebSocket received:', msg);
  });

  ws.on('error', function error(err) {
    console.error('❌ Market data WebSocket error:', err.message);
  });

  ws.on('close', function close() {
    console.log('❌ Market data WebSocket disconnected');
  });

  // 5秒后关闭连接
  setTimeout(() => {
    ws.close();
  }, 5000);
}

// 测试交易 WebSocket 连接
function testTradingWebSocket() {
  console.log('🎯 Testing trading WebSocket connection...');
  const ws = new WebSocket('ws://localhost:8080/ws/trading');

  ws.on('open', function open() {
    console.log('✅ Trading WebSocket connected');
  });

  ws.on('message', function message(data) {
    const msg = JSON.parse(data.toString());
    console.log('📨 Trading WebSocket received:', msg);
  });

  ws.on('error', function error(err) {
    console.error('❌ Trading WebSocket error:', err.message);
  });

  ws.on('close', function close() {
    console.log('❌ Trading WebSocket disconnected');
  });

  // 5秒后关闭连接
  setTimeout(() => {
    ws.close();
  }, 5000);
}

// 测试系统 WebSocket 连接
function testSystemWebSocket() {
  console.log('🖥️ Testing system WebSocket connection...');
  const ws = new WebSocket('ws://localhost:8080/ws/system');

  ws.on('open', function open() {
    console.log('✅ System WebSocket connected');
  });

  ws.on('message', function message(data) {
    const msg = JSON.parse(data.toString());
    console.log('📨 System WebSocket received:', msg);
  });

  ws.on('error', function error(err) {
    console.error('❌ System WebSocket error:', err.message);
  });

  ws.on('close', function close() {
    console.log('❌ System WebSocket disconnected');
  });

  // 5秒后关闭连接
  setTimeout(() => {
    ws.close();
  }, 5000);
}

// 依次测试所有 WebSocket 连接
testMainWebSocket();

setTimeout(() => {
  testMarketDataWebSocket();
}, 1000);

setTimeout(() => {
  testTradingWebSocket();
}, 2000);

setTimeout(() => {
  testSystemWebSocket();
}, 3000);

// 6秒后检查 WebSocket 统计
setTimeout(async () => {
  console.log('\n📊 Checking WebSocket statistics...');
  try {
    const response = await fetch('http://localhost:8080/api/v1/websocket/stats');
    const data = await response.json();
    console.log('📈 WebSocket stats:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('❌ Failed to fetch WebSocket stats:', error.message);
  }
}, 6000);

// 7秒后退出
setTimeout(() => {
  console.log('\n🎉 WebSocket testing completed!');
  process.exit(0);
}, 7000);
