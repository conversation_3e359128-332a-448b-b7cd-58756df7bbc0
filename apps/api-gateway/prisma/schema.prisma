// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String   @id @default(cuid())
  email        String   @unique
  username     String   @unique
  passwordHash String
  role         String   @default("user")
  firstName    String?
  lastName     String?
  timezone     String?
  language     String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  lastLoginAt  DateTime?

  // Relations
  strategies   Strategy[]
  apiKeys      ApiKey[]

  @@map("users")
}

model Strategy {
  id          String   @id @default(cuid())
  name        String
  description String?
  type        String   // 'arbitrage', 'trend', 'grid', 'dca', 'ai', 'custom'
  status      String   @default("inactive") // 'active', 'inactive', 'paused', 'error'
  config      Json
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastExecuted DateTime?

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  trades       Trade[]
  performances Performance[]

  @@map("strategies")
}

model Trade {
  id              String   @id @default(cuid())
  strategyId      String
  symbol          String
  side            String   // 'buy', 'sell'
  amount          Float
  price           Float
  fee             Float    @default(0)
  exchangeOrderId String?
  timestamp       DateTime @default(now())

  // Relations
  strategy Strategy @relation(fields: [strategyId], references: [id], onDelete: Cascade)

  @@map("trades")
}

model Performance {
  id           String   @id @default(cuid())
  strategyId   String
  totalReturn  Float
  sharpeRatio  Float?
  maxDrawdown  Float?
  winRate      Float?
  totalTrades  Int
  period       String   // 'daily', 'weekly', 'monthly'
  timestamp    DateTime @default(now())

  // Relations
  strategy Strategy @relation(fields: [strategyId], references: [id], onDelete: Cascade)

  @@map("performances")
}

model ApiKey {
  id        String   @id @default(cuid())
  userId    String
  name      String
  key       String   @unique
  active    Boolean  @default(true)
  lastUsed  DateTime?
  createdAt DateTime @default(now())
  expiresAt DateTime?

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model PriceAlert {
  id          String   @id @default(cuid())
  userId      String
  symbol      String
  condition   String   // 'above', 'below'
  targetPrice Float
  triggered   Boolean  @default(false)
  createdAt   DateTime @default(now())
  triggeredAt DateTime?

  @@map("price_alerts")
}

model SystemLog {
  id        String   @id @default(cuid())
  level     String   // 'info', 'warn', 'error', 'debug'
  message   String
  metadata  Json?
  timestamp DateTime @default(now())

  @@map("system_logs")
}
