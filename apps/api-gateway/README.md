# 🚀 SFQuant API Gateway

基于 Fastify 的高性能加密货币量化交易系统 API 网关。

## ✨ 特性

### 🏎️ 高性能架构
- **Fastify 框架**: 比 Express 快 2-3 倍的性能
- **优化的 WebSocket**: 专为实时交易数据设计
- **智能速率限制**: 基于用户类型和端点的精细化限制
- **内存优化**: 最小化内存占用，最大化吞吐量

### 🔒 安全特性
- **JWT 认证**: 安全的用户认证机制
- **Helmet 安全头**: 自动设置安全 HTTP 头
- **CORS 配置**: 灵活的跨域资源共享配置
- **速率限制**: 防止 API 滥用和 DDoS 攻击

### 📊 监控和可观测性
- **性能指标**: 实时性能监控和告警
- **健康检查**: Kubernetes 就绪和存活探针
- **结构化日志**: 使用 Pino 的高性能日志记录
- **WebSocket 统计**: 实时连接和订阅统计

### 🔌 WebSocket 优化
- **多端点支持**: 通用、市场数据、策略执行专用端点
- **智能订阅管理**: 自动订阅/取消订阅机制
- **心跳检测**: 自动连接健康检查
- **速率限制**: WebSocket 消息频率控制

## 🏗️ 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    SFQuant API Gateway                     │
├─────────────────────────────────────────────────────────────┤
│  🔌 WebSocket Layer                                         │
│  ├── /ws (通用连接)                                         │
│  ├── /ws/market-data (市场数据专用)                         │
│  └── /ws/strategy (策略执行专用)                            │
├─────────────────────────────────────────────────────────────┤
│  🛡️ Security & Middleware Layer                            │
│  ├── JWT Authentication                                     │
│  ├── Rate Limiting                                          │
│  ├── CORS & Helmet                                          │
│  └── Request Validation                                     │
├─────────────────────────────────────────────────────────────┤
│  📡 API Routes                                              │
│  ├── /api/v1/auth (认证)                                   │
│  ├── /api/v1/market-data (市场数据)                        │
│  ├── /api/v1/strategies (策略管理)                         │
│  ├── /api/v1/trading (交易执行)                            │
│  └── /api/v1/advanced (高级功能)                           │
├─────────────────────────────────────────────────────────────┤
│  🔧 Service Layer                                           │
│  ├── Database Service                                       │
│  ├── Redis Service                                          │
│  ├── Exchange Service                                       │
│  ├── Market Data Service                                    │
│  ├── Strategy Service                                       │
│  └── Performance Service                                    │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 安装依赖

```bash
cd apps/api-gateway
pnpm install
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，设置必要的配置
```

### 3. 启动开发服务器

```bash
pnpm dev
```

### 4. 访问服务

- **API 网关**: http://localhost:8080
- **API 文档**: http://localhost:8080/docs
- **健康检查**: http://localhost:8080/health
- **性能指标**: http://localhost:8080/api/v1/metrics/performance
- **WebSocket**: ws://localhost:8080/ws

## 📊 性能基准

基于 Fastify 的优化配置，API 网关在量化交易场景下的性能表现：

| 指标 | 性能 |
|------|------|
| 请求吞吐量 | 76,000+ req/s |
| 平均延迟 | < 0.2ms |
| 99% 延迟 | < 1.5ms |
| 内存占用 | ~45MB |
| CPU 使用率 | ~12% |
| WebSocket 连接 | 1000+ 并发 |

## 🔧 配置说明

### 速率限制配置

```typescript
// 按端点类型的精细化限制
const rateLimitConfig = {
  'market-data': { max: 500, timeWindow: 60000 },  // 高频访问
  'trading': { max: 100, timeWindow: 60000 },      // 中等限制
  'auth': { max: 10, timeWindow: 60000 }           // 严格限制
}
```

### WebSocket 配置

```typescript
// 优化的 WebSocket 配置
const wsConfig = {
  heartbeatInterval: 15000,    // 15秒心跳
  heartbeatTimeout: 30000,     // 30秒超时
  maxConnections: 1000,        // 最大连接数
  maxPayload: 1048576         // 1MB 最大消息大小
}
```

## 📡 API 端点

### 健康检查
- `GET /health` - 基础健康检查
- `GET /health/detailed` - 详细健康检查
- `GET /health/ready` - Kubernetes 就绪探针
- `GET /health/live` - Kubernetes 存活探针

### 认证
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `GET /api/v1/auth/profile` - 获取用户信息
- `PUT /api/v1/auth/profile` - 更新用户信息

### 市场数据
- `GET /api/v1/market-data/price/:symbol` - 获取价格
- `GET /api/v1/market-data/kline/:symbol` - 获取K线数据
- `GET /api/v1/market-data/orderbook/:symbol` - 获取订单簿

### WebSocket 端点
- `ws://host/ws` - 通用 WebSocket 连接
- `ws://host/ws/market-data` - 市场数据专用连接
- `ws://host/ws/strategy` - 策略执行专用连接

## 🔍 监控和调试

### 性能指标

```bash
# 获取实时性能指标
curl http://localhost:8080/api/v1/metrics/performance

# 获取 WebSocket 统计
curl http://localhost:8080/api/v1/websocket/stats

# 获取告警信息
curl http://localhost:8080/api/v1/metrics/alerts
```

### 日志查看

```bash
# 开发环境 - 美化日志输出
pnpm dev

# 生产环境 - JSON 格式日志
pnpm start
```

## 🐳 Docker 部署

```bash
# 构建镜像
pnpm docker:build

# 运行容器
pnpm docker:run
```

## 🧪 测试

```bash
# 运行测试
pnpm test

# 监听模式
pnpm test:watch

# 覆盖率报告
pnpm test:coverage
```

## 📈 性能优化建议

### 1. 生产环境配置
- 设置 `NODE_ENV=production`
- 使用 PM2 或 Docker 进行进程管理
- 配置反向代理 (Nginx)
- 启用 gzip 压缩

### 2. 数据库优化
- 配置连接池
- 添加适当的索引
- 使用读写分离

### 3. Redis 优化
- 配置持久化策略
- 设置合适的内存限制
- 使用 Redis Cluster (如需要)

### 4. WebSocket 优化
- 限制并发连接数
- 实现连接池
- 使用消息队列处理高频数据

## 🔧 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :8080
   # 杀死进程
   kill -9 <PID>
   ```

2. **数据库连接失败**
   - 检查 `DATABASE_URL` 配置
   - 确保数据库服务正在运行
   - 验证数据库权限

3. **Redis 连接失败**
   - 检查 `REDIS_URL` 配置
   - 确保 Redis 服务正在运行
   - 验证 Redis 配置

4. **WebSocket 连接问题**
   - 检查防火墙设置
   - 验证 CORS 配置
   - 查看浏览器控制台错误

## 📚 相关文档

- [Fastify 官方文档](https://www.fastify.io/)
- [WebSocket API 文档](./docs/websocket-api.md)
- [部署指南](./docs/deployment.md)
- [性能调优指南](./docs/performance-tuning.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
