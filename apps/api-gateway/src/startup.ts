import { FastifyInstance } from 'fastify'

export async function startupServices(fastify: FastifyInstance): Promise<void> {
  console.log('🚀 Starting SFQuant services...')

  try {
    // 1. 启动实时市场数据服务
    console.log('📊 Starting Real-Time Market Data Service...')
    await fastify.realTimeMarketDataService.start()
    console.log('✅ Real-Time Market Data Service started')

    // 2. 启动策略执行引擎
    console.log('🎯 Starting Strategy Execution Engine...')
    await fastify.strategyExecutionEngine.start()
    console.log('✅ Strategy Execution Engine started')

    // 3. 设置服务间的事件监听
    setupServiceEventListeners(fastify)

    console.log('🎉 All SFQuant services started successfully!')
    
    // 输出服务状态
    await printServiceStatus(fastify)

  } catch (error) {
    console.error('❌ Failed to start services:', error)
    throw error
  }
}

function setupServiceEventListeners(fastify: FastifyInstance): void {
  console.log('🔗 Setting up service event listeners...')

  // 监听实时数据更新
  fastify.realTimeMarketDataService.on('price_update', (priceData) => {
    // 广播价格更新到 WebSocket 客户端
    fastify.wsManager.broadcast('market-data', {
      type: 'price_update',
      data: priceData
    })
  })

  fastify.realTimeMarketDataService.on('orderbook_update', (orderBookData) => {
    // 广播订单簿更新到 WebSocket 客户端
    fastify.wsManager.broadcast('market-data', {
      type: 'orderbook_update',
      data: orderBookData
    })
  })

  fastify.realTimeMarketDataService.on('trade_update', (tradeData) => {
    // 广播交易更新到 WebSocket 客户端
    fastify.wsManager.broadcast('market-data', {
      type: 'trade_update',
      data: tradeData
    })
  })

  // 监听策略执行事件
  fastify.strategyExecutionEngine.on('execution_signal', (signal) => {
    // 广播执行信号到 WebSocket 客户端
    fastify.wsManager.broadcast('trading', {
      type: 'execution_signal',
      data: signal
    })
  })

  fastify.strategyExecutionEngine.on('execution_completed', (execution) => {
    // 广播执行完成到 WebSocket 客户端
    fastify.wsManager.broadcast('trading', {
      type: 'execution_completed',
      data: execution
    })
  })

  console.log('✅ Service event listeners set up')
}

async function printServiceStatus(fastify: FastifyInstance): Promise<void> {
  console.log('\n📋 Service Status Report:')
  console.log('=' .repeat(50))

  // 数据库状态
  try {
    await fastify.database.healthCheck()
    console.log('✅ Database: Connected')
  } catch (error) {
    console.log('❌ Database: Disconnected')
  }

  // Redis状态
  try {
    await fastify.redis.ping()
    console.log('✅ Redis: Connected')
  } catch (error) {
    console.log('❌ Redis: Disconnected')
  }

  // 交易所状态
  const exchangeStatus = fastify.exchangeService.getAllExchangeStatus()
  console.log('\n📈 Exchange Status:')
  for (const [exchange, status] of Object.entries(exchangeStatus)) {
    const statusIcon = status.status ? '✅' : '❌'
    const supportedText = status.supported ? 'Configured' : 'Not Configured'
    console.log(`  ${statusIcon} ${exchange}: ${supportedText}`)
  }

  // WebSocket状态
  const wsStats = fastify.wsManager.getStats()
  console.log('\n🔌 WebSocket Status:')
  console.log(`  📊 Total Clients: ${wsStats.totalClients}`)
  console.log(`  📺 Total Channels: ${wsStats.totalChannels}`)
  
  if (wsStats.channels.length > 0) {
    console.log('  📋 Active Channels:')
    wsStats.channels.forEach((channel: any) => {
      console.log(`    - ${channel.name}: ${channel.clientCount} clients`)
    })
  }

  // 策略执行引擎状态
  const executionStats = fastify.strategyExecutionEngine.getExecutionStats()
  console.log('\n🎯 Strategy Execution Engine:')
  console.log(`  🏃 Running: ${executionStats.isRunning ? 'Yes' : 'No'}`)
  console.log(`  📈 Active Strategies: ${executionStats.activeStrategies}`)
  console.log(`  📋 Queue Length: ${executionStats.queueLength}`)

  if (executionStats.strategies.length > 0) {
    console.log('  📊 Active Strategies:')
    executionStats.strategies.forEach((strategy: any) => {
      console.log(`    - ${strategy.name} (${strategy.type}): ${strategy.symbol} on ${strategy.exchange}`)
    })
  }

  console.log('=' .repeat(50))
  console.log('🎉 SFQuant API Gateway is ready for trading!')
  console.log('')
}

export async function shutdownServices(fastify: FastifyInstance): Promise<void> {
  console.log('🛑 Shutting down SFQuant services...')

  try {
    // 停止策略执行引擎
    console.log('⏹️ Stopping Strategy Execution Engine...')
    await fastify.strategyExecutionEngine.stop()
    console.log('✅ Strategy Execution Engine stopped')

    // 停止实时市场数据服务
    console.log('⏹️ Stopping Real-Time Market Data Service...')
    await fastify.realTimeMarketDataService.stop()
    console.log('✅ Real-Time Market Data Service stopped')

    // 清理交易所连接
    console.log('🧹 Cleaning up exchange connections...')
    await fastify.exchangeService.cleanup()
    console.log('✅ Exchange connections cleaned up')

    console.log('✅ All services shut down successfully')

  } catch (error) {
    console.error('❌ Error during shutdown:', error)
    throw error
  }
}

// 健康检查函数
export async function performHealthCheck(fastify: FastifyInstance): Promise<{
  status: 'healthy' | 'unhealthy'
  services: Record<string, boolean>
  timestamp: string
}> {
  const services: Record<string, boolean> = {}

  // 检查数据库
  try {
    await fastify.database.healthCheck()
    services.database = true
  } catch {
    services.database = false
  }

  // 检查Redis
  try {
    await fastify.redis.ping()
    services.redis = true
  } catch {
    services.redis = false
  }

  // 检查交易所
  const exchangeStatus = fastify.exchangeService.getAllExchangeStatus()
  const healthyExchanges = Object.values(exchangeStatus).filter(status => status.status).length
  services.exchanges = healthyExchanges > 0

  // 检查实时数据服务
  services.realTimeData = true // 假设服务正在运行

  // 检查策略执行引擎
  const executionStats = fastify.strategyExecutionEngine.getExecutionStats()
  services.strategyExecution = executionStats.isRunning

  // 检查WebSocket
  const wsStats = fastify.wsManager.getStats()
  services.websocket = wsStats.totalClients >= 0 // WebSocket服务可用

  // 计算整体状态
  const allHealthy = Object.values(services).every(status => status)
  const criticalServicesHealthy = services.database && services.redis

  return {
    status: criticalServicesHealthy ? 'healthy' : 'unhealthy',
    services,
    timestamp: new Date().toISOString()
  }
}

// 获取系统指标
export function getSystemMetrics(fastify: FastifyInstance): any {
  const wsStats = fastify.wsManager.getStats()
  const executionStats = fastify.strategyExecutionEngine.getExecutionStats()
  const exchangeStatus = fastify.exchangeService.getAllExchangeStatus()

  return {
    websocket: {
      totalClients: wsStats.totalClients,
      totalChannels: wsStats.totalChannels,
      channels: wsStats.channels
    },
    execution: {
      isRunning: executionStats.isRunning,
      activeStrategies: executionStats.activeStrategies,
      queueLength: executionStats.queueLength
    },
    exchanges: exchangeStatus,
    memory: {
      used: process.memoryUsage().heapUsed,
      total: process.memoryUsage().heapTotal,
      external: process.memoryUsage().external
    },
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  }
}
