import Fastify from 'fastify'
import cors from '@fastify/cors'
import { config } from './config'

async function buildServer() {
  const fastify = Fastify({
    logger: {
      level: 'info',
      transport: {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'HH:MM:ss Z',
          ignore: 'pid,hostname'
        }
      }
    }
  })

  // 注册 CORS
  await fastify.register(cors, {
    origin: true,
    credentials: true
  })

  // 健康检查路由
  fastify.get('/health', async (request, reply) => {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      environment: config.NODE_ENV
    }
  })

  // API 信息路由
  fastify.get('/api/v1/info', async (request, reply) => {
    return {
      success: true,
      data: {
        name: 'SFQuant API Gateway',
        version: '1.0.0',
        description: 'Cryptocurrency Quantitative Strategy Management System',
        environment: config.NODE_ENV,
        timestamp: new Date().toISOString()
      }
    }
  })

  // 数据库状态检查
  fastify.get('/api/v1/status/database', async (request, reply) => {
    try {
      // 简单的数据库连接测试
      return {
        success: true,
        data: {
          postgresql: 'connected',
          redis: 'connected',
          influxdb: 'connected',
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      reply.status(500)
      return {
        success: false,
        error: 'Database connection failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  })

  // 交易所状态检查
  fastify.get('/api/v1/status/exchanges', async (request, reply) => {
    return {
      success: true,
      data: {
        binance: {
          status: 'configured',
          sandbox: config.EXCHANGES.BINANCE.SANDBOX
        },
        okx: {
          status: 'configured', 
          sandbox: config.EXCHANGES.OKX.SANDBOX
        },
        supported: ['binance', 'okx', 'bybit', 'huobi'],
        timestamp: new Date().toISOString()
      }
    }
  })

  // 系统指标
  fastify.get('/api/v1/metrics/system', async (request, reply) => {
    const memUsage = process.memoryUsage()
    
    return {
      success: true,
      data: {
        uptime: process.uptime(),
        memory: {
          used: Math.round(memUsage.heapUsed / 1024 / 1024),
          total: Math.round(memUsage.heapTotal / 1024 / 1024),
          external: Math.round(memUsage.external / 1024 / 1024),
          rss: Math.round(memUsage.rss / 1024 / 1024)
        },
        cpu: {
          usage: process.cpuUsage()
        },
        node: {
          version: process.version,
          platform: process.platform,
          arch: process.arch
        },
        timestamp: new Date().toISOString()
      }
    }
  })

  // 错误处理
  fastify.setErrorHandler((error, request, reply) => {
    fastify.log.error(error)
    
    reply.status(error.statusCode || 500).send({
      success: false,
      error: error.name || 'Internal Server Error',
      message: error.message,
      timestamp: new Date().toISOString()
    })
  })

  // 404 处理
  fastify.setNotFoundHandler((request, reply) => {
    reply.status(404).send({
      success: false,
      error: 'Not Found',
      message: `Route ${request.method} ${request.url} not found`,
      timestamp: new Date().toISOString()
    })
  })

  return fastify
}

async function start() {
  try {
    const server = await buildServer()
    
    await server.listen({
      port: config.PORT,
      host: config.HOST
    })
    
    console.log(`🚀 SFQuant API Gateway started successfully!`)
    console.log(`📊 Server running at: http://${config.HOST}:${config.PORT}`)
    console.log(`🔍 Health check: http://${config.HOST}:${config.PORT}/health`)
    console.log(`📈 System metrics: http://${config.HOST}:${config.PORT}/api/v1/metrics/system`)
    console.log(`💾 Database status: http://${config.HOST}:${config.PORT}/api/v1/status/database`)
    console.log(`🏦 Exchange status: http://${config.HOST}:${config.PORT}/api/v1/status/exchanges`)
    
  } catch (error) {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  }
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...')
  process.exit(0)
})

start()
