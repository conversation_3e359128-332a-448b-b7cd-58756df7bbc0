import { EventEmitter } from 'events'
import { ArbitrageStrategy, ArbitrageConfig } from './ArbitrageStrategy'
import { GridTradingStrategy, GridConfig } from './GridTradingStrategy'
import { ExchangeService } from '../services/ExchangeService'
import { RealTimeMarketDataService } from '../services/RealTimeMarketDataService'
import { StrategyService } from '../services/StrategyService'
import { RedisService } from '../services/RedisService'

export type StrategyType = 'arbitrage' | 'grid' | 'trend' | 'dca' | 'ai'

export interface StrategyInstance {
  id: string
  name: string
  type: StrategyType
  strategy: ArbitrageStrategy | GridTradingStrategy | any
  config: any
  status: 'running' | 'stopped' | 'error'
  createdAt: Date
  lastUpdate: Date
  performance?: {
    totalReturn: number
    totalTrades: number
    winRate: number
  }
}

export class StrategyManager extends EventEmitter {
  private strategies: Map<string, StrategyInstance> = new Map()
  private monitoringInterval: NodeJS.Timeout | null = null

  constructor(
    private exchangeService: ExchangeService,
    private marketDataService: RealTimeMarketDataService,
    private strategyService: StrategyService,
    private redisService: RedisService
  ) {
    super()
    this.startMonitoring()
  }

  // 创建套利策略
  async createArbitrageStrategy(
    id: string,
    name: string,
    config: ArbitrageConfig
  ): Promise<StrategyInstance> {
    
    const strategy = new ArbitrageStrategy(
      config,
      this.exchangeService,
      this.marketDataService
    )

    // 设置事件监听
    this.setupStrategyEventListeners(id, strategy)

    const instance: StrategyInstance = {
      id,
      name,
      type: 'arbitrage',
      strategy,
      config,
      status: 'stopped',
      createdAt: new Date(),
      lastUpdate: new Date()
    }

    this.strategies.set(id, instance)
    
    console.log(`✅ Arbitrage strategy created: ${name} (${id})`)
    this.emit('strategy_created', instance)
    
    return instance
  }

  // 创建网格交易策略
  async createGridStrategy(
    id: string,
    name: string,
    config: GridConfig
  ): Promise<StrategyInstance> {
    
    const strategy = new GridTradingStrategy(
      config,
      this.exchangeService,
      this.marketDataService
    )

    // 设置事件监听
    this.setupStrategyEventListeners(id, strategy)

    const instance: StrategyInstance = {
      id,
      name,
      type: 'grid',
      strategy,
      config,
      status: 'stopped',
      createdAt: new Date(),
      lastUpdate: new Date()
    }

    this.strategies.set(id, instance)
    
    console.log(`✅ Grid trading strategy created: ${name} (${id})`)
    this.emit('strategy_created', instance)
    
    return instance
  }

  // 启动策略
  async startStrategy(id: string): Promise<boolean> {
    const instance = this.strategies.get(id)
    if (!instance) {
      console.error(`Strategy not found: ${id}`)
      return false
    }

    if (instance.status === 'running') {
      console.log(`Strategy ${id} is already running`)
      return true
    }

    try {
      await instance.strategy.start()
      instance.status = 'running'
      instance.lastUpdate = new Date()
      
      console.log(`🚀 Strategy started: ${instance.name} (${id})`)
      this.emit('strategy_started', instance)
      
      return true
    } catch (error) {
      console.error(`Failed to start strategy ${id}:`, error)
      instance.status = 'error'
      this.emit('strategy_error', { instance, error })
      return false
    }
  }

  // 停止策略
  async stopStrategy(id: string): Promise<boolean> {
    const instance = this.strategies.get(id)
    if (!instance) {
      console.error(`Strategy not found: ${id}`)
      return false
    }

    if (instance.status === 'stopped') {
      console.log(`Strategy ${id} is already stopped`)
      return true
    }

    try {
      await instance.strategy.stop()
      instance.status = 'stopped'
      instance.lastUpdate = new Date()
      
      console.log(`⏹️ Strategy stopped: ${instance.name} (${id})`)
      this.emit('strategy_stopped', instance)
      
      return true
    } catch (error) {
      console.error(`Failed to stop strategy ${id}:`, error)
      instance.status = 'error'
      this.emit('strategy_error', { instance, error })
      return false
    }
  }

  // 删除策略
  async removeStrategy(id: string): Promise<boolean> {
    const instance = this.strategies.get(id)
    if (!instance) {
      console.error(`Strategy not found: ${id}`)
      return false
    }

    // 如果策略正在运行，先停止它
    if (instance.status === 'running') {
      await this.stopStrategy(id)
    }

    this.strategies.delete(id)
    
    console.log(`🗑️ Strategy removed: ${instance.name} (${id})`)
    this.emit('strategy_removed', instance)
    
    return true
  }

  // 获取策略实例
  getStrategy(id: string): StrategyInstance | undefined {
    return this.strategies.get(id)
  }

  // 获取所有策略
  getAllStrategies(): StrategyInstance[] {
    return Array.from(this.strategies.values())
  }

  // 获取运行中的策略
  getRunningStrategies(): StrategyInstance[] {
    return Array.from(this.strategies.values()).filter(
      instance => instance.status === 'running'
    )
  }

  // 获取策略统计
  getStrategyStats(id: string): any {
    const instance = this.strategies.get(id)
    if (!instance) {
      return null
    }

    const baseStats = {
      id: instance.id,
      name: instance.name,
      type: instance.type,
      status: instance.status,
      createdAt: instance.createdAt,
      lastUpdate: instance.lastUpdate,
      performance: instance.performance
    }

    // 获取策略特定的统计信息
    if (instance.strategy.getStats) {
      return {
        ...baseStats,
        strategySpecific: instance.strategy.getStats()
      }
    }

    return baseStats
  }

  // 获取所有策略的统计信息
  getAllStats(): any {
    const strategies = Array.from(this.strategies.values())
    
    return {
      total: strategies.length,
      running: strategies.filter(s => s.status === 'running').length,
      stopped: strategies.filter(s => s.status === 'stopped').length,
      error: strategies.filter(s => s.status === 'error').length,
      byType: {
        arbitrage: strategies.filter(s => s.type === 'arbitrage').length,
        grid: strategies.filter(s => s.type === 'grid').length,
        trend: strategies.filter(s => s.type === 'trend').length,
        dca: strategies.filter(s => s.type === 'dca').length,
        ai: strategies.filter(s => s.type === 'ai').length
      },
      strategies: strategies.map(s => this.getStrategyStats(s.id))
    }
  }

  // 设置策略事件监听
  private setupStrategyEventListeners(id: string, strategy: any): void {
    // 套利策略事件
    if (strategy instanceof ArbitrageStrategy) {
      strategy.on('arbitrage_opportunity', (opportunity) => {
        this.emit('arbitrage_opportunity', { strategyId: id, opportunity })
      })
      
      strategy.on('arbitrage_executed', (execution) => {
        this.emit('arbitrage_executed', { strategyId: id, execution })
      })
      
      strategy.on('arbitrage_error', (error) => {
        this.emit('arbitrage_error', { strategyId: id, error })
      })
    }

    // 网格交易策略事件
    if (strategy instanceof GridTradingStrategy) {
      strategy.on('grid_order_filled', (data) => {
        this.emit('grid_order_filled', { strategyId: id, ...data })
      })
      
      strategy.on('counter_order_placed', (data) => {
        this.emit('counter_order_placed', { strategyId: id, ...data })
      })
      
      strategy.on('grid_rebalance_needed', (data) => {
        this.emit('grid_rebalance_needed', { strategyId: id, ...data })
      })
    }
  }

  // 启动监控
  private startMonitoring(): void {
    this.monitoringInterval = setInterval(() => {
      this.performHealthCheck()
    }, 30000) // 每30秒检查一次
  }

  // 执行健康检查
  private async performHealthCheck(): Promise<void> {
    for (const [id, instance] of this.strategies) {
      try {
        // 更新策略状态
        instance.lastUpdate = new Date()
        
        // 缓存策略统计信息
        const stats = this.getStrategyStats(id)
        await this.redisService.set(`strategy_stats:${id}`, stats, 300) // 5分钟缓存
        
      } catch (error) {
        console.error(`Health check failed for strategy ${id}:`, error)
        instance.status = 'error'
        this.emit('strategy_error', { instance, error })
      }
    }
  }

  // 停止监控
  private stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }
  }

  // 清理资源
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up strategy manager...')
    
    this.stopMonitoring()
    
    // 停止所有运行中的策略
    const runningStrategies = this.getRunningStrategies()
    for (const instance of runningStrategies) {
      await this.stopStrategy(instance.id)
    }
    
    this.strategies.clear()
    
    console.log('✅ Strategy manager cleaned up')
  }

  // 从数据库加载策略
  async loadStrategiesFromDatabase(userId?: string): Promise<void> {
    try {
      console.log('📥 Loading strategies from database...')
      
      const dbStrategies = userId 
        ? await this.strategyService.getUserStrategies(userId)
        : [] // 如果需要加载所有策略，可以实现相应方法
      
      for (const dbStrategy of dbStrategies) {
        if (dbStrategy.status === 'active') {
          await this.createStrategyFromDatabase(dbStrategy)
        }
      }
      
      console.log(`✅ Loaded ${dbStrategies.length} strategies from database`)
      
    } catch (error) {
      console.error('Failed to load strategies from database:', error)
    }
  }

  // 从数据库策略创建策略实例
  private async createStrategyFromDatabase(dbStrategy: any): Promise<void> {
    try {
      const config = dbStrategy.config
      
      switch (dbStrategy.type) {
        case 'arbitrage':
          await this.createArbitrageStrategy(dbStrategy.id, dbStrategy.name, config)
          break
          
        case 'grid':
          await this.createGridStrategy(dbStrategy.id, dbStrategy.name, config)
          break
          
        // 可以添加更多策略类型
        default:
          console.warn(`Unknown strategy type: ${dbStrategy.type}`)
      }
      
    } catch (error) {
      console.error(`Failed to create strategy from database: ${dbStrategy.id}`, error)
    }
  }
}
