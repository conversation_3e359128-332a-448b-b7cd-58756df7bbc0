import { EventEmitter } from 'events'
import { ExchangeService } from '../services/ExchangeService'
import { RealTimeMarketDataService, RealTimePrice } from '../services/RealTimeMarketDataService'

export interface GridLevel {
  price: number
  amount: number
  side: 'buy' | 'sell'
  orderId?: string
  filled: boolean
}

export interface GridConfig {
  symbol: string
  exchange: string
  basePrice: number
  gridSpacing: number // 网格间距百分比
  gridLevels: number // 网格层数
  orderAmount: number // 每个网格的订单金额
  upperLimit?: number // 上限价格
  lowerLimit?: number // 下限价格
}

export class GridTradingStrategy extends EventEmitter {
  private isRunning = false
  private gridLevels: GridLevel[] = []
  private currentPrice = 0
  private checkInterval: NodeJS.Timeout | null = null

  constructor(
    private config: GridConfig,
    private exchangeService: ExchangeService,
    private marketDataService: RealTimeMarketDataService
  ) {
    super()
    this.setupEventListeners()
    this.initializeGrid()
  }

  private setupEventListeners(): void {
    this.marketDataService.on('price_update', this.handlePriceUpdate.bind(this))
  }

  private initializeGrid(): void {
    console.log(`🔧 Initializing grid for ${this.config.symbol}`)
    
    const { basePrice, gridSpacing, gridLevels, orderAmount } = this.config
    this.gridLevels = []

    // 创建买入网格（基准价格以下）
    for (let i = 1; i <= Math.floor(gridLevels / 2); i++) {
      const price = basePrice * (1 - (gridSpacing / 100) * i)
      
      // 检查是否超出下限
      if (this.config.lowerLimit && price < this.config.lowerLimit) {
        break
      }
      
      this.gridLevels.push({
        price,
        amount: orderAmount,
        side: 'buy',
        filled: false
      })
    }

    // 创建卖出网格（基准价格以上）
    for (let i = 1; i <= Math.floor(gridLevels / 2); i++) {
      const price = basePrice * (1 + (gridSpacing / 100) * i)
      
      // 检查是否超出上限
      if (this.config.upperLimit && price > this.config.upperLimit) {
        break
      }
      
      this.gridLevels.push({
        price,
        amount: orderAmount,
        side: 'sell',
        filled: false
      })
    }

    // 按价格排序
    this.gridLevels.sort((a, b) => a.price - b.price)
    
    console.log(`✅ Grid initialized with ${this.gridLevels.length} levels`)
    console.log(`📊 Price range: ${this.gridLevels[0]?.price.toFixed(4)} - ${this.gridLevels[this.gridLevels.length - 1]?.price.toFixed(4)}`)
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      return
    }

    console.log(`🔄 Starting grid trading strategy for ${this.config.symbol}`)
    this.isRunning = true

    // 获取当前价格
    await this.updateCurrentPrice()

    // 下所有网格订单
    await this.placeGridOrders()

    // 启动监控
    this.checkInterval = setInterval(() => {
      this.checkAndUpdateGrid()
    }, 5000) // 每5秒检查一次

    console.log(`✅ Grid trading strategy started for ${this.config.symbol}`)
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    console.log(`⏹️ Stopping grid trading strategy for ${this.config.symbol}`)
    this.isRunning = false

    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }

    // 取消所有未成交的订单
    await this.cancelAllOrders()

    console.log(`✅ Grid trading strategy stopped for ${this.config.symbol}`)
  }

  private async updateCurrentPrice(): Promise<void> {
    try {
      const priceData = await this.marketDataService.getRealTimePrice(
        this.config.exchange,
        this.config.symbol
      )
      
      if (priceData) {
        this.currentPrice = priceData.price
      }
    } catch (error) {
      console.error('Error updating current price:', error)
    }
  }

  private handlePriceUpdate(priceData: RealTimePrice): void {
    if (priceData.symbol === this.config.symbol && 
        priceData.exchange === this.config.exchange) {
      this.currentPrice = priceData.price
      
      // 检查是否有订单被触发
      this.checkFilledOrders()
    }
  }

  private async placeGridOrders(): Promise<void> {
    console.log(`📋 Placing ${this.gridLevels.length} grid orders...`)
    
    for (const level of this.gridLevels) {
      if (!level.filled && !level.orderId) {
        try {
          const order = await this.exchangeService.createOrder(
            this.config.exchange,
            this.config.symbol,
            'limit',
            level.side,
            level.amount,
            level.price
          )
          
          if (order) {
            level.orderId = order.id
            console.log(`✅ Grid order placed: ${level.side} ${level.amount} at ${level.price} (${order.id})`)
          }
          
          // 避免请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 100))
          
        } catch (error) {
          console.error(`❌ Failed to place grid order at ${level.price}:`, error)
        }
      }
    }
    
    console.log(`📊 Grid orders placement completed`)
  }

  private async checkFilledOrders(): Promise<void> {
    for (const level of this.gridLevels) {
      if (level.orderId && !level.filled) {
        try {
          const order = await this.exchangeService.getOrderStatus(
            this.config.exchange,
            level.orderId,
            this.config.symbol
          )
          
          if (order && order.status === 'closed') {
            level.filled = true
            console.log(`✅ Grid order filled: ${level.side} ${level.amount} at ${level.price}`)
            
            this.emit('grid_order_filled', {
              level,
              order
            })
            
            // 在对面价格下新订单
            await this.placeCounterOrder(level)
          }
          
        } catch (error) {
          console.error(`Error checking order status for ${level.orderId}:`, error)
        }
      }
    }
  }

  private async placeCounterOrder(filledLevel: GridLevel): Promise<void> {
    try {
      const counterSide = filledLevel.side === 'buy' ? 'sell' : 'buy'
      const priceMultiplier = counterSide === 'sell' 
        ? (1 + this.config.gridSpacing / 100)
        : (1 - this.config.gridSpacing / 100)
      
      const counterPrice = filledLevel.price * priceMultiplier
      
      // 检查价格限制
      if (this.config.upperLimit && counterPrice > this.config.upperLimit) {
        console.log(`⚠️ Counter order price ${counterPrice} exceeds upper limit`)
        return
      }
      
      if (this.config.lowerLimit && counterPrice < this.config.lowerLimit) {
        console.log(`⚠️ Counter order price ${counterPrice} below lower limit`)
        return
      }
      
      const order = await this.exchangeService.createOrder(
        this.config.exchange,
        this.config.symbol,
        'limit',
        counterSide,
        filledLevel.amount,
        counterPrice
      )
      
      if (order) {
        // 添加新的网格层级
        const newLevel: GridLevel = {
          price: counterPrice,
          amount: filledLevel.amount,
          side: counterSide,
          orderId: order.id,
          filled: false
        }
        
        this.gridLevels.push(newLevel)
        this.gridLevels.sort((a, b) => a.price - b.price)
        
        console.log(`✅ Counter order placed: ${counterSide} ${filledLevel.amount} at ${counterPrice}`)
        
        this.emit('counter_order_placed', {
          originalLevel: filledLevel,
          newLevel,
          order
        })
      }
      
    } catch (error) {
      console.error('Error placing counter order:', error)
    }
  }

  private async checkAndUpdateGrid(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    try {
      await this.updateCurrentPrice()
      await this.checkFilledOrders()
      
      // 检查是否需要调整网格
      await this.rebalanceGrid()
      
    } catch (error) {
      console.error('Error in grid check:', error)
    }
  }

  private async rebalanceGrid(): Promise<void> {
    // 如果价格偏离基准价格太多，可以考虑重新平衡网格
    const priceDeviation = Math.abs(this.currentPrice - this.config.basePrice) / this.config.basePrice
    
    if (priceDeviation > 0.2) { // 如果价格偏离超过20%
      console.log(`⚠️ Price deviation ${(priceDeviation * 100).toFixed(2)}% detected, considering rebalance`)
      
      this.emit('grid_rebalance_needed', {
        currentPrice: this.currentPrice,
        basePrice: this.config.basePrice,
        deviation: priceDeviation
      })
    }
  }

  private async cancelAllOrders(): Promise<void> {
    console.log(`🗑️ Cancelling all grid orders...`)
    
    for (const level of this.gridLevels) {
      if (level.orderId && !level.filled) {
        try {
          await this.exchangeService.cancelOrder(
            this.config.exchange,
            level.orderId,
            this.config.symbol
          )
          console.log(`✅ Cancelled order: ${level.orderId}`)
        } catch (error) {
          console.error(`❌ Failed to cancel order ${level.orderId}:`, error)
        }
      }
    }
  }

  // 获取策略统计
  getStats(): any {
    const activeOrders = this.gridLevels.filter(level => level.orderId && !level.filled).length
    const filledOrders = this.gridLevels.filter(level => level.filled).length
    const totalOrders = this.gridLevels.length

    return {
      isRunning: this.isRunning,
      symbol: this.config.symbol,
      exchange: this.config.exchange,
      currentPrice: this.currentPrice,
      basePrice: this.config.basePrice,
      gridSpacing: this.config.gridSpacing,
      totalGridLevels: totalOrders,
      activeOrders,
      filledOrders,
      priceRange: {
        min: this.gridLevels[0]?.price || 0,
        max: this.gridLevels[this.gridLevels.length - 1]?.price || 0
      },
      gridLevels: this.gridLevels.map(level => ({
        price: level.price,
        side: level.side,
        amount: level.amount,
        filled: level.filled,
        hasOrder: !!level.orderId
      }))
    }
  }

  // 更新网格配置
  async updateConfig(newConfig: Partial<GridConfig>): Promise<void> {
    console.log(`🔧 Updating grid configuration...`)
    
    // 停止当前网格
    const wasRunning = this.isRunning
    if (wasRunning) {
      await this.stop()
    }
    
    // 更新配置
    Object.assign(this.config, newConfig)
    
    // 重新初始化网格
    this.initializeGrid()
    
    // 如果之前在运行，重新启动
    if (wasRunning) {
      await this.start()
    }
    
    console.log(`✅ Grid configuration updated`)
  }
}
