import { EventEmitter } from 'events'
import { ExchangeService } from '../services/ExchangeService'
import { RealTimeMarketDataService, RealTimePrice } from '../services/RealTimeMarketDataService'

export interface ArbitrageOpportunity {
  symbol: string
  buyExchange: string
  sellExchange: string
  buyPrice: number
  sellPrice: number
  profitPercent: number
  volume: number
  timestamp: number
}

export interface ArbitrageConfig {
  symbol: string
  exchanges: string[]
  minProfitPercent: number
  maxPositionSize: number
  checkInterval: number
}

export class ArbitrageStrategy extends EventEmitter {
  private isRunning = false
  private checkInterval: NodeJS.Timeout | null = null
  private lastPrices: Map<string, RealTimePrice> = new Map()

  constructor(
    private config: ArbitrageConfig,
    private exchangeService: ExchangeService,
    private marketDataService: RealTimeMarketDataService
  ) {
    super()
    this.setupEventListeners()
  }

  private setupEventListeners(): void {
    this.marketDataService.on('price_update', this.handlePriceUpdate.bind(this))
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      return
    }

    console.log(`🔄 Starting arbitrage strategy for ${this.config.symbol}`)
    this.isRunning = true

    // 启动定期检查
    this.checkInterval = setInterval(() => {
      this.checkArbitrageOpportunities()
    }, this.config.checkInterval)

    console.log(`✅ Arbitrage strategy started for ${this.config.symbol}`)
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    console.log(`⏹️ Stopping arbitrage strategy for ${this.config.symbol}`)
    this.isRunning = false

    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }

    console.log(`✅ Arbitrage strategy stopped for ${this.config.symbol}`)
  }

  private handlePriceUpdate(priceData: RealTimePrice): void {
    if (priceData.symbol === this.config.symbol && 
        this.config.exchanges.includes(priceData.exchange)) {
      
      const key = `${priceData.exchange}:${priceData.symbol}`
      this.lastPrices.set(key, priceData)
      
      // 实时检查套利机会
      this.checkArbitrageOpportunities()
    }
  }

  private async checkArbitrageOpportunities(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    try {
      const opportunities = await this.findArbitrageOpportunities()
      
      for (const opportunity of opportunities) {
        if (opportunity.profitPercent >= this.config.minProfitPercent) {
          this.emit('arbitrage_opportunity', opportunity)
          console.log(`💰 Arbitrage opportunity found: ${opportunity.profitPercent.toFixed(2)}% profit`)
        }
      }
    } catch (error) {
      console.error('Error checking arbitrage opportunities:', error)
    }
  }

  private async findArbitrageOpportunities(): Promise<ArbitrageOpportunity[]> {
    const opportunities: ArbitrageOpportunity[] = []
    const prices: Map<string, RealTimePrice> = new Map()

    // 收集所有交易所的价格
    for (const exchange of this.config.exchanges) {
      const key = `${exchange}:${this.config.symbol}`
      const cachedPrice = this.lastPrices.get(key)
      
      if (cachedPrice) {
        prices.set(exchange, cachedPrice)
      } else {
        // 如果没有缓存价格，尝试获取实时价格
        const realTimePrice = await this.marketDataService.getRealTimePrice(exchange, this.config.symbol)
        if (realTimePrice) {
          prices.set(exchange, realTimePrice)
        }
      }
    }

    // 比较所有交易所对的价格
    const exchangeList = Array.from(prices.keys())
    
    for (let i = 0; i < exchangeList.length; i++) {
      for (let j = i + 1; j < exchangeList.length; j++) {
        const exchange1 = exchangeList[i]
        const exchange2 = exchangeList[j]
        
        const price1 = prices.get(exchange1)!
        const price2 = prices.get(exchange2)!
        
        // 检查从交易所1买入，交易所2卖出的机会
        const opportunity1 = this.calculateOpportunity(
          exchange1, exchange2, price1, price2
        )
        
        if (opportunity1) {
          opportunities.push(opportunity1)
        }
        
        // 检查从交易所2买入，交易所1卖出的机会
        const opportunity2 = this.calculateOpportunity(
          exchange2, exchange1, price2, price1
        )
        
        if (opportunity2) {
          opportunities.push(opportunity2)
        }
      }
    }

    return opportunities.sort((a, b) => b.profitPercent - a.profitPercent)
  }

  private calculateOpportunity(
    buyExchange: string,
    sellExchange: string,
    buyPrice: RealTimePrice,
    sellPrice: RealTimePrice
  ): ArbitrageOpportunity | null {
    
    // 使用买价和卖价计算更精确的套利机会
    const actualBuyPrice = buyPrice.ask // 我们需要支付的价格
    const actualSellPrice = sellPrice.bid // 我们能获得的价格
    
    if (actualSellPrice <= actualBuyPrice) {
      return null // 没有套利机会
    }
    
    const profitPercent = ((actualSellPrice - actualBuyPrice) / actualBuyPrice) * 100
    
    // 考虑交易费用（假设每边0.1%）
    const tradingFees = 0.2 // 0.1% * 2
    const netProfitPercent = profitPercent - tradingFees
    
    if (netProfitPercent <= 0) {
      return null
    }
    
    // 计算可交易的数量（取两个交易所的最小流动性）
    const maxVolume = Math.min(buyPrice.volume, sellPrice.volume)
    const positionSize = Math.min(maxVolume, this.config.maxPositionSize)
    
    return {
      symbol: this.config.symbol,
      buyExchange,
      sellExchange,
      buyPrice: actualBuyPrice,
      sellPrice: actualSellPrice,
      profitPercent: netProfitPercent,
      volume: positionSize,
      timestamp: Date.now()
    }
  }

  // 执行套利交易
  async executeArbitrage(opportunity: ArbitrageOpportunity): Promise<boolean> {
    try {
      console.log(`🎯 Executing arbitrage: Buy ${opportunity.volume} ${opportunity.symbol} on ${opportunity.buyExchange} at ${opportunity.buyPrice}, Sell on ${opportunity.sellExchange} at ${opportunity.sellPrice}`)
      
      // 同时下买单和卖单
      const [buyOrder, sellOrder] = await Promise.all([
        this.exchangeService.createOrder(
          opportunity.buyExchange,
          opportunity.symbol,
          'market',
          'buy',
          opportunity.volume
        ),
        this.exchangeService.createOrder(
          opportunity.sellExchange,
          opportunity.symbol,
          'market',
          'sell',
          opportunity.volume
        )
      ])
      
      if (buyOrder && sellOrder) {
        console.log(`✅ Arbitrage executed successfully`)
        console.log(`📈 Buy order: ${buyOrder.id} on ${opportunity.buyExchange}`)
        console.log(`📉 Sell order: ${sellOrder.id} on ${opportunity.sellExchange}`)
        
        this.emit('arbitrage_executed', {
          opportunity,
          buyOrder,
          sellOrder
        })
        
        return true
      } else {
        console.error(`❌ Failed to execute arbitrage: Missing orders`)
        return false
      }
      
    } catch (error) {
      console.error(`❌ Error executing arbitrage:`, error)
      this.emit('arbitrage_error', { opportunity, error })
      return false
    }
  }

  // 获取策略统计
  getStats(): any {
    return {
      isRunning: this.isRunning,
      symbol: this.config.symbol,
      exchanges: this.config.exchanges,
      minProfitPercent: this.config.minProfitPercent,
      maxPositionSize: this.config.maxPositionSize,
      lastPricesCount: this.lastPrices.size,
      lastPrices: Array.from(this.lastPrices.entries()).map(([key, price]) => ({
        key,
        price: price.price,
        timestamp: price.timestamp
      }))
    }
  }
}
