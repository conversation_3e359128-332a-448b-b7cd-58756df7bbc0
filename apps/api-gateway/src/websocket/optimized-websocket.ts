import { FastifyInstance } from 'fastify'
import { SocketStream } from '@fastify/websocket'

export interface WebSocketClient {
  id: string
  socket: SocketStream
  subscriptions: Set<string>
  lastPing: number
  isAlive: boolean
}

export interface WebSocketMessage {
  type: 'subscribe' | 'unsubscribe' | 'ping' | 'pong' | 'data'
  channel?: string
  data?: any
  timestamp?: number
}

export class OptimizedWebSocketManager {
  private clients: Map<string, WebSocketClient> = new Map()
  private channels: Map<string, Set<string>> = new Map()
  private heartbeatInterval: NodeJS.Timeout | null = null
  private fastify: FastifyInstance

  constructor(fastify: FastifyInstance) {
    this.fastify = fastify
    this.startHeartbeat()
  }

  async registerRoutes(): Promise<void> {
    // 主 WebSocket 路由
    this.fastify.register(async (fastify) => {
      fastify.get('/ws', { websocket: true }, this.handleConnection.bind(this))
      fastify.get('/ws/market-data', { websocket: true }, this.handleMarketDataConnection.bind(this))
      fastify.get('/ws/trading', { websocket: true }, this.handleTradingConnection.bind(this))
      fastify.get('/ws/system', { websocket: true }, this.handleSystemConnection.bind(this))
    })
  }

  private async handleConnection(connection: SocketStream): Promise<void> {
    const clientId = this.generateClientId()
    const client: WebSocketClient = {
      id: clientId,
      socket: connection,
      subscriptions: new Set(),
      lastPing: Date.now(),
      isAlive: true
    }

    this.clients.set(clientId, client)
    this.fastify.log.info(`WebSocket client connected: ${clientId}`)

    // 发送欢迎消息
    this.sendMessage(client, {
      type: 'data',
      data: {
        message: 'Connected to SFQuant WebSocket',
        clientId,
        timestamp: Date.now()
      }
    })

    // 处理消息
    connection.socket.on('message', (message: Buffer) => {
      this.handleMessage(client, message)
    })

    // 处理断开连接
    connection.socket.on('close', () => {
      this.handleDisconnection(clientId)
    })

    // 处理错误
    connection.socket.on('error', (error) => {
      this.fastify.log.error(`WebSocket error for client ${clientId}:`, error)
      this.handleDisconnection(clientId)
    })

    // 处理 pong 响应
    connection.socket.on('pong', () => {
      client.isAlive = true
      client.lastPing = Date.now()
    })
  }

  private async handleMarketDataConnection(connection: SocketStream): Promise<void> {
    const clientId = this.generateClientId()
    const client: WebSocketClient = {
      id: clientId,
      socket: connection,
      subscriptions: new Set(),
      lastPing: Date.now(),
      isAlive: true
    }

    this.clients.set(clientId, client)
    this.fastify.log.info(`Market data WebSocket client connected: ${clientId}`)

    // 自动订阅市场数据频道
    this.subscribe(client, 'market-data')

    connection.socket.on('message', (message: Buffer) => {
      this.handleMessage(client, message)
    })

    connection.socket.on('close', () => {
      this.handleDisconnection(clientId)
    })

    connection.socket.on('error', (error) => {
      this.fastify.log.error(`Market data WebSocket error for client ${clientId}:`, error)
      this.handleDisconnection(clientId)
    })
  }

  private async handleTradingConnection(connection: SocketStream): Promise<void> {
    const clientId = this.generateClientId()
    const client: WebSocketClient = {
      id: clientId,
      socket: connection,
      subscriptions: new Set(),
      lastPing: Date.now(),
      isAlive: true
    }

    this.clients.set(clientId, client)
    this.fastify.log.info(`Trading WebSocket client connected: ${clientId}`)

    // 自动订阅交易频道
    this.subscribe(client, 'trading')

    connection.socket.on('message', (message: Buffer) => {
      this.handleMessage(client, message)
    })

    connection.socket.on('close', () => {
      this.handleDisconnection(clientId)
    })

    connection.socket.on('error', (error) => {
      this.fastify.log.error(`Trading WebSocket error for client ${clientId}:`, error)
      this.handleDisconnection(clientId)
    })
  }

  private async handleSystemConnection(connection: SocketStream): Promise<void> {
    const clientId = this.generateClientId()
    const client: WebSocketClient = {
      id: clientId,
      socket: connection,
      subscriptions: new Set(),
      lastPing: Date.now(),
      isAlive: true
    }

    this.clients.set(clientId, client)
    this.fastify.log.info(`System WebSocket client connected: ${clientId}`)

    // 自动订阅系统频道
    this.subscribe(client, 'system')

    connection.socket.on('message', (message: Buffer) => {
      this.handleMessage(client, message)
    })

    connection.socket.on('close', () => {
      this.handleDisconnection(clientId)
    })

    connection.socket.on('error', (error) => {
      this.fastify.log.error(`System WebSocket error for client ${clientId}:`, error)
      this.handleDisconnection(clientId)
    })
  }

  private handleMessage(client: WebSocketClient, message: Buffer): void {
    try {
      const data: WebSocketMessage = JSON.parse(message.toString())
      
      switch (data.type) {
        case 'subscribe':
          if (data.channel) {
            this.subscribe(client, data.channel)
          }
          break
        
        case 'unsubscribe':
          if (data.channel) {
            this.unsubscribe(client, data.channel)
          }
          break
        
        case 'ping':
          this.sendMessage(client, { type: 'pong', timestamp: Date.now() })
          break
        
        default:
          this.fastify.log.warn(`Unknown message type: ${data.type}`)
      }
    } catch (error) {
      this.fastify.log.error(`Error parsing WebSocket message:`, error)
    }
  }

  private subscribe(client: WebSocketClient, channel: string): void {
    client.subscriptions.add(channel)
    
    if (!this.channels.has(channel)) {
      this.channels.set(channel, new Set())
    }
    
    this.channels.get(channel)!.add(client.id)
    
    this.sendMessage(client, {
      type: 'data',
      data: {
        message: `Subscribed to channel: ${channel}`,
        channel,
        timestamp: Date.now()
      }
    })
    
    this.fastify.log.info(`Client ${client.id} subscribed to channel: ${channel}`)
  }

  private unsubscribe(client: WebSocketClient, channel: string): void {
    client.subscriptions.delete(channel)
    
    const channelClients = this.channels.get(channel)
    if (channelClients) {
      channelClients.delete(client.id)
      if (channelClients.size === 0) {
        this.channels.delete(channel)
      }
    }
    
    this.sendMessage(client, {
      type: 'data',
      data: {
        message: `Unsubscribed from channel: ${channel}`,
        channel,
        timestamp: Date.now()
      }
    })
    
    this.fastify.log.info(`Client ${client.id} unsubscribed from channel: ${channel}`)
  }

  private handleDisconnection(clientId: string): void {
    const client = this.clients.get(clientId)
    if (!client) return

    // 从所有频道中移除客户端
    for (const channel of client.subscriptions) {
      const channelClients = this.channels.get(channel)
      if (channelClients) {
        channelClients.delete(clientId)
        if (channelClients.size === 0) {
          this.channels.delete(channel)
        }
      }
    }

    this.clients.delete(clientId)
    this.fastify.log.info(`WebSocket client disconnected: ${clientId}`)
  }

  private sendMessage(client: WebSocketClient, message: WebSocketMessage): void {
    try {
      if (client.socket.readyState === 1) { // WebSocket.OPEN
        client.socket.send(JSON.stringify(message))
      }
    } catch (error) {
      this.fastify.log.error(`Error sending message to client ${client.id}:`, error)
    }
  }

  // 广播消息到频道
  public broadcast(channel: string, data: any): void {
    const channelClients = this.channels.get(channel)
    if (!channelClients) return

    const message: WebSocketMessage = {
      type: 'data',
      channel,
      data,
      timestamp: Date.now()
    }

    for (const clientId of channelClients) {
      const client = this.clients.get(clientId)
      if (client) {
        this.sendMessage(client, message)
      }
    }
  }

  // 发送消息给特定客户端
  public sendToClient(clientId: string, data: any): void {
    const client = this.clients.get(clientId)
    if (client) {
      this.sendMessage(client, {
        type: 'data',
        data,
        timestamp: Date.now()
      })
    }
  }

  // 心跳检测
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      for (const [clientId, client] of this.clients) {
        if (!client.isAlive) {
          this.handleDisconnection(clientId)
          continue
        }

        client.isAlive = false
        try {
          client.socket.ping()
        } catch (error) {
          this.handleDisconnection(clientId)
        }
      }
    }, 30000) // 30秒心跳检测
  }

  // 获取统计信息
  public getStats(): any {
    return {
      totalClients: this.clients.size,
      totalChannels: this.channels.size,
      channels: Array.from(this.channels.entries()).map(([channel, clients]) => ({
        name: channel,
        clientCount: clients.size
      })),
      clients: Array.from(this.clients.values()).map(client => ({
        id: client.id,
        subscriptions: Array.from(client.subscriptions),
        lastPing: client.lastPing,
        isAlive: client.isAlive
      }))
    }
  }

  // 清理资源
  public async cleanup(): Promise<void> {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    // 关闭所有连接
    for (const client of this.clients.values()) {
      try {
        client.socket.close()
      } catch (error) {
        this.fastify.log.error(`Error closing client connection:`, error)
      }
    }

    this.clients.clear()
    this.channels.clear()
  }

  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}
