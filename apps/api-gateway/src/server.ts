import { build, setupErrorHandling, setupGracefulShutdown } from './app'
import { config } from './config'
import { startupServices, shutdownServices } from './startup'

async function start() {
  try {
    // 构建 Fastify 应用
    const fastify = await build({
      logger: {
        level: config.LOG_LEVEL,
        transport: config.NODE_ENV === 'development' ? {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'HH:MM:ss Z',
            ignore: 'pid,hostname'
          }
        } : undefined
      }
    })

    // 设置错误处理
    setupErrorHandling(fastify)

    // 设置优雅关闭
    setupGracefulShutdown(fastify)

    // 启动服务器
    await fastify.listen({
      port: config.PORT,
      host: config.HOST
    })

    fastify.log.info(`🚀 SFQuant API Gateway is running on http://${config.HOST}:${config.PORT}`)

    // 启动所有服务
    await startupServices(fastify)

    if (config.NODE_ENV === 'development') {
      fastify.log.info(`📚 API Documentation available at http://${config.HOST}:${config.PORT}/docs`)
      fastify.log.info(`📊 Performance Metrics available at http://${config.HOST}:${config.PORT}/api/v1/metrics/performance`)
      fastify.log.info(`🔌 WebSocket endpoint: ws://${config.HOST}:${config.PORT}/ws`)
      fastify.log.info(`📈 Real-time Data: http://${config.HOST}:${config.PORT}/api/v1/realtime`)
      fastify.log.info(`🎯 Strategy Execution: http://${config.HOST}:${config.PORT}/api/v1/execution`)
    }

    // 输出启动信息
    console.log(`
╔══════════════════════════════════════════════════════════════╗
║                     🚀 SFQuant API Gateway                   ║
║                                                              ║
║  🌐 Server: http://${config.HOST}:${config.PORT.toString().padEnd(43)} ║
║  📚 Docs:   http://${config.HOST}:${config.PORT}/docs${' '.repeat(35)} ║
║  🔌 WebSocket: ws://${config.HOST}:${config.PORT}/ws${' '.repeat(37)} ║
║  📊 Metrics: http://${config.HOST}:${config.PORT}/api/v1/metrics/performance${' '.repeat(11)} ║
║                                                              ║
║  Environment: ${config.NODE_ENV.toUpperCase().padEnd(47)} ║
║  Log Level:   ${config.LOG_LEVEL.toUpperCase().padEnd(47)} ║
║                                                              ║
║  🎯 Ready for quantitative trading operations!               ║
╚══════════════════════════════════════════════════════════════╝
    `)

  } catch (error) {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  }
}

// 启动应用
start()
