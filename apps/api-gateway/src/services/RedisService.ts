import Redis from 'ioredis'
import { config } from '../config'

export class RedisService {
  private redis: Redis | null = null
  private subscriber: Redis | null = null
  private publisher: Redis | null = null
  private isConnected = false

  constructor() {
    // 初始化时不立即连接，等待 initialize 调用
  }

  async initialize(): Promise<void> {
    try {
      // 主连接 - 用于一般操作
      this.redis = new Redis(config.REDIS_URL, {
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keepAlive: 30000,
        family: 4,
        keyPrefix: 'sfquant:',
      })

      // 订阅连接 - 用于 pub/sub
      this.subscriber = new Redis(config.REDIS_URL, {
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keyPrefix: 'sfquant:',
      })

      // 发布连接 - 用于 pub/sub
      this.publisher = new Redis(config.REDIS_URL, {
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keyPrefix: 'sfquant:',
      })

      // 连接到 Redis
      await Promise.all([
        this.redis.connect(),
        this.subscriber.connect(),
        this.publisher.connect()
      ])

      this.isConnected = true
      console.log('✅ Redis connected successfully')

      // 设置错误处理
      this.setupErrorHandlers()

    } catch (error) {
      console.error('❌ Redis connection failed:', error)
      throw error
    }
  }

  private setupErrorHandlers(): void {
    if (!this.redis || !this.subscriber || !this.publisher) return

    this.redis.on('error', (error) => {
      console.error('Redis main connection error:', error)
      this.isConnected = false
    })

    this.subscriber.on('error', (error) => {
      console.error('Redis subscriber error:', error)
    })

    this.publisher.on('error', (error) => {
      console.error('Redis publisher error:', error)
    })

    this.redis.on('connect', () => {
      console.log('Redis main connection established')
      this.isConnected = true
    })
  }

  async ping(): Promise<string> {
    if (!this.redis) {
      throw new Error('Redis not initialized')
    }
    return this.redis.ping()
  }

  async disconnect(): Promise<void> {
    if (this.redis) {
      await this.redis.quit()
    }
    if (this.subscriber) {
      await this.subscriber.quit()
    }
    if (this.publisher) {
      await this.publisher.quit()
    }
    this.isConnected = false
    console.log('🔌 Redis disconnected')
  }

  get connected(): boolean {
    return this.isConnected
  }

  // 基础操作
  async set(key: string, value: any, ttl?: number): Promise<void> {
    if (!this.redis) throw new Error('Redis not initialized')
    
    const serializedValue = typeof value === 'string' ? value : JSON.stringify(value)
    
    if (ttl) {
      await this.redis.setex(key, ttl, serializedValue)
    } else {
      await this.redis.set(key, serializedValue)
    }
  }

  async get<T = any>(key: string): Promise<T | null> {
    if (!this.redis) throw new Error('Redis not initialized')
    
    const value = await this.redis.get(key)
    if (!value) return null
    
    try {
      return JSON.parse(value)
    } catch {
      return value as T
    }
  }

  async del(key: string): Promise<number> {
    if (!this.redis) throw new Error('Redis not initialized')
    return this.redis.del(key)
  }

  async exists(key: string): Promise<boolean> {
    if (!this.redis) throw new Error('Redis not initialized')
    const result = await this.redis.exists(key)
    return result === 1
  }

  async expire(key: string, seconds: number): Promise<boolean> {
    if (!this.redis) throw new Error('Redis not initialized')
    const result = await this.redis.expire(key, seconds)
    return result === 1
  }

  async ttl(key: string): Promise<number> {
    if (!this.redis) throw new Error('Redis not initialized')
    return this.redis.ttl(key)
  }

  // 哈希操作
  async hset(key: string, field: string, value: any): Promise<void> {
    if (!this.redis) throw new Error('Redis not initialized')
    const serializedValue = typeof value === 'string' ? value : JSON.stringify(value)
    await this.redis.hset(key, field, serializedValue)
  }

  async hget<T = any>(key: string, field: string): Promise<T | null> {
    if (!this.redis) throw new Error('Redis not initialized')
    const value = await this.redis.hget(key, field)
    if (!value) return null
    
    try {
      return JSON.parse(value)
    } catch {
      return value as T
    }
  }

  async hgetall<T = any>(key: string): Promise<Record<string, T>> {
    if (!this.redis) throw new Error('Redis not initialized')
    const hash = await this.redis.hgetall(key)
    const result: Record<string, T> = {}
    
    for (const [field, value] of Object.entries(hash)) {
      try {
        result[field] = JSON.parse(value)
      } catch {
        result[field] = value as T
      }
    }
    
    return result
  }

  async hdel(key: string, field: string): Promise<number> {
    if (!this.redis) throw new Error('Redis not initialized')
    return this.redis.hdel(key, field)
  }

  // 列表操作
  async lpush(key: string, ...values: any[]): Promise<number> {
    if (!this.redis) throw new Error('Redis not initialized')
    const serializedValues = values.map(v => typeof v === 'string' ? v : JSON.stringify(v))
    return this.redis.lpush(key, ...serializedValues)
  }

  async rpush(key: string, ...values: any[]): Promise<number> {
    if (!this.redis) throw new Error('Redis not initialized')
    const serializedValues = values.map(v => typeof v === 'string' ? v : JSON.stringify(v))
    return this.redis.rpush(key, ...serializedValues)
  }

  async lpop<T = any>(key: string): Promise<T | null> {
    if (!this.redis) throw new Error('Redis not initialized')
    const value = await this.redis.lpop(key)
    if (!value) return null
    
    try {
      return JSON.parse(value)
    } catch {
      return value as T
    }
  }

  async lrange<T = any>(key: string, start: number, stop: number): Promise<T[]> {
    if (!this.redis) throw new Error('Redis not initialized')
    const values = await this.redis.lrange(key, start, stop)
    
    return values.map(value => {
      try {
        return JSON.parse(value)
      } catch {
        return value as T
      }
    })
  }

  // 集合操作
  async sadd(key: string, ...members: any[]): Promise<number> {
    if (!this.redis) throw new Error('Redis not initialized')
    const serializedMembers = members.map(m => typeof m === 'string' ? m : JSON.stringify(m))
    return this.redis.sadd(key, ...serializedMembers)
  }

  async smembers<T = any>(key: string): Promise<T[]> {
    if (!this.redis) throw new Error('Redis not initialized')
    const members = await this.redis.smembers(key)
    
    return members.map(member => {
      try {
        return JSON.parse(member)
      } catch {
        return member as T
      }
    })
  }

  async srem(key: string, ...members: any[]): Promise<number> {
    if (!this.redis) throw new Error('Redis not initialized')
    const serializedMembers = members.map(m => typeof m === 'string' ? m : JSON.stringify(m))
    return this.redis.srem(key, ...serializedMembers)
  }

  // 发布/订阅
  async publish(channel: string, message: any): Promise<number> {
    if (!this.publisher) throw new Error('Redis publisher not initialized')
    const serializedMessage = typeof message === 'string' ? message : JSON.stringify(message)
    return this.publisher.publish(channel, serializedMessage)
  }

  async subscribe(channel: string, callback: (message: any) => void): Promise<void> {
    if (!this.subscriber) throw new Error('Redis subscriber not initialized')
    
    await this.subscriber.subscribe(channel)
    this.subscriber.on('message', (receivedChannel, message) => {
      if (receivedChannel === `sfquant:${channel}`) {
        try {
          const parsedMessage = JSON.parse(message)
          callback(parsedMessage)
        } catch {
          callback(message)
        }
      }
    })
  }

  async unsubscribe(channel: string): Promise<void> {
    if (!this.subscriber) throw new Error('Redis subscriber not initialized')
    await this.subscriber.unsubscribe(channel)
  }

  // 缓存辅助方法
  async cache<T>(key: string, fetcher: () => Promise<T>, ttl = 300): Promise<T> {
    const cached = await this.get<T>(key)
    if (cached !== null) {
      return cached
    }
    
    const fresh = await fetcher()
    await this.set(key, fresh, ttl)
    return fresh
  }

  // 分布式锁
  async acquireLock(key: string, ttl = 10): Promise<boolean> {
    if (!this.redis) throw new Error('Redis not initialized')
    const result = await this.redis.set(`lock:${key}`, '1', 'EX', ttl, 'NX')
    return result === 'OK'
  }

  async releaseLock(key: string): Promise<void> {
    await this.del(`lock:${key}`)
  }
}
