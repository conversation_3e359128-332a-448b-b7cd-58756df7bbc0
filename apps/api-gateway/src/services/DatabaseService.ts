import { PrismaClient } from '@prisma/client'

export class DatabaseService {
  private prisma: PrismaClient | null = null
  private isConnected = false

  constructor() {
    // 初始化时不立即连接，等待 initialize 调用
  }

  async initialize(): Promise<void> {
    try {
      this.prisma = new PrismaClient({
        log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
        errorFormat: 'pretty',
      })

      // 测试连接
      await this.prisma.$connect()
      this.isConnected = true
      
      console.log('✅ Database connected successfully')
    } catch (error) {
      console.error('❌ Database connection failed:', error)
      throw error
    }
  }

  async healthCheck(): Promise<void> {
    if (!this.prisma) {
      throw new Error('Database not initialized')
    }

    try {
      await this.prisma.$queryRaw`SELECT 1`
    } catch (error) {
      this.isConnected = false
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.prisma) {
      await this.prisma.$disconnect()
      this.isConnected = false
      console.log('🔌 Database disconnected')
    }
  }

  get client(): PrismaClient {
    if (!this.prisma) {
      throw new Error('Database not initialized. Call initialize() first.')
    }
    return this.prisma
  }

  get connected(): boolean {
    return this.isConnected
  }

  // 用户相关操作
  async createUser(data: {
    email: string
    username: string
    passwordHash: string
    role?: string
  }) {
    return this.client.user.create({
      data: {
        email: data.email,
        username: data.username,
        passwordHash: data.passwordHash,
        role: data.role || 'user',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    })
  }

  async findUserByEmail(email: string) {
    return this.client.user.findUnique({
      where: { email }
    })
  }

  async findUserById(id: string) {
    return this.client.user.findUnique({
      where: { id }
    })
  }

  async updateUser(id: string, data: any) {
    return this.client.user.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    })
  }

  // 策略相关操作
  async createStrategy(data: {
    name: string
    description?: string
    type: string
    config: any
    userId: string
  }) {
    return this.client.strategy.create({
      data: {
        name: data.name,
        description: data.description,
        type: data.type,
        config: data.config,
        userId: data.userId,
        status: 'inactive',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    })
  }

  async findStrategiesByUserId(userId: string) {
    return this.client.strategy.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    })
  }

  async findStrategyById(id: string) {
    return this.client.strategy.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    })
  }

  async updateStrategy(id: string, data: any) {
    return this.client.strategy.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    })
  }

  async deleteStrategy(id: string) {
    return this.client.strategy.delete({
      where: { id }
    })
  }

  // 交易记录相关操作
  async createTrade(data: {
    strategyId: string
    symbol: string
    side: 'buy' | 'sell'
    amount: number
    price: number
    fee?: number
    exchangeOrderId?: string
  }) {
    return this.client.trade.create({
      data: {
        strategyId: data.strategyId,
        symbol: data.symbol,
        side: data.side,
        amount: data.amount,
        price: data.price,
        fee: data.fee || 0,
        exchangeOrderId: data.exchangeOrderId,
        timestamp: new Date()
      }
    })
  }

  async findTradesByStrategyId(strategyId: string, limit = 100) {
    return this.client.trade.findMany({
      where: { strategyId },
      orderBy: { timestamp: 'desc' },
      take: limit
    })
  }

  // 性能统计相关操作
  async createPerformanceRecord(data: {
    strategyId: string
    totalReturn: number
    sharpeRatio?: number
    maxDrawdown?: number
    winRate?: number
    totalTrades: number
    period: string
  }) {
    return this.client.performance.create({
      data: {
        strategyId: data.strategyId,
        totalReturn: data.totalReturn,
        sharpeRatio: data.sharpeRatio,
        maxDrawdown: data.maxDrawdown,
        winRate: data.winRate,
        totalTrades: data.totalTrades,
        period: data.period,
        timestamp: new Date()
      }
    })
  }

  async getLatestPerformance(strategyId: string) {
    return this.client.performance.findFirst({
      where: { strategyId },
      orderBy: { timestamp: 'desc' }
    })
  }

  // 事务支持
  async transaction<T>(fn: (prisma: PrismaClient) => Promise<T>): Promise<T> {
    return this.client.$transaction(fn)
  }

  // 原始查询支持
  async query(sql: string, params?: any[]): Promise<any> {
    return this.client.$queryRawUnsafe(sql, ...(params || []))
  }

  // 批量操作
  async batchCreate(model: string, data: any[]): Promise<any> {
    // @ts-ignore
    return this.client[model].createMany({
      data,
      skipDuplicates: true
    })
  }
}
