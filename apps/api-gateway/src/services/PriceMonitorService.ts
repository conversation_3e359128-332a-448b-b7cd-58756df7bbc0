import { RedisService } from './RedisService'

export interface PriceData {
  symbol: string
  price: number
  change24h: number
  changePercent24h: number
  volume24h: number
  high24h: number
  low24h: number
  timestamp: number
}

export interface PriceAnomaly {
  id: string
  symbol: string
  type: 'spike' | 'drop' | 'volume_surge' | 'unusual_pattern'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  currentPrice: number
  referencePrice: number
  changePercent: number
  timestamp: number
  detected: boolean
}

export interface MonitoringConfig {
  symbol: string
  enabled: boolean
  priceChangeThreshold: number // 百分比
  volumeChangeThreshold: number // 百分比
  checkInterval: number // 毫秒
  alertChannels: string[]
}

export class PriceMonitorService {
  private monitoringConfigs: Map<string, MonitoringConfig> = new Map()
  private priceHistory: Map<string, PriceData[]> = new Map()
  private anomalies: PriceAnomaly[] = []
  private isMonitoring = false
  private monitoringIntervals: Map<string, NodeJS.Timeout> = new Map()

  constructor(private redisService: RedisService) {
    this.setupDefaultConfigs()
  }

  private setupDefaultConfigs(): void {
    const defaultSymbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
    
    for (const symbol of defaultSymbols) {
      this.monitoringConfigs.set(symbol, {
        symbol,
        enabled: true,
        priceChangeThreshold: 5.0, // 5%价格变化
        volumeChangeThreshold: 50.0, // 50%成交量变化
        checkInterval: 30000, // 30秒检查一次
        alertChannels: ['redis', 'console']
      })
    }
  }

  // 开始监控
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      return
    }

    this.isMonitoring = true
    
    for (const [symbol, config] of this.monitoringConfigs) {
      if (config.enabled) {
        await this.startSymbolMonitoring(symbol, config)
      }
    }

    console.log('📊 Price monitoring started')
  }

  // 停止监控
  async stopMonitoring(): Promise<void> {
    this.isMonitoring = false
    
    for (const interval of this.monitoringIntervals.values()) {
      clearInterval(interval)
    }
    this.monitoringIntervals.clear()
    
    console.log('📊 Price monitoring stopped')
  }

  // 开始单个符号监控
  private async startSymbolMonitoring(symbol: string, config: MonitoringConfig): Promise<void> {
    const interval = setInterval(async () => {
      if (!this.isMonitoring) {
        clearInterval(interval)
        return
      }

      try {
        await this.checkSymbolForAnomalies(symbol, config)
      } catch (error) {
        console.error(`Error monitoring ${symbol}:`, error)
      }
    }, config.checkInterval)

    this.monitoringIntervals.set(symbol, interval)
    console.log(`📊 Started monitoring ${symbol}`)
  }

  // 检查符号异常
  private async checkSymbolForAnomalies(symbol: string, config: MonitoringConfig): Promise<void> {
    try {
      // 获取当前价格数据
      const currentPrice = await this.getCurrentPriceData(symbol)
      if (!currentPrice) {
        return
      }

      // 保存价格历史
      await this.savePriceHistory(symbol, currentPrice)

      // 获取历史数据进行比较
      const history = this.priceHistory.get(symbol) || []
      if (history.length < 2) {
        return // 需要至少2个数据点进行比较
      }

      // 检测价格异常
      await this.detectPriceAnomalies(symbol, currentPrice, history, config)

    } catch (error) {
      console.error(`Failed to check anomalies for ${symbol}:`, error)
    }
  }

  // 获取当前价格数据
  private async getCurrentPriceData(symbol: string): Promise<PriceData | null> {
    try {
      // 从缓存获取价格数据
      const cacheKey = `price_data:${symbol}`
      const cached = await this.redisService.get<PriceData>(cacheKey)
      
      if (cached && Date.now() - cached.timestamp < 60000) { // 1分钟内的数据
        return cached
      }

      // 这里应该从市场数据服务获取实际价格
      // 暂时返回模拟数据
      const mockPrice: PriceData = {
        symbol,
        price: Math.random() * 50000 + 30000, // 模拟BTC价格
        change24h: (Math.random() - 0.5) * 2000,
        changePercent24h: (Math.random() - 0.5) * 10,
        volume24h: Math.random() * 1000000,
        high24h: Math.random() * 52000 + 30000,
        low24h: Math.random() * 48000 + 30000,
        timestamp: Date.now()
      }

      // 缓存价格数据
      await this.redisService.set(cacheKey, mockPrice, 60)
      
      return mockPrice
    } catch (error) {
      console.error(`Failed to get current price for ${symbol}:`, error)
      return null
    }
  }

  // 保存价格历史
  private async savePriceHistory(symbol: string, priceData: PriceData): Promise<void> {
    let history = this.priceHistory.get(symbol) || []
    history.push(priceData)
    
    // 只保留最近100个数据点
    if (history.length > 100) {
      history = history.slice(-100)
    }
    
    this.priceHistory.set(symbol, history)

    // 也保存到Redis
    const cacheKey = `price_history:${symbol}`
    await this.redisService.lpush(cacheKey, priceData)
    
    // 保持Redis中最近100个数据点
    const listLength = await this.redisService.redis?.llen(cacheKey) || 0
    if (listLength > 100) {
      await this.redisService.redis?.ltrim(cacheKey, 0, 99)
    }
  }

  // 检测价格异常
  private async detectPriceAnomalies(
    symbol: string, 
    currentPrice: PriceData, 
    history: PriceData[], 
    config: MonitoringConfig
  ): Promise<void> {
    const previousPrice = history[history.length - 2]
    const priceChangePercent = ((currentPrice.price - previousPrice.price) / previousPrice.price) * 100

    // 检测价格突变
    if (Math.abs(priceChangePercent) >= config.priceChangeThreshold) {
      const anomaly: PriceAnomaly = {
        id: `anomaly_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        symbol,
        type: priceChangePercent > 0 ? 'spike' : 'drop',
        severity: this.calculateSeverity(Math.abs(priceChangePercent), config.priceChangeThreshold),
        description: `Price ${priceChangePercent > 0 ? 'spike' : 'drop'} of ${priceChangePercent.toFixed(2)}%`,
        currentPrice: currentPrice.price,
        referencePrice: previousPrice.price,
        changePercent: priceChangePercent,
        timestamp: Date.now(),
        detected: true
      }

      await this.recordAnomaly(anomaly)
    }

    // 检测成交量异常
    const volumeChangePercent = ((currentPrice.volume24h - previousPrice.volume24h) / previousPrice.volume24h) * 100
    if (Math.abs(volumeChangePercent) >= config.volumeChangeThreshold) {
      const anomaly: PriceAnomaly = {
        id: `anomaly_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        symbol,
        type: 'volume_surge',
        severity: this.calculateSeverity(Math.abs(volumeChangePercent), config.volumeChangeThreshold),
        description: `Volume surge of ${volumeChangePercent.toFixed(2)}%`,
        currentPrice: currentPrice.price,
        referencePrice: previousPrice.price,
        changePercent: volumeChangePercent,
        timestamp: Date.now(),
        detected: true
      }

      await this.recordAnomaly(anomaly)
    }

    // 检测异常模式（简单的移动平均偏离）
    if (history.length >= 10) {
      const recentPrices = history.slice(-10).map(p => p.price)
      const movingAverage = recentPrices.reduce((a, b) => a + b, 0) / recentPrices.length
      const deviation = ((currentPrice.price - movingAverage) / movingAverage) * 100

      if (Math.abs(deviation) >= config.priceChangeThreshold * 1.5) {
        const anomaly: PriceAnomaly = {
          id: `anomaly_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          symbol,
          type: 'unusual_pattern',
          severity: this.calculateSeverity(Math.abs(deviation), config.priceChangeThreshold * 1.5),
          description: `Unusual price pattern detected, ${deviation.toFixed(2)}% deviation from moving average`,
          currentPrice: currentPrice.price,
          referencePrice: movingAverage,
          changePercent: deviation,
          timestamp: Date.now(),
          detected: true
        }

        await this.recordAnomaly(anomaly)
      }
    }
  }

  // 计算异常严重程度
  private calculateSeverity(changePercent: number, threshold: number): 'low' | 'medium' | 'high' | 'critical' {
    if (changePercent >= threshold * 3) {
      return 'critical'
    } else if (changePercent >= threshold * 2) {
      return 'high'
    } else if (changePercent >= threshold * 1.5) {
      return 'medium'
    } else {
      return 'low'
    }
  }

  // 记录异常
  private async recordAnomaly(anomaly: PriceAnomaly): Promise<void> {
    this.anomalies.push(anomaly)
    
    // 只保留最近1000个异常
    if (this.anomalies.length > 1000) {
      this.anomalies = this.anomalies.slice(-1000)
    }

    // 保存到Redis
    await this.redisService.lpush('price_anomalies', anomaly)
    await this.redisService.expire('price_anomalies', 86400) // 24小时过期

    // 发布异常事件
    await this.redisService.publish('price_anomalies', {
      type: 'price_anomaly_detected',
      anomaly,
      timestamp: Date.now()
    })

    console.warn(`🚨 Price anomaly detected: ${anomaly.description} for ${anomaly.symbol}`)
  }

  // 添加监控配置
  addMonitoringConfig(config: MonitoringConfig): void {
    this.monitoringConfigs.set(config.symbol, config)
    
    if (this.isMonitoring && config.enabled) {
      this.startSymbolMonitoring(config.symbol, config)
    }
  }

  // 更新监控配置
  updateMonitoringConfig(symbol: string, updates: Partial<MonitoringConfig>): boolean {
    const existing = this.monitoringConfigs.get(symbol)
    if (!existing) {
      return false
    }

    const updated = { ...existing, ...updates }
    this.monitoringConfigs.set(symbol, updated)

    // 重启监控
    if (this.isMonitoring) {
      const interval = this.monitoringIntervals.get(symbol)
      if (interval) {
        clearInterval(interval)
      }
      
      if (updated.enabled) {
        this.startSymbolMonitoring(symbol, updated)
      }
    }

    return true
  }

  // 移除监控配置
  removeMonitoringConfig(symbol: string): boolean {
    const interval = this.monitoringIntervals.get(symbol)
    if (interval) {
      clearInterval(interval)
      this.monitoringIntervals.delete(symbol)
    }

    return this.monitoringConfigs.delete(symbol)
  }

  // 获取监控配置
  getMonitoringConfigs(): MonitoringConfig[] {
    return Array.from(this.monitoringConfigs.values())
  }

  // 获取最近的异常
  getRecentAnomalies(limit = 50): PriceAnomaly[] {
    return this.anomalies.slice(-limit)
  }

  // 获取特定符号的异常
  getSymbolAnomalies(symbol: string, limit = 20): PriceAnomaly[] {
    return this.anomalies
      .filter(a => a.symbol === symbol)
      .slice(-limit)
  }

  // 获取价格历史
  getPriceHistory(symbol: string, limit = 100): PriceData[] {
    const history = this.priceHistory.get(symbol) || []
    return history.slice(-limit)
  }

  // 获取监控统计
  getMonitoringStats(): any {
    const totalConfigs = this.monitoringConfigs.size
    const enabledConfigs = Array.from(this.monitoringConfigs.values()).filter(c => c.enabled).length
    const totalAnomalies = this.anomalies.length
    const recentAnomalies = this.anomalies.filter(a => Date.now() - a.timestamp < 3600000).length // 1小时内

    return {
      totalSymbols: totalConfigs,
      activeSymbols: enabledConfigs,
      totalAnomalies,
      recentAnomalies,
      isMonitoring: this.isMonitoring,
      lastUpdate: Date.now()
    }
  }

  // 清理旧数据
  async cleanup(): Promise<void> {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000) // 24小时前
    
    // 清理异常数据
    this.anomalies = this.anomalies.filter(a => a.timestamp > cutoff)
    
    // 清理价格历史
    for (const [symbol, history] of this.priceHistory) {
      const filtered = history.filter(p => p.timestamp > cutoff)
      this.priceHistory.set(symbol, filtered)
    }
    
    console.log('🧹 Price monitoring data cleaned up')
  }
}
