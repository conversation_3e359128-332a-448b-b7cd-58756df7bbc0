import ccxt from 'ccxt'
import { config } from '../config'

export interface ExchangeConfig {
  apiKey?: string
  secret?: string
  password?: string
  sandbox?: boolean
  enableRateLimit?: boolean
}

export interface MarketData {
  symbol: string
  price: number
  bid: number
  ask: number
  volume: number
  timestamp: number
}

export interface OrderBook {
  symbol: string
  bids: [number, number][]
  asks: [number, number][]
  timestamp: number
}

export interface Trade {
  id: string
  symbol: string
  side: 'buy' | 'sell'
  amount: number
  price: number
  fee?: number
  timestamp: number
}

export class ExchangeService {
  private exchanges: Map<string, ccxt.Exchange> = new Map()
  private supportedExchanges = ['binance', 'okx', 'bybit', 'huobi', 'coinbase', 'kraken']
  private exchangeStatus: Map<string, boolean> = new Map()
  private lastHealthCheck: Map<string, number> = new Map()

  constructor() {
    this.initializeExchanges()
    this.startHealthCheck()
  }

  private initializeExchanges(): void {
    console.log('🔄 Initializing exchanges...')

    // 初始化币安
    this.initializeBinance()

    // 初始化 OKX
    this.initializeOKX()

    // 初始化 Bybit
    this.initializeBybit()

    // 初始化火币
    this.initializeHuobi()

    console.log(`🔗 Initialized ${this.exchanges.size} exchanges`)
  }

  private initializeBinance(): void {
    if (config.EXCHANGES.BINANCE.API_KEY && config.EXCHANGES.BINANCE.SECRET_KEY) {
      try {
        const binance = new ccxt.binance({
          apiKey: config.EXCHANGES.BINANCE.API_KEY,
          secret: config.EXCHANGES.BINANCE.SECRET_KEY,
          sandbox: config.EXCHANGES.BINANCE.SANDBOX,
          enableRateLimit: true,
          rateLimit: 1200, // 每分钟1200请求
          options: {
            defaultType: 'spot',
            recvWindow: 10000,
          },
          urls: config.EXCHANGES.BINANCE.SANDBOX ? {
            api: {
              public: 'https://testnet.binance.vision/api',
              private: 'https://testnet.binance.vision/api',
            }
          } : undefined
        })
        this.exchanges.set('binance', binance)
        this.exchangeStatus.set('binance', true)
        console.log('✅ Binance exchange initialized')
      } catch (error) {
        console.error('❌ Failed to initialize Binance:', error)
        this.exchangeStatus.set('binance', false)
      }
    } else {
      console.log('⚠️ Binance API keys not configured')
    }
  }

  private initializeOKX(): void {
    if (config.EXCHANGES.OKX.API_KEY && config.EXCHANGES.OKX.SECRET_KEY) {
      try {
        const okx = new ccxt.okx({
          apiKey: config.EXCHANGES.OKX.API_KEY,
          secret: config.EXCHANGES.OKX.SECRET_KEY,
          password: config.EXCHANGES.OKX.PASSPHRASE,
          sandbox: config.EXCHANGES.OKX.SANDBOX,
          enableRateLimit: true,
          rateLimit: 100, // 每秒10请求
          options: {
            defaultType: 'spot',
          }
        })
        this.exchanges.set('okx', okx)
        this.exchangeStatus.set('okx', true)
        console.log('✅ OKX exchange initialized')
      } catch (error) {
        console.error('❌ Failed to initialize OKX:', error)
        this.exchangeStatus.set('okx', false)
      }
    } else {
      console.log('⚠️ OKX API keys not configured')
    }
  }

  private initializeBybit(): void {
    if (config.EXCHANGES.BYBIT?.API_KEY && config.EXCHANGES.BYBIT?.SECRET_KEY) {
      try {
        const bybit = new ccxt.bybit({
          apiKey: config.EXCHANGES.BYBIT.API_KEY,
          secret: config.EXCHANGES.BYBIT.SECRET_KEY,
          sandbox: config.EXCHANGES.BYBIT.SANDBOX,
          enableRateLimit: true,
          rateLimit: 120, // 每秒50请求
          options: {
            defaultType: 'spot',
          }
        })
        this.exchanges.set('bybit', bybit)
        this.exchangeStatus.set('bybit', true)
        console.log('✅ Bybit exchange initialized')
      } catch (error) {
        console.error('❌ Failed to initialize Bybit:', error)
        this.exchangeStatus.set('bybit', false)
      }
    } else {
      console.log('⚠️ Bybit API keys not configured')
    }
  }

  private initializeHuobi(): void {
    if (config.EXCHANGES.HUOBI?.API_KEY && config.EXCHANGES.HUOBI?.SECRET_KEY) {
      try {
        const huobi = new ccxt.huobi({
          apiKey: config.EXCHANGES.HUOBI.API_KEY,
          secret: config.EXCHANGES.HUOBI.SECRET_KEY,
          sandbox: config.EXCHANGES.HUOBI.SANDBOX,
          enableRateLimit: true,
          rateLimit: 100, // 每秒10请求
          options: {
            defaultType: 'spot',
          }
        })
        this.exchanges.set('huobi', huobi)
        this.exchangeStatus.set('huobi', true)
        console.log('✅ Huobi exchange initialized')
      } catch (error) {
        console.error('❌ Failed to initialize Huobi:', error)
        this.exchangeStatus.set('huobi', false)
      }
    } else {
      console.log('⚠️ Huobi API keys not configured')
    }
  }

  private startHealthCheck(): void {
    // 每5分钟检查一次交易所连接状态
    setInterval(() => {
      this.performHealthCheck()
    }, 5 * 60 * 1000)
  }

  private async performHealthCheck(): Promise<void> {
    console.log('🔍 Performing exchange health check...')

    for (const [exchangeId, exchange] of this.exchanges) {
      try {
        // 尝试获取服务器时间来测试连接
        await exchange.fetchTime()
        this.exchangeStatus.set(exchangeId, true)
        this.lastHealthCheck.set(exchangeId, Date.now())
      } catch (error) {
        console.error(`❌ Health check failed for ${exchangeId}:`, error)
        this.exchangeStatus.set(exchangeId, false)
      }
    }
  }

  getExchange(exchangeId: string): ccxt.Exchange | null {
    const exchange = this.exchanges.get(exchangeId)
    if (!exchange) {
      return null
    }

    // 检查交易所状态
    const isHealthy = this.exchangeStatus.get(exchangeId)
    if (!isHealthy) {
      console.warn(`⚠️ Exchange ${exchangeId} is not healthy`)
    }

    return exchange
  }

  getAvailableExchanges(): string[] {
    return Array.from(this.exchanges.keys())
  }

  getHealthyExchanges(): string[] {
    return Array.from(this.exchanges.keys()).filter(id =>
      this.exchangeStatus.get(id) === true
    )
  }

  getExchangeStatus(exchangeId: string): boolean {
    return this.exchangeStatus.get(exchangeId) || false
  }

  getAllExchangeStatus(): Record<string, {
    status: boolean;
    lastCheck: number | null;
    supported: boolean;
  }> {
    const result: Record<string, any> = {}

    for (const exchangeId of this.supportedExchanges) {
      result[exchangeId] = {
        status: this.exchangeStatus.get(exchangeId) || false,
        lastCheck: this.lastHealthCheck.get(exchangeId) || null,
        supported: this.exchanges.has(exchangeId)
      }
    }

    return result
  }

  async getMarketData(exchangeId: string, symbol: string): Promise<MarketData | null> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not available`)
    }

    try {
      const ticker = await exchange.fetchTicker(symbol)
      return {
        symbol,
        price: ticker.last || 0,
        bid: ticker.bid || 0,
        ask: ticker.ask || 0,
        volume: ticker.baseVolume || 0,
        timestamp: ticker.timestamp || Date.now()
      }
    } catch (error) {
      console.error(`Failed to fetch market data for ${symbol} on ${exchangeId}:`, error)
      return null
    }
  }

  async getOrderBook(exchangeId: string, symbol: string, limit = 20): Promise<OrderBook | null> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not available`)
    }

    try {
      const orderbook = await exchange.fetchOrderBook(symbol, limit)
      return {
        symbol,
        bids: orderbook.bids.slice(0, limit),
        asks: orderbook.asks.slice(0, limit),
        timestamp: orderbook.timestamp || Date.now()
      }
    } catch (error) {
      console.error(`Failed to fetch order book for ${symbol} on ${exchangeId}:`, error)
      return null
    }
  }

  async getKlineData(
    exchangeId: string,
    symbol: string,
    timeframe = '1h',
    limit = 100
  ): Promise<any[] | null> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not available`)
    }

    try {
      const ohlcv = await exchange.fetchOHLCV(symbol, timeframe, undefined, limit)
      return ohlcv.map(candle => ({
        timestamp: candle[0],
        open: candle[1],
        high: candle[2],
        low: candle[3],
        close: candle[4],
        volume: candle[5]
      }))
    } catch (error) {
      console.error(`Failed to fetch kline data for ${symbol} on ${exchangeId}:`, error)
      return null
    }
  }

  async getBalance(exchangeId: string): Promise<any | null> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not available`)
    }

    try {
      const balance = await exchange.fetchBalance()
      return balance
    } catch (error) {
      console.error(`Failed to fetch balance on ${exchangeId}:`, error)
      return null
    }
  }

  async createOrder(
    exchangeId: string,
    symbol: string,
    type: 'market' | 'limit',
    side: 'buy' | 'sell',
    amount: number,
    price?: number,
    params?: any
  ): Promise<any | null> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not available`)
    }

    try {
      const order = await exchange.createOrder(symbol, type, side, amount, price, params)
      return order
    } catch (error) {
      console.error(`Failed to create order on ${exchangeId}:`, error)
      throw error
    }
  }

  async cancelOrder(exchangeId: string, orderId: string, symbol: string): Promise<any | null> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not available`)
    }

    try {
      const result = await exchange.cancelOrder(orderId, symbol)
      return result
    } catch (error) {
      console.error(`Failed to cancel order ${orderId} on ${exchangeId}:`, error)
      throw error
    }
  }

  async getOrderStatus(exchangeId: string, orderId: string, symbol: string): Promise<any | null> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not available`)
    }

    try {
      const order = await exchange.fetchOrder(orderId, symbol)
      return order
    } catch (error) {
      console.error(`Failed to fetch order ${orderId} on ${exchangeId}:`, error)
      return null
    }
  }

  async getOpenOrders(exchangeId: string, symbol?: string): Promise<any[] | null> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not available`)
    }

    try {
      const orders = await exchange.fetchOpenOrders(symbol)
      return orders
    } catch (error) {
      console.error(`Failed to fetch open orders on ${exchangeId}:`, error)
      return null
    }
  }

  async getTradeHistory(exchangeId: string, symbol?: string, limit = 100): Promise<Trade[] | null> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not available`)
    }

    try {
      const trades = await exchange.fetchMyTrades(symbol, undefined, limit)
      return trades.map(trade => ({
        id: trade.id,
        symbol: trade.symbol,
        side: trade.side as 'buy' | 'sell',
        amount: trade.amount,
        price: trade.price,
        fee: trade.fee?.cost,
        timestamp: trade.timestamp
      }))
    } catch (error) {
      console.error(`Failed to fetch trade history on ${exchangeId}:`, error)
      return null
    }
  }

  async getMarkets(exchangeId: string): Promise<any | null> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not available`)
    }

    try {
      const markets = await exchange.loadMarkets()
      return markets
    } catch (error) {
      console.error(`Failed to fetch markets on ${exchangeId}:`, error)
      return null
    }
  }

  async testConnection(exchangeId: string): Promise<boolean> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      return false
    }

    try {
      await exchange.fetchStatus()
      return true
    } catch (error) {
      console.error(`Connection test failed for ${exchangeId}:`, error)
      return false
    }
  }

  // 获取所有交易所的状态
  async getExchangeStatuses(): Promise<Record<string, boolean>> {
    const statuses: Record<string, boolean> = {}

    for (const exchangeId of this.exchanges.keys()) {
      statuses[exchangeId] = await this.testConnection(exchangeId)
    }

    return statuses
  }

  // 获取多个交易所的价格进行比较
  async comparePrice(symbol: string): Promise<Record<string, MarketData | null>> {
    const prices: Record<string, MarketData | null> = {}

    for (const exchangeId of this.exchanges.keys()) {
      try {
        prices[exchangeId] = await this.getMarketData(exchangeId, symbol)
      } catch (error) {
        prices[exchangeId] = null
      }
    }

    return prices
  }

  // 清理资源
  async cleanup(): Promise<void> {
    for (const [exchangeId, exchange] of this.exchanges) {
      try {
        if (exchange.close) {
          await exchange.close()
        }
      } catch (error) {
        console.error(`Failed to close ${exchangeId}:`, error)
      }
    }
    this.exchanges.clear()
    console.log('🧹 Exchange service cleaned up')
  }
}
