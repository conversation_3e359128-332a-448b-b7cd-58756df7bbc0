import { EventEmitter } from 'events'
import { StrategyService, Strategy, StrategyExecution } from './StrategyService'
import { ExchangeService } from './ExchangeService'
import { RealTimeMarketDataService, RealTimePrice } from './RealTimeMarketDataService'
import { RedisService } from './RedisService'
import { OptimizedWebSocketManager } from '../websocket/optimized-websocket'

export interface ExecutionSignal {
  strategyId: string
  action: 'buy' | 'sell' | 'hold'
  symbol: string
  amount: number
  price?: number
  reason: string
  confidence: number
  timestamp: number
}

export interface RiskCheck {
  passed: boolean
  reason?: string
  maxAmount?: number
  stopLoss?: number
  takeProfit?: number
}

export class StrategyExecutionEngine extends EventEmitter {
  private isRunning = false
  private executionInterval: NodeJS.Timeout | null = null
  private activeStrategies: Map<string, Strategy> = new Map()
  private executionQueue: ExecutionSignal[] = []
  private riskLimits = {
    maxPositionSize: 0.1, // 10% of capital
    maxDailyLoss: 0.05,   // 5% daily loss limit
    maxDrawdown: 0.15     // 15% max drawdown
  }

  constructor(
    private strategyService: StrategyService,
    private exchangeService: ExchangeService,
    private marketDataService: RealTimeMarketDataService,
    private redisService: RedisService,
    private wsManager: OptimizedWebSocketManager
  ) {
    super()
    this.setupEventHandlers()
  }

  private setupEventHandlers(): void {
    this.marketDataService.on('price_update', this.handlePriceUpdate.bind(this))
    this.on('execution_signal', this.handleExecutionSignal.bind(this))
    this.on('execution_completed', this.handleExecutionCompleted.bind(this))
  }

  // 启动策略执行引擎
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ Strategy Execution Engine is already running')
      return
    }

    console.log('🚀 Starting Strategy Execution Engine...')
    this.isRunning = true

    // 加载活跃策略
    await this.loadActiveStrategies()

    // 启动执行循环
    this.startExecutionLoop()

    console.log(`✅ Strategy Execution Engine started with ${this.activeStrategies.size} active strategies`)
  }

  // 停止策略执行引擎
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    console.log('🛑 Stopping Strategy Execution Engine...')
    this.isRunning = false

    if (this.executionInterval) {
      clearInterval(this.executionInterval)
      this.executionInterval = null
    }

    this.activeStrategies.clear()
    this.executionQueue = []

    console.log('✅ Strategy Execution Engine stopped')
  }

  // 加载活跃策略
  private async loadActiveStrategies(): Promise<void> {
    try {
      const activeStrategyIds = await this.strategyService.getActiveStrategies()
      
      for (const strategyId of activeStrategyIds) {
        const strategy = await this.strategyService.getStrategy(strategyId)
        if (strategy && strategy.status === 'active') {
          this.activeStrategies.set(strategyId, strategy)
          console.log(`📈 Loaded active strategy: ${strategy.name} (${strategyId})`)
        }
      }
    } catch (error) {
      console.error('Failed to load active strategies:', error)
    }
  }

  // 启动执行循环
  private startExecutionLoop(): void {
    this.executionInterval = setInterval(async () => {
      await this.processExecutionQueue()
      await this.evaluateStrategies()
    }, 5000) // 每5秒执行一次
  }

  // 处理价格更新
  private async handlePriceUpdate(priceData: RealTimePrice): Promise<void> {
    // 为每个活跃策略检查是否需要执行
    for (const [strategyId, strategy] of this.activeStrategies) {
      if (strategy.config.symbol === priceData.symbol) {
        await this.evaluateStrategy(strategy, priceData)
      }
    }
  }

  // 评估策略
  private async evaluateStrategy(strategy: Strategy, priceData: RealTimePrice): Promise<void> {
    try {
      const signal = await this.generateExecutionSignal(strategy, priceData)
      
      if (signal && signal.action !== 'hold') {
        this.emit('execution_signal', signal)
      }
    } catch (error) {
      console.error(`Error evaluating strategy ${strategy.id}:`, error)
    }
  }

  // 生成执行信号
  private async generateExecutionSignal(strategy: Strategy, priceData: RealTimePrice): Promise<ExecutionSignal | null> {
    // 这里实现具体的策略逻辑
    // 根据策略类型调用不同的算法
    
    switch (strategy.type) {
      case 'arbitrage':
        return await this.evaluateArbitrageStrategy(strategy, priceData)
      case 'trend':
        return await this.evaluateTrendStrategy(strategy, priceData)
      case 'grid':
        return await this.evaluateGridStrategy(strategy, priceData)
      case 'dca':
        return await this.evaluateDCAStrategy(strategy, priceData)
      default:
        return null
    }
  }

  // 套利策略评估
  private async evaluateArbitrageStrategy(strategy: Strategy, priceData: RealTimePrice): Promise<ExecutionSignal | null> {
    // 获取其他交易所的价格进行比较
    const otherExchanges = this.exchangeService.getHealthyExchanges()
      .filter(ex => ex !== priceData.exchange)

    let bestOpportunity: { exchange: string; price: number; profit: number } | null = null

    for (const exchange of otherExchanges) {
      const otherPrice = await this.marketDataService.getRealTimePrice(exchange, priceData.symbol)
      if (otherPrice) {
        const priceDiff = Math.abs(priceData.price - otherPrice.price)
        const profitPercent = (priceDiff / priceData.price) * 100
        
        if (profitPercent > 0.5) { // 0.5% 以上的价差
          if (!bestOpportunity || profitPercent > bestOpportunity.profit) {
            bestOpportunity = {
              exchange,
              price: otherPrice.price,
              profit: profitPercent
            }
          }
        }
      }
    }

    if (bestOpportunity) {
      const action = priceData.price < bestOpportunity.price ? 'buy' : 'sell'
      const amount = strategy.config.capital * 0.1 / priceData.price // 10% of capital
      
      return {
        strategyId: strategy.id,
        action,
        symbol: priceData.symbol,
        amount,
        price: priceData.price,
        reason: `Arbitrage opportunity: ${bestOpportunity.profit.toFixed(2)}% profit`,
        confidence: Math.min(bestOpportunity.profit * 20, 100), // 置信度基于利润率
        timestamp: Date.now()
      }
    }

    return null
  }

  // 趋势策略评估
  private async evaluateTrendStrategy(strategy: Strategy, priceData: RealTimePrice): Promise<ExecutionSignal | null> {
    // 简单的移动平均线策略
    const prices = await this.getHistoricalPrices(priceData.exchange, priceData.symbol, 20)
    
    if (prices.length < 20) {
      return null
    }

    const sma20 = prices.reduce((sum, price) => sum + price, 0) / prices.length
    const currentPrice = priceData.price

    let action: 'buy' | 'sell' | 'hold' = 'hold'
    let reason = ''

    if (currentPrice > sma20 * 1.02) { // 价格比20日均线高2%
      action = 'buy'
      reason = `Price above SMA20 by ${((currentPrice / sma20 - 1) * 100).toFixed(2)}%`
    } else if (currentPrice < sma20 * 0.98) { // 价格比20日均线低2%
      action = 'sell'
      reason = `Price below SMA20 by ${((1 - currentPrice / sma20) * 100).toFixed(2)}%`
    }

    if (action !== 'hold') {
      const amount = strategy.config.capital * 0.05 / currentPrice // 5% of capital
      
      return {
        strategyId: strategy.id,
        action,
        symbol: priceData.symbol,
        amount,
        price: currentPrice,
        reason,
        confidence: 70,
        timestamp: Date.now()
      }
    }

    return null
  }

  // 网格策略评估
  private async evaluateGridStrategy(strategy: Strategy, priceData: RealTimePrice): Promise<ExecutionSignal | null> {
    // 简单的网格策略实现
    const gridSize = strategy.config.parameters?.gridSize || 0.01 // 1% 网格
    const basePrice = strategy.config.parameters?.basePrice || priceData.price
    
    const currentPrice = priceData.price
    const priceChange = (currentPrice - basePrice) / basePrice
    
    if (Math.abs(priceChange) > gridSize) {
      const action = priceChange > 0 ? 'sell' : 'buy'
      const amount = strategy.config.capital * 0.02 / currentPrice // 2% of capital
      
      return {
        strategyId: strategy.id,
        action,
        symbol: priceData.symbol,
        amount,
        price: currentPrice,
        reason: `Grid trigger: ${(priceChange * 100).toFixed(2)}% from base price`,
        confidence: 80,
        timestamp: Date.now()
      }
    }

    return null
  }

  // DCA策略评估
  private async evaluateDCAStrategy(strategy: Strategy, priceData: RealTimePrice): Promise<ExecutionSignal | null> {
    // 定期定额投资策略
    const interval = strategy.config.parameters?.interval || 3600000 // 1小时
    const lastExecution = strategy.lastExecuted?.getTime() || 0
    
    if (Date.now() - lastExecution > interval) {
      const amount = strategy.config.capital * 0.01 / priceData.price // 1% of capital
      
      return {
        strategyId: strategy.id,
        action: 'buy',
        symbol: priceData.symbol,
        amount,
        price: priceData.price,
        reason: 'DCA scheduled purchase',
        confidence: 90,
        timestamp: Date.now()
      }
    }

    return null
  }

  // 处理执行信号
  private async handleExecutionSignal(signal: ExecutionSignal): Promise<void> {
    // 风险检查
    const riskCheck = await this.performRiskCheck(signal)
    
    if (!riskCheck.passed) {
      console.warn(`❌ Risk check failed for signal: ${riskCheck.reason}`)
      return
    }

    // 添加到执行队列
    this.executionQueue.push(signal)
    
    // 通知客户端
    this.wsManager.broadcast('trading', {
      type: 'execution_signal',
      data: signal
    })
  }

  // 风险检查
  private async performRiskCheck(signal: ExecutionSignal): Promise<RiskCheck> {
    const strategy = this.activeStrategies.get(signal.strategyId)
    if (!strategy) {
      return { passed: false, reason: 'Strategy not found' }
    }

    // 检查仓位大小
    const positionValue = signal.amount * (signal.price || 0)
    const maxPositionValue = strategy.config.capital * this.riskLimits.maxPositionSize
    
    if (positionValue > maxPositionValue) {
      return { 
        passed: false, 
        reason: 'Position size exceeds limit',
        maxAmount: maxPositionValue / (signal.price || 1)
      }
    }

    // 检查账户余额
    const balance = await this.exchangeService.getBalance(strategy.config.exchange)
    if (!balance) {
      return { passed: false, reason: 'Unable to fetch balance' }
    }

    // 其他风险检查...
    
    return { passed: true }
  }

  // 处理执行队列
  private async processExecutionQueue(): Promise<void> {
    while (this.executionQueue.length > 0 && this.isRunning) {
      const signal = this.executionQueue.shift()
      if (signal) {
        await this.executeSignal(signal)
      }
    }
  }

  // 执行信号
  private async executeSignal(signal: ExecutionSignal): Promise<void> {
    try {
      const strategy = this.activeStrategies.get(signal.strategyId)
      if (!strategy) {
        console.error(`Strategy not found: ${signal.strategyId}`)
        return
      }

      console.log(`🎯 Executing signal: ${signal.action} ${signal.amount} ${signal.symbol} at ${signal.price}`)

      // 创建订单
      const order = await this.exchangeService.createOrder(
        strategy.config.exchange,
        signal.symbol,
        'market',
        signal.action,
        signal.amount,
        signal.price
      )

      if (order) {
        // 记录执行
        const execution: StrategyExecution = {
          id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          strategyId: signal.strategyId,
          action: signal.action,
          symbol: signal.symbol,
          amount: signal.amount,
          price: signal.price || 0,
          reason: signal.reason,
          timestamp: new Date(),
          status: 'executed',
          orderId: order.id
        }

        // 更新策略最后执行时间
        await this.strategyService.updateStrategy(signal.strategyId, {
          lastExecuted: new Date()
        })

        this.emit('execution_completed', execution)
        
        console.log(`✅ Signal executed successfully: ${order.id}`)
      }
    } catch (error) {
      console.error(`❌ Failed to execute signal:`, error)
      
      // 通知执行失败
      this.wsManager.broadcast('trading', {
        type: 'execution_failed',
        data: { signal, error: error.message }
      })
    }
  }

  // 处理执行完成
  private async handleExecutionCompleted(execution: StrategyExecution): Promise<void> {
    // 缓存执行记录
    const cacheKey = `execution:${execution.strategyId}:${execution.id}`
    await this.redisService.set(cacheKey, execution, 3600) // 1小时缓存

    // 通知客户端
    this.wsManager.broadcast('trading', {
      type: 'execution_completed',
      data: execution
    })
  }

  // 评估所有策略
  private async evaluateStrategies(): Promise<void> {
    for (const [strategyId, strategy] of this.activeStrategies) {
      try {
        // 获取最新价格
        const priceData = await this.marketDataService.getRealTimePrice(
          strategy.config.exchange,
          strategy.config.symbol
        )
        
        if (priceData) {
          await this.evaluateStrategy(strategy, priceData)
        }
      } catch (error) {
        console.error(`Error evaluating strategy ${strategyId}:`, error)
      }
    }
  }

  // 获取历史价格
  private async getHistoricalPrices(exchange: string, symbol: string, count: number): Promise<number[]> {
    try {
      const klineData = await this.exchangeService.getKlineData(exchange, symbol, '1h', count)
      return klineData ? klineData.map(candle => candle.close) : []
    } catch (error) {
      console.error('Failed to get historical prices:', error)
      return []
    }
  }

  // 添加策略到执行引擎
  async addStrategy(strategy: Strategy): Promise<void> {
    if (strategy.status === 'active') {
      this.activeStrategies.set(strategy.id, strategy)
      console.log(`📈 Added strategy to execution engine: ${strategy.name}`)
    }
  }

  // 从执行引擎移除策略
  async removeStrategy(strategyId: string): Promise<void> {
    this.activeStrategies.delete(strategyId)
    console.log(`📉 Removed strategy from execution engine: ${strategyId}`)
  }

  // 获取执行统计
  getExecutionStats(): any {
    return {
      isRunning: this.isRunning,
      activeStrategies: this.activeStrategies.size,
      queueLength: this.executionQueue.length,
      strategies: Array.from(this.activeStrategies.values()).map(s => ({
        id: s.id,
        name: s.name,
        type: s.type,
        symbol: s.config.symbol,
        exchange: s.config.exchange
      }))
    }
  }
}
