import { RedisService } from './RedisService'

export interface PerformanceMetrics {
  timestamp: number
  requestCount: number
  responseTime: number
  errorCount: number
  memoryUsage: NodeJS.MemoryUsage
  cpuUsage: NodeJS.CpuUsage
  activeConnections: number
}

export interface AlertThreshold {
  metric: string
  operator: 'gt' | 'lt' | 'eq'
  value: number
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export interface Alert {
  id: string
  metric: string
  currentValue: number
  threshold: AlertThreshold
  message: string
  timestamp: number
  acknowledged: boolean
}

export class PerformanceTrackingService {
  private metrics: PerformanceMetrics[] = []
  private alerts: Alert[] = []
  private thresholds: AlertThreshold[] = []
  private isTracking = false
  private trackingInterval?: NodeJS.Timeout

  constructor(private redisService: RedisService) {
    this.setupDefaultThresholds()
  }

  private setupDefaultThresholds(): void {
    this.thresholds = [
      {
        metric: 'responseTime',
        operator: 'gt',
        value: 1000, // 1秒
        severity: 'medium'
      },
      {
        metric: 'responseTime',
        operator: 'gt',
        value: 5000, // 5秒
        severity: 'high'
      },
      {
        metric: 'errorCount',
        operator: 'gt',
        value: 10,
        severity: 'medium'
      },
      {
        metric: 'errorCount',
        operator: 'gt',
        value: 50,
        severity: 'critical'
      },
      {
        metric: 'memoryUsage',
        operator: 'gt',
        value: 512 * 1024 * 1024, // 512MB
        severity: 'medium'
      },
      {
        metric: 'memoryUsage',
        operator: 'gt',
        value: 1024 * 1024 * 1024, // 1GB
        severity: 'high'
      }
    ]
  }

  // 开始性能追踪
  startTracking(interval = 10000): void {
    if (this.isTracking) {
      return
    }

    this.isTracking = true
    this.trackingInterval = setInterval(() => {
      this.collectMetrics()
    }, interval)

    console.log(`📊 Performance tracking started with ${interval}ms interval`)
  }

  // 停止性能追踪
  stopTracking(): void {
    if (this.trackingInterval) {
      clearInterval(this.trackingInterval)
      this.trackingInterval = undefined
    }
    this.isTracking = false
    console.log('📊 Performance tracking stopped')
  }

  // 收集性能指标
  private async collectMetrics(): Promise<void> {
    try {
      const metrics: PerformanceMetrics = {
        timestamp: Date.now(),
        requestCount: await this.getRequestCount(),
        responseTime: await this.getAverageResponseTime(),
        errorCount: await this.getErrorCount(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
        activeConnections: await this.getActiveConnections()
      }

      // 保存指标
      this.metrics.push(metrics)
      
      // 只保留最近1000个指标点
      if (this.metrics.length > 1000) {
        this.metrics = this.metrics.slice(-1000)
      }

      // 保存到Redis
      await this.saveMetricsToRedis(metrics)

      // 检查告警
      await this.checkAlerts(metrics)

    } catch (error) {
      console.error('Failed to collect performance metrics:', error)
    }
  }

  // 保存指标到Redis
  private async saveMetricsToRedis(metrics: PerformanceMetrics): Promise<void> {
    const key = `performance_metrics:${Math.floor(metrics.timestamp / 60000)}` // 按分钟分组
    await this.redisService.lpush(key, metrics)
    await this.redisService.expire(key, 3600) // 1小时过期
  }

  // 获取请求数量
  private async getRequestCount(): Promise<number> {
    const count = await this.redisService.get<number>('request_count') || 0
    return count
  }

  // 获取平均响应时间
  private async getAverageResponseTime(): Promise<number> {
    const responseTime = await this.redisService.get<number>('avg_response_time') || 0
    return responseTime
  }

  // 获取错误数量
  private async getErrorCount(): Promise<number> {
    const count = await this.redisService.get<number>('error_count') || 0
    return count
  }

  // 获取活跃连接数
  private async getActiveConnections(): Promise<number> {
    const count = await this.redisService.get<number>('active_connections') || 0
    return count
  }

  // 检查告警
  private async checkAlerts(metrics: PerformanceMetrics): Promise<void> {
    for (const threshold of this.thresholds) {
      const currentValue = this.getMetricValue(metrics, threshold.metric)
      
      if (this.shouldTriggerAlert(currentValue, threshold)) {
        await this.triggerAlert(threshold, currentValue)
      }
    }
  }

  // 获取指标值
  private getMetricValue(metrics: PerformanceMetrics, metricName: string): number {
    switch (metricName) {
      case 'responseTime':
        return metrics.responseTime
      case 'errorCount':
        return metrics.errorCount
      case 'memoryUsage':
        return metrics.memoryUsage.heapUsed
      case 'requestCount':
        return metrics.requestCount
      case 'activeConnections':
        return metrics.activeConnections
      default:
        return 0
    }
  }

  // 判断是否应该触发告警
  private shouldTriggerAlert(currentValue: number, threshold: AlertThreshold): boolean {
    switch (threshold.operator) {
      case 'gt':
        return currentValue > threshold.value
      case 'lt':
        return currentValue < threshold.value
      case 'eq':
        return currentValue === threshold.value
      default:
        return false
    }
  }

  // 触发告警
  private async triggerAlert(threshold: AlertThreshold, currentValue: number): Promise<void> {
    const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const alert: Alert = {
      id: alertId,
      metric: threshold.metric,
      currentValue,
      threshold,
      message: this.generateAlertMessage(threshold, currentValue),
      timestamp: Date.now(),
      acknowledged: false
    }

    this.alerts.push(alert)

    // 保存到Redis
    await this.redisService.lpush('performance_alerts', alert)
    await this.redisService.expire('performance_alerts', 86400) // 24小时过期

    // 发布告警事件
    await this.redisService.publish('alerts', {
      type: 'performance_alert',
      alert,
      timestamp: Date.now()
    })

    console.warn(`🚨 Performance alert: ${alert.message}`)
  }

  // 生成告警消息
  private generateAlertMessage(threshold: AlertThreshold, currentValue: number): string {
    const operator = threshold.operator === 'gt' ? 'exceeds' : 
                    threshold.operator === 'lt' ? 'below' : 'equals'
    
    return `${threshold.metric} ${operator} threshold: ${currentValue} ${threshold.operator} ${threshold.value}`
  }

  // 获取最新指标
  getLatestMetrics(): PerformanceMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null
  }

  // 获取历史指标
  getHistoricalMetrics(limit = 100): PerformanceMetrics[] {
    return this.metrics.slice(-limit)
  }

  // 获取指标统计
  getMetricsStats(minutes = 60): any {
    const cutoff = Date.now() - (minutes * 60 * 1000)
    const recentMetrics = this.metrics.filter(m => m.timestamp > cutoff)

    if (recentMetrics.length === 0) {
      return null
    }

    const responseTimes = recentMetrics.map(m => m.responseTime)
    const errorCounts = recentMetrics.map(m => m.errorCount)
    const memoryUsages = recentMetrics.map(m => m.memoryUsage.heapUsed)

    return {
      period: `${minutes} minutes`,
      sampleCount: recentMetrics.length,
      responseTime: {
        avg: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
        min: Math.min(...responseTimes),
        max: Math.max(...responseTimes)
      },
      errorCount: {
        total: errorCounts.reduce((a, b) => a + b, 0),
        avg: errorCounts.reduce((a, b) => a + b, 0) / errorCounts.length
      },
      memoryUsage: {
        avg: memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length,
        min: Math.min(...memoryUsages),
        max: Math.max(...memoryUsages)
      }
    }
  }

  // 获取活跃告警
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.acknowledged)
  }

  // 确认告警
  async acknowledgeAlert(alertId: string): Promise<boolean> {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.acknowledged = true
      
      // 更新Redis中的告警状态
      await this.redisService.publish('alerts', {
        type: 'alert_acknowledged',
        alertId,
        timestamp: Date.now()
      })
      
      return true
    }
    return false
  }

  // 添加自定义阈值
  addThreshold(threshold: AlertThreshold): void {
    this.thresholds.push(threshold)
  }

  // 移除阈值
  removeThreshold(metric: string, value: number): boolean {
    const index = this.thresholds.findIndex(t => t.metric === metric && t.value === value)
    if (index !== -1) {
      this.thresholds.splice(index, 1)
      return true
    }
    return false
  }

  // 获取当前阈值配置
  getThresholds(): AlertThreshold[] {
    return [...this.thresholds]
  }

  // 记录请求
  async recordRequest(responseTime: number, isError = false): Promise<void> {
    // 增加请求计数
    await this.redisService.set('request_count', 
      (await this.redisService.get<number>('request_count') || 0) + 1, 60)

    // 更新平均响应时间
    const currentAvg = await this.redisService.get<number>('avg_response_time') || 0
    const currentCount = await this.redisService.get<number>('request_count') || 1
    const newAvg = ((currentAvg * (currentCount - 1)) + responseTime) / currentCount
    await this.redisService.set('avg_response_time', newAvg, 60)

    // 记录错误
    if (isError) {
      await this.redisService.set('error_count', 
        (await this.redisService.get<number>('error_count') || 0) + 1, 60)
    }
  }

  // 记录活跃连接数
  async updateActiveConnections(count: number): Promise<void> {
    await this.redisService.set('active_connections', count, 60)
  }

  // 清理旧数据
  async cleanup(): Promise<void> {
    // 清理内存中的旧指标
    const cutoff = Date.now() - (24 * 60 * 60 * 1000) // 24小时前
    this.metrics = this.metrics.filter(m => m.timestamp > cutoff)
    
    // 清理旧告警
    this.alerts = this.alerts.filter(a => a.timestamp > cutoff)
    
    console.log('🧹 Performance tracking data cleaned up')
  }
}
