import { DatabaseService } from './DatabaseService'
import { RedisService } from './RedisService'

export interface Strategy {
  id: string
  name: string
  description?: string
  type: 'arbitrage' | 'trend' | 'grid' | 'dca' | 'ai' | 'custom'
  status: 'active' | 'inactive' | 'paused' | 'error'
  config: StrategyConfig
  userId: string
  createdAt: Date
  updatedAt: Date
  lastExecuted?: Date
  performance?: StrategyPerformance
}

export interface StrategyConfig {
  symbol: string
  exchange: string
  capital: number
  riskLevel: 'low' | 'medium' | 'high'
  parameters: Record<string, any>
  stopLoss?: number
  takeProfit?: number
  maxDrawdown?: number
}

export interface StrategyPerformance {
  totalReturn: number
  totalReturnPercent: number
  sharpeRatio?: number
  maxDrawdown?: number
  winRate?: number
  totalTrades: number
  profitableTrades: number
  averageProfit: number
  averageLoss: number
  lastUpdated: Date
}

export interface StrategyExecution {
  id: string
  strategyId: string
  action: 'buy' | 'sell' | 'hold'
  symbol: string
  amount: number
  price: number
  reason: string
  timestamp: Date
  status: 'pending' | 'executed' | 'failed' | 'cancelled'
  orderId?: string
}

export class StrategyService {
  constructor(
    private databaseService: DatabaseService,
    private redisService: RedisService
  ) {}

  // 创建策略
  async createStrategy(userId: string, strategyData: Omit<Strategy, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<Strategy> {
    try {
      // 验证策略配置
      this.validateStrategyConfig(strategyData.config)

      const strategy = await this.databaseService.createStrategy({
        name: strategyData.name,
        description: strategyData.description,
        type: strategyData.type,
        config: strategyData.config,
        userId
      })

      // 缓存策略信息
      await this.cacheStrategy(strategy)

      console.log(`✅ Strategy created: ${strategy.name} (${strategy.id})`)
      return strategy
    } catch (error) {
      console.error('Failed to create strategy:', error)
      throw error
    }
  }

  // 获取用户的所有策略
  async getUserStrategies(userId: string): Promise<Strategy[]> {
    try {
      const cacheKey = `user_strategies:${userId}`
      const cached = await this.redisService.get<Strategy[]>(cacheKey)
      
      if (cached) {
        return cached
      }

      const strategies = await this.databaseService.findStrategiesByUserId(userId)
      
      // 缓存结果
      await this.redisService.set(cacheKey, strategies, 300) // 5分钟缓存
      
      return strategies
    } catch (error) {
      console.error('Failed to get user strategies:', error)
      throw error
    }
  }

  // 获取策略详情
  async getStrategy(strategyId: string): Promise<Strategy | null> {
    try {
      const cacheKey = `strategy:${strategyId}`
      const cached = await this.redisService.get<Strategy>(cacheKey)
      
      if (cached) {
        return cached
      }

      const strategy = await this.databaseService.findStrategyById(strategyId)
      
      if (strategy) {
        await this.cacheStrategy(strategy)
      }
      
      return strategy
    } catch (error) {
      console.error('Failed to get strategy:', error)
      return null
    }
  }

  // 更新策略
  async updateStrategy(strategyId: string, updates: Partial<Strategy>): Promise<Strategy | null> {
    try {
      if (updates.config) {
        this.validateStrategyConfig(updates.config)
      }

      const strategy = await this.databaseService.updateStrategy(strategyId, updates)
      
      if (strategy) {
        await this.cacheStrategy(strategy)
        await this.invalidateUserStrategiesCache(strategy.userId)
      }
      
      console.log(`✅ Strategy updated: ${strategyId}`)
      return strategy
    } catch (error) {
      console.error('Failed to update strategy:', error)
      throw error
    }
  }

  // 删除策略
  async deleteStrategy(strategyId: string): Promise<boolean> {
    try {
      const strategy = await this.getStrategy(strategyId)
      if (!strategy) {
        return false
      }

      // 如果策略正在运行，先停止它
      if (strategy.status === 'active') {
        await this.stopStrategy(strategyId)
      }

      await this.databaseService.deleteStrategy(strategyId)
      
      // 清理缓存
      await this.redisService.del(`strategy:${strategyId}`)
      await this.invalidateUserStrategiesCache(strategy.userId)
      
      console.log(`✅ Strategy deleted: ${strategyId}`)
      return true
    } catch (error) {
      console.error('Failed to delete strategy:', error)
      return false
    }
  }

  // 启动策略
  async startStrategy(strategyId: string): Promise<boolean> {
    try {
      const strategy = await this.getStrategy(strategyId)
      if (!strategy) {
        throw new Error('Strategy not found')
      }

      if (strategy.status === 'active') {
        return true // 已经在运行
      }

      // 更新状态为活跃
      await this.updateStrategy(strategyId, { 
        status: 'active',
        lastExecuted: new Date()
      })

      // 添加到执行队列
      await this.addToExecutionQueue(strategyId)

      console.log(`🚀 Strategy started: ${strategy.name} (${strategyId})`)
      return true
    } catch (error) {
      console.error('Failed to start strategy:', error)
      return false
    }
  }

  // 停止策略
  async stopStrategy(strategyId: string): Promise<boolean> {
    try {
      const strategy = await this.getStrategy(strategyId)
      if (!strategy) {
        throw new Error('Strategy not found')
      }

      // 更新状态为非活跃
      await this.updateStrategy(strategyId, { status: 'inactive' })

      // 从执行队列移除
      await this.removeFromExecutionQueue(strategyId)

      console.log(`⏹️ Strategy stopped: ${strategy.name} (${strategyId})`)
      return true
    } catch (error) {
      console.error('Failed to stop strategy:', error)
      return false
    }
  }

  // 暂停策略
  async pauseStrategy(strategyId: string): Promise<boolean> {
    try {
      await this.updateStrategy(strategyId, { status: 'paused' })
      console.log(`⏸️ Strategy paused: ${strategyId}`)
      return true
    } catch (error) {
      console.error('Failed to pause strategy:', error)
      return false
    }
  }

  // 恢复策略
  async resumeStrategy(strategyId: string): Promise<boolean> {
    try {
      await this.updateStrategy(strategyId, { status: 'active' })
      await this.addToExecutionQueue(strategyId)
      console.log(`▶️ Strategy resumed: ${strategyId}`)
      return true
    } catch (error) {
      console.error('Failed to resume strategy:', error)
      return false
    }
  }

  // 获取策略性能
  async getStrategyPerformance(strategyId: string): Promise<StrategyPerformance | null> {
    try {
      const cacheKey = `strategy_performance:${strategyId}`
      const cached = await this.redisService.get<StrategyPerformance>(cacheKey)
      
      if (cached) {
        return cached
      }

      const performance = await this.calculateStrategyPerformance(strategyId)
      
      if (performance) {
        // 缓存性能数据
        await this.redisService.set(cacheKey, performance, 600) // 10分钟缓存
      }
      
      return performance
    } catch (error) {
      console.error('Failed to get strategy performance:', error)
      return null
    }
  }

  // 计算策略性能
  private async calculateStrategyPerformance(strategyId: string): Promise<StrategyPerformance | null> {
    try {
      const trades = await this.databaseService.findTradesByStrategyId(strategyId)
      
      if (trades.length === 0) {
        return null
      }

      let totalProfit = 0
      let profitableTrades = 0
      let totalProfits = 0
      let totalLosses = 0
      let profitCount = 0
      let lossCount = 0

      for (const trade of trades) {
        const profit = trade.side === 'sell' 
          ? (trade.price * trade.amount) - (trade.fee || 0)
          : -(trade.price * trade.amount) - (trade.fee || 0)
        
        totalProfit += profit
        
        if (profit > 0) {
          profitableTrades++
          totalProfits += profit
          profitCount++
        } else {
          totalLosses += Math.abs(profit)
          lossCount++
        }
      }

      const winRate = trades.length > 0 ? (profitableTrades / trades.length) * 100 : 0
      const averageProfit = profitCount > 0 ? totalProfits / profitCount : 0
      const averageLoss = lossCount > 0 ? totalLosses / lossCount : 0

      return {
        totalReturn: totalProfit,
        totalReturnPercent: 0, // 需要初始资金来计算
        winRate,
        totalTrades: trades.length,
        profitableTrades,
        averageProfit,
        averageLoss,
        lastUpdated: new Date()
      }
    } catch (error) {
      console.error('Failed to calculate strategy performance:', error)
      return null
    }
  }

  // 验证策略配置
  private validateStrategyConfig(config: StrategyConfig): void {
    if (!config.symbol) {
      throw new Error('Symbol is required')
    }
    if (!config.exchange) {
      throw new Error('Exchange is required')
    }
    if (!config.capital || config.capital <= 0) {
      throw new Error('Capital must be greater than 0')
    }
    if (!['low', 'medium', 'high'].includes(config.riskLevel)) {
      throw new Error('Invalid risk level')
    }
  }

  // 缓存策略
  private async cacheStrategy(strategy: any): Promise<void> {
    const cacheKey = `strategy:${strategy.id}`
    await this.redisService.set(cacheKey, strategy, 600) // 10分钟缓存
  }

  // 清理用户策略缓存
  private async invalidateUserStrategiesCache(userId: string): Promise<void> {
    await this.redisService.del(`user_strategies:${userId}`)
  }

  // 添加到执行队列
  private async addToExecutionQueue(strategyId: string): Promise<void> {
    await this.redisService.sadd('active_strategies', strategyId)
  }

  // 从执行队列移除
  private async removeFromExecutionQueue(strategyId: string): Promise<void> {
    await this.redisService.srem('active_strategies', strategyId)
  }

  // 获取活跃策略列表
  async getActiveStrategies(): Promise<string[]> {
    return this.redisService.smembers<string>('active_strategies')
  }

  // 获取策略统计
  async getStrategyStats(userId?: string): Promise<any> {
    try {
      const cacheKey = userId ? `strategy_stats:${userId}` : 'strategy_stats:global'
      const cached = await this.redisService.get(cacheKey)
      
      if (cached) {
        return cached
      }

      // 这里应该从数据库查询实际统计数据
      const stats = {
        totalStrategies: 0,
        activeStrategies: 0,
        totalTrades: 0,
        totalProfit: 0,
        lastUpdated: new Date()
      }

      // 缓存统计数据
      await this.redisService.set(cacheKey, stats, 300) // 5分钟缓存
      
      return stats
    } catch (error) {
      console.error('Failed to get strategy stats:', error)
      return null
    }
  }
}
