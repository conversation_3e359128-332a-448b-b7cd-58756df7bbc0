import WebSocket from 'ws'
import { EventEmitter } from 'events'
import { ExchangeService } from './ExchangeService'
import { RedisService } from './RedisService'
import { OptimizedWebSocketManager } from '../websocket/optimized-websocket'

export interface RealTimePrice {
  symbol: string
  exchange: string
  price: number
  bid: number
  ask: number
  volume: number
  change24h: number
  timestamp: number
}

export interface RealTimeOrderBook {
  symbol: string
  exchange: string
  bids: [number, number][]
  asks: [number, number][]
  timestamp: number
}

export interface RealTimeTrade {
  symbol: string
  exchange: string
  price: number
  amount: number
  side: 'buy' | 'sell'
  timestamp: number
}

export class RealTimeMarketDataService extends EventEmitter {
  private wsConnections: Map<string, WebSocket> = new Map()
  private subscriptions: Map<string, Set<string>> = new Map()
  private reconnectAttempts: Map<string, number> = new Map()
  private maxReconnectAttempts = 5
  private reconnectDelay = 5000

  constructor(
    private exchangeService: ExchangeService,
    private redisService: RedisService,
    private wsManager: OptimizedWebSocketManager
  ) {
    super()
    this.setupEventHandlers()
  }

  private setupEventHandlers(): void {
    this.on('price_update', this.handlePriceUpdate.bind(this))
    this.on('orderbook_update', this.handleOrderBookUpdate.bind(this))
    this.on('trade_update', this.handleTradeUpdate.bind(this))
  }

  // 启动实时数据服务
  async start(): Promise<void> {
    console.log('🚀 Starting Real-Time Market Data Service...')
    
    const exchanges = this.exchangeService.getHealthyExchanges()
    
    for (const exchange of exchanges) {
      await this.connectToExchange(exchange)
    }

    console.log(`✅ Real-Time Market Data Service started for ${exchanges.length} exchanges`)
  }

  // 连接到交易所 WebSocket
  private async connectToExchange(exchange: string): Promise<void> {
    try {
      const wsUrl = this.getWebSocketUrl(exchange)
      if (!wsUrl) {
        console.warn(`⚠️ No WebSocket URL for exchange: ${exchange}`)
        return
      }

      const ws = new WebSocket(wsUrl)
      this.wsConnections.set(exchange, ws)

      ws.on('open', () => {
        console.log(`🔗 Connected to ${exchange} WebSocket`)
        this.reconnectAttempts.set(exchange, 0)
        this.subscribeToDefaultChannels(exchange, ws)
      })

      ws.on('message', (data: Buffer) => {
        this.handleWebSocketMessage(exchange, data)
      })

      ws.on('close', () => {
        console.log(`❌ Disconnected from ${exchange} WebSocket`)
        this.handleReconnect(exchange)
      })

      ws.on('error', (error) => {
        console.error(`❌ WebSocket error for ${exchange}:`, error)
        this.handleReconnect(exchange)
      })

    } catch (error) {
      console.error(`Failed to connect to ${exchange}:`, error)
    }
  }

  // 获取交易所 WebSocket URL
  private getWebSocketUrl(exchange: string): string | null {
    const urls: Record<string, string> = {
      binance: 'wss://stream.binance.com:9443/ws/btcusdt@ticker/ethusdt@ticker/adausdt@ticker',
      okx: 'wss://ws.okx.com:8443/ws/v5/public',
      bybit: 'wss://stream.bybit.com/v5/public/spot',
      huobi: 'wss://api.huobi.pro/ws'
    }
    
    return urls[exchange] || null
  }

  // 订阅默认频道
  private subscribeToDefaultChannels(exchange: string, ws: WebSocket): void {
    const defaultSymbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'BNBUSDT', 'SOLUSDT']
    
    switch (exchange) {
      case 'binance':
        this.subscribeBinanceChannels(ws, defaultSymbols)
        break
      case 'okx':
        this.subscribeOKXChannels(ws, defaultSymbols)
        break
      case 'bybit':
        this.subscribeBybitChannels(ws, defaultSymbols)
        break
      case 'huobi':
        this.subscribeHuobiChannels(ws, defaultSymbols)
        break
    }
  }

  // 币安订阅
  private subscribeBinanceChannels(ws: WebSocket, symbols: string[]): void {
    const streams = symbols.flatMap(symbol => [
      `${symbol.toLowerCase()}@ticker`,
      `${symbol.toLowerCase()}@depth20@100ms`,
      `${symbol.toLowerCase()}@trade`
    ])

    const subscribeMsg = {
      method: 'SUBSCRIBE',
      params: streams,
      id: Date.now()
    }

    ws.send(JSON.stringify(subscribeMsg))
  }

  // OKX订阅
  private subscribeOKXChannels(ws: WebSocket, symbols: string[]): void {
    const args = symbols.flatMap(symbol => [
      { channel: 'tickers', instId: symbol },
      { channel: 'books5', instId: symbol },
      { channel: 'trades', instId: symbol }
    ])

    const subscribeMsg = {
      op: 'subscribe',
      args
    }

    ws.send(JSON.stringify(subscribeMsg))
  }

  // Bybit订阅
  private subscribeBybitChannels(ws: WebSocket, symbols: string[]): void {
    const topics = symbols.flatMap(symbol => [
      `tickers.${symbol}`,
      `orderbook.20.${symbol}`,
      `publicTrade.${symbol}`
    ])

    const subscribeMsg = {
      op: 'subscribe',
      args: topics
    }

    ws.send(JSON.stringify(subscribeMsg))
  }

  // 火币订阅
  private subscribeHuobiChannels(ws: WebSocket, symbols: string[]): void {
    symbols.forEach(symbol => {
      const tickerSub = {
        sub: `market.${symbol.toLowerCase()}.ticker`,
        id: `ticker_${symbol}_${Date.now()}`
      }
      
      const depthSub = {
        sub: `market.${symbol.toLowerCase()}.depth.step0`,
        id: `depth_${symbol}_${Date.now()}`
      }

      ws.send(JSON.stringify(tickerSub))
      ws.send(JSON.stringify(depthSub))
    })
  }

  // 处理 WebSocket 消息
  private handleWebSocketMessage(exchange: string, data: Buffer): void {
    try {
      const message = JSON.parse(data.toString())
      
      switch (exchange) {
        case 'binance':
          this.handleBinanceMessage(message)
          break
        case 'okx':
          this.handleOKXMessage(message)
          break
        case 'bybit':
          this.handleBybitMessage(message)
          break
        case 'huobi':
          this.handleHuobiMessage(message)
          break
      }
    } catch (error) {
      console.error(`Error parsing WebSocket message from ${exchange}:`, error)
    }
  }

  // 处理币安消息
  private handleBinanceMessage(message: any): void {
    if (message.stream && message.data) {
      const stream = message.stream
      const data = message.data
      
      if (stream.includes('@ticker')) {
        const priceData: RealTimePrice = {
          symbol: data.s,
          exchange: 'binance',
          price: parseFloat(data.c),
          bid: parseFloat(data.b),
          ask: parseFloat(data.a),
          volume: parseFloat(data.v),
          change24h: parseFloat(data.P),
          timestamp: data.E
        }
        this.emit('price_update', priceData)
      }
      
      if (stream.includes('@depth')) {
        const orderBookData: RealTimeOrderBook = {
          symbol: data.s,
          exchange: 'binance',
          bids: data.bids.map((bid: string[]) => [parseFloat(bid[0]), parseFloat(bid[1])]),
          asks: data.asks.map((ask: string[]) => [parseFloat(ask[0]), parseFloat(ask[1])]),
          timestamp: Date.now()
        }
        this.emit('orderbook_update', orderBookData)
      }
      
      if (stream.includes('@trade')) {
        const tradeData: RealTimeTrade = {
          symbol: data.s,
          exchange: 'binance',
          price: parseFloat(data.p),
          amount: parseFloat(data.q),
          side: data.m ? 'sell' : 'buy',
          timestamp: data.T
        }
        this.emit('trade_update', tradeData)
      }
    }
  }

  // 处理OKX消息
  private handleOKXMessage(message: any): void {
    if (message.data && message.arg) {
      const channel = message.arg.channel
      const data = message.data[0]
      
      if (channel === 'tickers') {
        const priceData: RealTimePrice = {
          symbol: data.instId,
          exchange: 'okx',
          price: parseFloat(data.last),
          bid: parseFloat(data.bidPx),
          ask: parseFloat(data.askPx),
          volume: parseFloat(data.vol24h),
          change24h: parseFloat(data.chgUtc8),
          timestamp: parseInt(data.ts)
        }
        this.emit('price_update', priceData)
      }
    }
  }

  // 处理Bybit消息
  private handleBybitMessage(message: any): void {
    if (message.topic && message.data) {
      const topic = message.topic
      const data = message.data
      
      if (topic.startsWith('tickers.')) {
        const priceData: RealTimePrice = {
          symbol: data.symbol,
          exchange: 'bybit',
          price: parseFloat(data.lastPrice),
          bid: parseFloat(data.bid1Price),
          ask: parseFloat(data.ask1Price),
          volume: parseFloat(data.volume24h),
          change24h: parseFloat(data.price24hPcnt) * 100,
          timestamp: parseInt(data.time)
        }
        this.emit('price_update', priceData)
      }
    }
  }

  // 处理火币消息
  private handleHuobiMessage(message: any): void {
    if (message.ch && message.tick) {
      const channel = message.ch
      const data = message.tick
      
      if (channel.includes('.ticker')) {
        const symbol = channel.split('.')[1].toUpperCase()
        const priceData: RealTimePrice = {
          symbol,
          exchange: 'huobi',
          price: data.close,
          bid: data.bid[0],
          ask: data.ask[0],
          volume: data.vol,
          change24h: ((data.close - data.open) / data.open) * 100,
          timestamp: data.ts
        }
        this.emit('price_update', priceData)
      }
    }
  }

  // 处理价格更新
  private async handlePriceUpdate(priceData: RealTimePrice): Promise<void> {
    // 缓存到 Redis
    const cacheKey = `price:${priceData.exchange}:${priceData.symbol}`
    await this.redisService.set(cacheKey, priceData, 60) // 1分钟缓存

    // 广播到 WebSocket 客户端
    this.wsManager.broadcast('market-data', {
      type: 'price_update',
      data: priceData
    })
  }

  // 处理订单簿更新
  private async handleOrderBookUpdate(orderBookData: RealTimeOrderBook): Promise<void> {
    const cacheKey = `orderbook:${orderBookData.exchange}:${orderBookData.symbol}`
    await this.redisService.set(cacheKey, orderBookData, 30) // 30秒缓存

    this.wsManager.broadcast('market-data', {
      type: 'orderbook_update',
      data: orderBookData
    })
  }

  // 处理交易更新
  private async handleTradeUpdate(tradeData: RealTimeTrade): Promise<void> {
    this.wsManager.broadcast('market-data', {
      type: 'trade_update',
      data: tradeData
    })
  }

  // 重连处理
  private handleReconnect(exchange: string): void {
    const attempts = this.reconnectAttempts.get(exchange) || 0
    
    if (attempts < this.maxReconnectAttempts) {
      this.reconnectAttempts.set(exchange, attempts + 1)
      
      setTimeout(() => {
        console.log(`🔄 Attempting to reconnect to ${exchange} (attempt ${attempts + 1})`)
        this.connectToExchange(exchange)
      }, this.reconnectDelay * Math.pow(2, attempts)) // 指数退避
    } else {
      console.error(`❌ Max reconnection attempts reached for ${exchange}`)
    }
  }

  // 停止服务
  async stop(): Promise<void> {
    console.log('🛑 Stopping Real-Time Market Data Service...')
    
    for (const [exchange, ws] of this.wsConnections) {
      ws.close()
      console.log(`❌ Closed connection to ${exchange}`)
    }
    
    this.wsConnections.clear()
    this.subscriptions.clear()
    this.reconnectAttempts.clear()
    
    console.log('✅ Real-Time Market Data Service stopped')
  }

  // 获取实时价格
  async getRealTimePrice(exchange: string, symbol: string): Promise<RealTimePrice | null> {
    const cacheKey = `price:${exchange}:${symbol}`
    return await this.redisService.get<RealTimePrice>(cacheKey)
  }

  // 获取实时订单簿
  async getRealTimeOrderBook(exchange: string, symbol: string): Promise<RealTimeOrderBook | null> {
    const cacheKey = `orderbook:${exchange}:${symbol}`
    return await this.redisService.get<RealTimeOrderBook>(cacheKey)
  }
}
