import { RedisService } from './RedisService'
import { ExchangeService, MarketData } from './ExchangeService'

export interface PriceAlert {
  id: string
  symbol: string
  condition: 'above' | 'below'
  targetPrice: number
  currentPrice: number
  userId: string
  triggered: boolean
  createdAt: Date
}

export interface MarketDataSubscription {
  symbol: string
  callback: (data: MarketData) => void
  interval: number
  lastUpdate: number
}

export class MarketDataService {
  private subscriptions: Map<string, MarketDataSubscription> = new Map()
  private priceAlerts: Map<string, PriceAlert> = new Map()
  private updateIntervals: Map<string, NodeJS.Timeout> = new Map()
  private isRunning = false

  constructor(
    private redisService: RedisService,
    private exchangeService: ExchangeService
  ) {}

  async initialize(): Promise<void> {
    this.isRunning = true
    console.log('✅ Market Data Service initialized')
  }

  async shutdown(): Promise<void> {
    this.isRunning = false
    
    // 清理所有定时器
    for (const interval of this.updateIntervals.values()) {
      clearInterval(interval)
    }
    this.updateIntervals.clear()
    
    // 清理订阅
    this.subscriptions.clear()
    this.priceAlerts.clear()
    
    console.log('🛑 Market Data Service shutdown')
  }

  // 获取实时市场数据
  async getMarketData(symbol: string, exchangeId = 'binance'): Promise<MarketData | null> {
    // 先尝试从缓存获取
    const cacheKey = `market_data:${exchangeId}:${symbol}`
    const cached = await this.redisService.get<MarketData>(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < 5000) { // 5秒内的数据认为是新鲜的
      return cached
    }

    // 从交易所获取最新数据
    const marketData = await this.exchangeService.getMarketData(exchangeId, symbol)
    if (marketData) {
      // 缓存数据，TTL 10秒
      await this.redisService.set(cacheKey, marketData, 10)
    }

    return marketData
  }

  // 获取多个交易所的价格对比
  async getPriceComparison(symbol: string): Promise<Record<string, MarketData | null>> {
    const cacheKey = `price_comparison:${symbol}`
    const cached = await this.redisService.get<Record<string, MarketData | null>>(cacheKey)
    
    if (cached) {
      // 检查缓存数据是否还新鲜（30秒内）
      const isDataFresh = Object.values(cached).some(data => 
        data && Date.now() - data.timestamp < 30000
      )
      if (isDataFresh) {
        return cached
      }
    }

    // 获取新数据
    const comparison = await this.exchangeService.comparePrice(symbol)
    
    // 缓存结果，TTL 30秒
    await this.redisService.set(cacheKey, comparison, 30)
    
    return comparison
  }

  // 获取K线数据
  async getKlineData(
    symbol: string, 
    timeframe = '1h', 
    limit = 100, 
    exchangeId = 'binance'
  ): Promise<any[] | null> {
    const cacheKey = `kline:${exchangeId}:${symbol}:${timeframe}:${limit}`
    const cached = await this.redisService.get<any[]>(cacheKey)
    
    if (cached) {
      return cached
    }

    const klineData = await this.exchangeService.getKlineData(exchangeId, symbol, timeframe, limit)
    if (klineData) {
      // K线数据缓存时间较长，根据时间框架决定
      const ttl = this.getKlineCacheTTL(timeframe)
      await this.redisService.set(cacheKey, klineData, ttl)
    }

    return klineData
  }

  private getKlineCacheTTL(timeframe: string): number {
    const ttlMap: Record<string, number> = {
      '1m': 30,      // 1分钟K线缓存30秒
      '5m': 60,      // 5分钟K线缓存1分钟
      '15m': 180,    // 15分钟K线缓存3分钟
      '1h': 600,     // 1小时K线缓存10分钟
      '4h': 1800,    // 4小时K线缓存30分钟
      '1d': 3600,    // 日K线缓存1小时
    }
    return ttlMap[timeframe] || 300
  }

  // 获取订单簿
  async getOrderBook(symbol: string, limit = 20, exchangeId = 'binance'): Promise<any | null> {
    const cacheKey = `orderbook:${exchangeId}:${symbol}:${limit}`
    const cached = await this.redisService.get(cacheKey)
    
    if (cached) {
      return cached
    }

    const orderBook = await this.exchangeService.getOrderBook(exchangeId, symbol, limit)
    if (orderBook) {
      // 订单簿数据缓存时间很短
      await this.redisService.set(cacheKey, orderBook, 5)
    }

    return orderBook
  }

  // 订阅实时价格更新
  async subscribeToSymbol(symbol: string, callback: (data: MarketData) => void, interval = 1000): Promise<void> {
    const subscriptionKey = `${symbol}_${Date.now()}`
    
    const subscription: MarketDataSubscription = {
      symbol,
      callback,
      interval,
      lastUpdate: 0
    }

    this.subscriptions.set(subscriptionKey, subscription)

    // 创建定时更新
    const updateInterval = setInterval(async () => {
      if (!this.isRunning) {
        clearInterval(updateInterval)
        return
      }

      try {
        const marketData = await this.getMarketData(symbol)
        if (marketData && marketData.timestamp > subscription.lastUpdate) {
          subscription.lastUpdate = marketData.timestamp
          callback(marketData)
        }
      } catch (error) {
        console.error(`Error updating market data for ${symbol}:`, error)
      }
    }, interval)

    this.updateIntervals.set(subscriptionKey, updateInterval)

    console.log(`📊 Subscribed to ${symbol} with interval ${interval}ms`)
  }

  // 取消订阅
  async unsubscribeFromSymbol(symbol: string): Promise<void> {
    const keysToRemove: string[] = []
    
    for (const [key, subscription] of this.subscriptions) {
      if (subscription.symbol === symbol) {
        keysToRemove.push(key)
      }
    }

    for (const key of keysToRemove) {
      this.subscriptions.delete(key)
      const interval = this.updateIntervals.get(key)
      if (interval) {
        clearInterval(interval)
        this.updateIntervals.delete(key)
      }
    }

    console.log(`📊 Unsubscribed from ${symbol}`)
  }

  // 创建价格预警
  async createPriceAlert(
    userId: string,
    symbol: string,
    condition: 'above' | 'below',
    targetPrice: number
  ): Promise<string> {
    const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const currentData = await this.getMarketData(symbol)
    const currentPrice = currentData?.price || 0

    const alert: PriceAlert = {
      id: alertId,
      symbol,
      condition,
      targetPrice,
      currentPrice,
      userId,
      triggered: false,
      createdAt: new Date()
    }

    this.priceAlerts.set(alertId, alert)

    // 保存到Redis
    await this.redisService.hset(`price_alerts:${userId}`, alertId, alert)

    console.log(`🚨 Created price alert: ${symbol} ${condition} ${targetPrice}`)
    return alertId
  }

  // 检查价格预警
  private async checkPriceAlerts(): Promise<void> {
    for (const [alertId, alert] of this.priceAlerts) {
      if (alert.triggered) continue

      try {
        const currentData = await this.getMarketData(alert.symbol)
        if (!currentData) continue

        const shouldTrigger = 
          (alert.condition === 'above' && currentData.price >= alert.targetPrice) ||
          (alert.condition === 'below' && currentData.price <= alert.targetPrice)

        if (shouldTrigger) {
          alert.triggered = true
          alert.currentPrice = currentData.price

          // 发送通知
          await this.triggerPriceAlert(alert)

          // 更新Redis
          await this.redisService.hset(`price_alerts:${alert.userId}`, alertId, alert)

          console.log(`🚨 Price alert triggered: ${alert.symbol} ${alert.condition} ${alert.targetPrice}`)
        }
      } catch (error) {
        console.error(`Error checking price alert ${alertId}:`, error)
      }
    }
  }

  private async triggerPriceAlert(alert: PriceAlert): Promise<void> {
    // 发布价格预警事件
    await this.redisService.publish('price_alerts', {
      type: 'price_alert_triggered',
      alert,
      timestamp: Date.now()
    })

    // 这里可以添加其他通知方式，如邮件、短信等
  }

  // 获取用户的价格预警
  async getUserPriceAlerts(userId: string): Promise<PriceAlert[]> {
    const alerts = await this.redisService.hgetall<PriceAlert>(`price_alerts:${userId}`)
    return Object.values(alerts)
  }

  // 删除价格预警
  async deletePriceAlert(userId: string, alertId: string): Promise<boolean> {
    this.priceAlerts.delete(alertId)
    const result = await this.redisService.hdel(`price_alerts:${userId}`, alertId)
    return result > 0
  }

  // 获取热门交易对
  async getPopularSymbols(limit = 10): Promise<string[]> {
    const cacheKey = 'popular_symbols'
    const cached = await this.redisService.get<string[]>(cacheKey)
    
    if (cached) {
      return cached.slice(0, limit)
    }

    // 默认热门交易对
    const defaultSymbols = [
      'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT',
      'XRP/USDT', 'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'MATIC/USDT'
    ]

    // 缓存1小时
    await this.redisService.set(cacheKey, defaultSymbols, 3600)
    
    return defaultSymbols.slice(0, limit)
  }

  // 获取市场统计
  async getMarketStats(): Promise<any> {
    const cacheKey = 'market_stats'
    const cached = await this.redisService.get(cacheKey)
    
    if (cached) {
      return cached
    }

    const stats = {
      totalSymbols: this.subscriptions.size,
      activeAlerts: this.priceAlerts.size,
      lastUpdate: Date.now()
    }

    // 缓存5分钟
    await this.redisService.set(cacheKey, stats, 300)
    
    return stats
  }

  // 启动价格预警检查
  startPriceAlertMonitoring(interval = 5000): void {
    setInterval(() => {
      if (this.isRunning) {
        this.checkPriceAlerts()
      }
    }, interval)
    
    console.log(`🚨 Price alert monitoring started with ${interval}ms interval`)
  }
}
