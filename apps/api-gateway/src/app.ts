import Fastify, { FastifyInstance, FastifyServerOptions } from 'fastify'
import cors from '@fastify/cors'
import helmet from '@fastify/helmet'
import swagger from '@fastify/swagger'
import swaggerUi from '@fastify/swagger-ui'
import jwt from '@fastify/jwt'
import websocket from '@fastify/websocket'

import { config } from './config'
import { setupRoutes } from './routes'
import { OptimizedWebSocketManager } from './websocket/optimized-websocket'
import { DatabaseService } from './services/DatabaseService'
import { RedisService } from './services/RedisService'
import { ExchangeService } from './services/ExchangeService'
import { MarketDataService } from './services/MarketDataService'
import { StrategyService } from './services/StrategyService'
import { PriceMonitorService } from './services/PriceMonitorService'
import { PerformanceTrackingService } from './services/PerformanceTrackingService'
import { RealTimeMarketDataService } from './services/RealTimeMarketDataService'
import { StrategyExecutionEngine } from './services/StrategyExecutionEngine'
import { StrategyManager } from './strategies/StrategyManager'
import tradingPerformancePlugin from './plugins/trading-performance-plugin'
import { applyRateLimitConfig } from './config/rate-limit-config'
import authPlugin from './middleware/auth'

export async function build(opts: FastifyServerOptions = {}): Promise<FastifyInstance> {
  const fastify = Fastify({
    logger: opts.logger !== false ? {
      level: config.LOG_LEVEL,
      ...(config.NODE_ENV === 'development' && {
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'HH:MM:ss Z',
            ignore: 'pid,hostname'
          }
        }
      })
    } : false,
    ...opts
  })

  // 注册核心插件
  await registerCorePlugins(fastify)

  // 注册自定义插件
  await registerCustomPlugins(fastify)

  // 初始化服务
  await initializeServices(fastify)

  // 应用速率限制配置
  await applyRateLimitConfig(fastify)

  // 设置路由
  await setupRoutes(fastify)

  // 设置优化的 WebSocket
  await setupOptimizedWebSocket(fastify)

  return fastify
}

// 注册核心插件
async function registerCorePlugins(fastify: FastifyInstance) {
  // 安全插件
  await fastify.register(helmet, {
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false
  })

  // CORS 配置
  await fastify.register(cors, {
    origin: config.CORS_ORIGINS,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  })

  // JWT 认证
  await fastify.register(jwt, {
    secret: config.JWT_SECRET,
    sign: {
      expiresIn: config.JWT_EXPIRES_IN
    }
  })

  // WebSocket 支持
  await fastify.register(websocket, {
    options: {
      maxPayload: 1048576, // 1MB
      verifyClient: (info, next) => {
        // 基础的客户端验证
        const origin = info.origin
        if (config.NODE_ENV === 'production' && !config.CORS_ORIGINS.includes(origin)) {
          return next(false)
        }
        next(true)
      }
    }
  })

  // Swagger 文档 (仅开发环境)
  if (config.NODE_ENV === 'development') {
    await fastify.register(swagger, {
      swagger: {
        info: {
          title: 'SFQuant API Gateway',
          description: 'Cryptocurrency Quantitative Strategy Management System API',
          version: '1.0.0'
        },
        host: `localhost:${config.PORT}`,
        schemes: ['http', 'https'],
        consumes: ['application/json'],
        produces: ['application/json'],
        securityDefinitions: {
          Bearer: {
            type: 'apiKey',
            name: 'Authorization',
            in: 'header',
            description: 'JWT token for authentication'
          }
        },
        tags: [
          { name: 'Health', description: 'Health check endpoints' },
          { name: 'Auth', description: 'Authentication endpoints' },
          { name: 'Market Data', description: 'Market data endpoints' },
          { name: 'Strategies', description: 'Strategy management endpoints' },
          { name: 'Trading', description: 'Trading execution endpoints' },
          { name: 'Advanced', description: 'Advanced features endpoints' }
        ]
      }
    })

    await fastify.register(swaggerUi, {
      routePrefix: '/docs',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: false
      },
      staticCSP: true,
      transformStaticCSP: (header) => header
    })
  }
}

// 注册自定义插件
async function registerCustomPlugins(fastify: FastifyInstance) {
  // 认证插件
  await fastify.register(authPlugin)

  // 性能监控插件
  await fastify.register(tradingPerformancePlugin, {
    enableMetrics: true,
    enableTracing: true,
    metricsInterval: 10000,
    alertThresholds: {
      responseTime: 1000,
      errorRate: 0.05,
      memoryUsage: 512 * 1024 * 1024
    }
  })
}

// 初始化服务
async function initializeServices(fastify: FastifyInstance): Promise<void> {
  try {
    // 数据库服务
    const databaseService = new DatabaseService()
    await databaseService.initialize()
    fastify.decorate('database', databaseService)

    // Redis服务
    const redisService = new RedisService()
    await redisService.initialize()
    fastify.decorate('redis', redisService)

    // 交易所服务
    const exchangeService = new ExchangeService()
    fastify.decorate('exchangeService', exchangeService)

    // 策略服务
    const strategyService = new StrategyService(databaseService, redisService)
    fastify.decorate('strategyService', strategyService)

    // 市场数据服务
    const marketDataService = new MarketDataService(redisService, exchangeService)
    await marketDataService.initialize()
    fastify.decorate('marketDataService', marketDataService)

    // 价格监控服务
    const priceMonitorService = new PriceMonitorService(redisService)
    fastify.decorate('priceMonitorService', priceMonitorService)

    // 性能追踪服务
    const performanceService = new PerformanceTrackingService(redisService)
    fastify.decorate('performanceService', performanceService)

    // 实时市场数据服务
    const realTimeMarketDataService = new RealTimeMarketDataService(
      exchangeService,
      redisService,
      null as any // wsManager 将在后面设置
    )
    fastify.decorate('realTimeMarketDataService', realTimeMarketDataService)

    // 策略执行引擎
    const strategyExecutionEngine = new StrategyExecutionEngine(
      strategyService,
      exchangeService,
      realTimeMarketDataService,
      redisService,
      null as any // wsManager 将在后面设置
    )
    fastify.decorate('strategyExecutionEngine', strategyExecutionEngine)

    // 策略管理器
    const strategyManager = new StrategyManager(
      exchangeService,
      realTimeMarketDataService,
      strategyService,
      redisService
    )
    fastify.decorate('strategyManager', strategyManager)

    fastify.log.info('All services initialized successfully')
  } catch (error) {
    fastify.log.error('Failed to initialize services:', error)
    throw error
  }
}

// 设置优化的 WebSocket
async function setupOptimizedWebSocket(fastify: FastifyInstance) {
  const wsManager = new OptimizedWebSocketManager(fastify)
  await wsManager.registerRoutes()

  // 装饰 Fastify 实例
  fastify.decorate('wsManager', wsManager)

  // 更新服务中的 wsManager 引用
  if (fastify.realTimeMarketDataService) {
    (fastify.realTimeMarketDataService as any).wsManager = wsManager
  }
  if (fastify.strategyExecutionEngine) {
    (fastify.strategyExecutionEngine as any).wsManager = wsManager
  }

  // 添加 WebSocket 统计路由
  fastify.get('/api/v1/websocket/stats', {
    schema: {
      description: 'Get WebSocket connection statistics',
      tags: ['monitoring'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' }
          }
        }
      }
    }
  }, async (request, reply) => {
    return {
      success: true,
      data: wsManager.getStats()
    }
  })
}

// 全局错误处理
export function setupErrorHandling(fastify: FastifyInstance) {
  fastify.setErrorHandler((error, request, reply) => {
    fastify.log.error(error)

    // 验证错误
    if (error.validation) {
      reply.status(400).send({
        statusCode: 400,
        error: 'Validation Error',
        message: error.message,
        details: error.validation
      })
      return
    }

    // JWT 错误
    if (error.code === 'FST_JWT_AUTHORIZATION_TOKEN_EXPIRED') {
      reply.status(401).send({
        statusCode: 401,
        error: 'Token Expired',
        message: 'JWT token has expired'
      })
      return
    }

    if (error.code === 'FST_JWT_NO_AUTHORIZATION_IN_HEADER') {
      reply.status(401).send({
        statusCode: 401,
        error: 'No Authorization',
        message: 'No authorization header found'
      })
      return
    }

    // 速率限制错误
    if (error.statusCode === 429) {
      reply.status(429).send({
        statusCode: 429,
        error: 'Too Many Requests',
        message: error.message || 'Rate limit exceeded'
      })
      return
    }

    // 其他已知错误
    if (error.statusCode) {
      reply.status(error.statusCode).send({
        statusCode: error.statusCode,
        error: error.name,
        message: error.message
      })
      return
    }

    // 未知错误
    reply.status(500).send({
      statusCode: 500,
      error: 'Internal Server Error',
      message: config.NODE_ENV === 'production' ? 'Something went wrong' : error.message
    })
  })

  // 404 处理
  fastify.setNotFoundHandler(async (request, reply) => {
    reply.status(404).send({
      statusCode: 404,
      error: 'Not Found',
      message: `Route ${request.method} ${request.url} not found`,
      timestamp: new Date().toISOString()
    })
  })
}

// 优雅关闭处理
export function setupGracefulShutdown(fastify: FastifyInstance) {
  const gracefulShutdown = async (signal: string) => {
    fastify.log.info(`Received ${signal}, shutting down gracefully...`)

    try {
      // 停止策略执行引擎
      if (fastify.strategyExecutionEngine) {
        await fastify.strategyExecutionEngine.stop()
      }

      // 停止实时市场数据服务
      if (fastify.realTimeMarketDataService) {
        await fastify.realTimeMarketDataService.stop()
      }

      // 清理交易所连接
      if (fastify.exchangeService) {
        await fastify.exchangeService.cleanup()
      }

      // 清理 WebSocket 连接
      if (fastify.wsManager) {
        await fastify.wsManager.cleanup()
      }

      // 关闭 Fastify 服务器
      await fastify.close()
      fastify.log.info('Server closed successfully')
      process.exit(0)
    } catch (error) {
      fastify.log.error('Error during shutdown:', error)
      process.exit(1)
    }
  }

  process.on('SIGINT', () => gracefulShutdown('SIGINT'))
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
}

// 声明类型扩展
declare module 'fastify' {
  interface FastifyInstance {
    database: DatabaseService
    redis: RedisService
    exchangeService: ExchangeService
    marketDataService: MarketDataService
    strategyService: StrategyService
    priceMonitorService: PriceMonitorService
    performanceService: PerformanceTrackingService
    realTimeMarketDataService: RealTimeMarketDataService
    strategyExecutionEngine: StrategyExecutionEngine
    strategyManager: StrategyManager
    wsManager: OptimizedWebSocketManager
  }
}
