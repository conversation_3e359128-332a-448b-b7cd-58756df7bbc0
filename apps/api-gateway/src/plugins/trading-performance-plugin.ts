import { FastifyInstance, FastifyPluginOptions } from 'fastify'
import fp from 'fastify-plugin'

export interface TradingPerformanceOptions {
  enableMetrics?: boolean
  enableTracing?: boolean
  metricsInterval?: number
  alertThresholds?: {
    responseTime?: number
    errorRate?: number
    memoryUsage?: number
  }
}

export interface PerformanceMetrics {
  requestCount: number
  errorCount: number
  totalResponseTime: number
  averageResponseTime: number
  errorRate: number
  memoryUsage: number
  cpuUsage: number
  activeConnections: number
  lastUpdated: number
}

class TradingPerformanceMonitor {
  private metrics: PerformanceMetrics = {
    requestCount: 0,
    errorCount: 0,
    totalResponseTime: 0,
    averageResponseTime: 0,
    errorRate: 0,
    memoryUsage: 0,
    cpuUsage: 0,
    activeConnections: 0,
    lastUpdated: Date.now()
  }

  private options: TradingPerformanceOptions
  private fastify: FastifyInstance
  private metricsInterval: NodeJS.Timeout | null = null

  constructor(fastify: FastifyInstance, options: TradingPerformanceOptions) {
    this.fastify = fastify
    this.options = {
      enableMetrics: true,
      enableTracing: false,
      metricsInterval: 10000,
      alertThresholds: {
        responseTime: 1000,
        errorRate: 0.05,
        memoryUsage: 512 * 1024 * 1024
      },
      ...options
    }
  }

  start(): void {
    if (this.options.enableMetrics) {
      this.startMetricsCollection()
    }

    if (this.options.enableTracing) {
      this.setupTracing()
    }

    this.setupHooks()
  }

  private startMetricsCollection(): void {
    this.metricsInterval = setInterval(() => {
      this.updateSystemMetrics()
      this.checkAlerts()
    }, this.options.metricsInterval)
  }

  private updateSystemMetrics(): void {
    const memUsage = process.memoryUsage()
    this.metrics.memoryUsage = memUsage.heapUsed
    this.metrics.lastUpdated = Date.now()

    // 计算平均响应时间
    if (this.metrics.requestCount > 0) {
      this.metrics.averageResponseTime = this.metrics.totalResponseTime / this.metrics.requestCount
      this.metrics.errorRate = this.metrics.errorCount / this.metrics.requestCount
    }

    this.fastify.log.debug('Performance metrics updated', this.metrics)
  }

  private checkAlerts(): void {
    const { alertThresholds } = this.options

    if (alertThresholds?.responseTime && this.metrics.averageResponseTime > alertThresholds.responseTime) {
      this.fastify.log.warn(`High response time detected: ${this.metrics.averageResponseTime}ms`)
    }

    if (alertThresholds?.errorRate && this.metrics.errorRate > alertThresholds.errorRate) {
      this.fastify.log.warn(`High error rate detected: ${(this.metrics.errorRate * 100).toFixed(2)}%`)
    }

    if (alertThresholds?.memoryUsage && this.metrics.memoryUsage > alertThresholds.memoryUsage) {
      this.fastify.log.warn(`High memory usage detected: ${(this.metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`)
    }
  }

  private setupTracing(): void {
    // 简单的请求追踪
    this.fastify.addHook('onRequest', async (request) => {
      request.startTime = Date.now()
      request.traceId = this.generateTraceId()
      
      if (this.options.enableTracing) {
        this.fastify.log.info(`[TRACE:${request.traceId}] ${request.method} ${request.url} - Started`)
      }
    })

    this.fastify.addHook('onResponse', async (request, reply) => {
      if (request.startTime && this.options.enableTracing) {
        const duration = Date.now() - request.startTime
        this.fastify.log.info(`[TRACE:${request.traceId}] ${request.method} ${request.url} - Completed in ${duration}ms (${reply.statusCode})`)
      }
    })
  }

  private setupHooks(): void {
    // 请求开始时记录
    this.fastify.addHook('onRequest', async (request) => {
      request.startTime = Date.now()
      this.metrics.requestCount++
      this.metrics.activeConnections++
    })

    // 请求完成时记录
    this.fastify.addHook('onResponse', async (request, reply) => {
      if (request.startTime) {
        const responseTime = Date.now() - request.startTime
        this.metrics.totalResponseTime += responseTime
        this.metrics.activeConnections--

        // 记录错误
        if (reply.statusCode >= 400) {
          this.metrics.errorCount++
        }
      }
    })

    // 错误处理
    this.fastify.addHook('onError', async (request, reply, error) => {
      this.metrics.errorCount++
      this.fastify.log.error(`Request error: ${error.message}`, {
        method: request.method,
        url: request.url,
        statusCode: reply.statusCode,
        traceId: request.traceId
      })
    })
  }

  private generateTraceId(): string {
    return `trace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  resetMetrics(): void {
    this.metrics = {
      requestCount: 0,
      errorCount: 0,
      totalResponseTime: 0,
      averageResponseTime: 0,
      errorRate: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      activeConnections: 0,
      lastUpdated: Date.now()
    }
  }

  stop(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval)
      this.metricsInterval = null
    }
  }
}

async function tradingPerformancePlugin(
  fastify: FastifyInstance,
  options: TradingPerformanceOptions
): Promise<void> {
  const monitor = new TradingPerformanceMonitor(fastify, options)
  
  // 启动监控
  monitor.start()

  // 装饰 Fastify 实例
  fastify.decorate('performanceMonitor', monitor)

  // 添加性能指标路由
  fastify.get('/api/v1/metrics/performance', {
    schema: {
      description: 'Get performance metrics',
      tags: ['monitoring'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                requestCount: { type: 'number' },
                errorCount: { type: 'number' },
                averageResponseTime: { type: 'number' },
                errorRate: { type: 'number' },
                memoryUsage: { type: 'number' },
                activeConnections: { type: 'number' },
                lastUpdated: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    return {
      success: true,
      data: monitor.getMetrics()
    }
  })

  // 添加重置指标路由
  fastify.post('/api/v1/metrics/reset', {
    schema: {
      description: 'Reset performance metrics',
      tags: ['monitoring'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    monitor.resetMetrics()
    return {
      success: true,
      message: 'Performance metrics reset successfully'
    }
  })

  // 添加健康检查路由
  fastify.get('/api/v1/metrics/health', {
    schema: {
      description: 'Get system health status',
      tags: ['monitoring'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                status: { type: 'string' },
                uptime: { type: 'number' },
                memory: { type: 'object' },
                timestamp: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const metrics = monitor.getMetrics()
    const memUsage = process.memoryUsage()
    
    let status = 'healthy'
    if (metrics.errorRate > 0.1) status = 'degraded'
    if (metrics.averageResponseTime > 2000) status = 'degraded'
    if (memUsage.heapUsed > 1024 * 1024 * 1024) status = 'critical' // 1GB

    return {
      success: true,
      data: {
        status,
        uptime: process.uptime(),
        memory: {
          used: memUsage.heapUsed,
          total: memUsage.heapTotal,
          external: memUsage.external,
          rss: memUsage.rss
        },
        performance: metrics,
        timestamp: Date.now()
      }
    }
  })

  // 优雅关闭
  fastify.addHook('onClose', async () => {
    monitor.stop()
  })

  fastify.log.info('Trading performance plugin registered successfully')
}

// 扩展 Fastify 类型
declare module 'fastify' {
  interface FastifyInstance {
    performanceMonitor: TradingPerformanceMonitor
  }
  
  interface FastifyRequest {
    startTime?: number
    traceId?: string
  }
}

export default fp(tradingPerformancePlugin, {
  name: 'trading-performance-plugin',
  fastify: '4.x'
})
