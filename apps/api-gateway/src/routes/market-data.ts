import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { z } from 'zod'

// 请求参数验证schemas
const symbolParamSchema = z.object({
  symbol: z.string().min(1, 'Symbol is required')
})

const exchangeQuerySchema = z.object({
  exchange: z.string().optional().default('binance')
})

const klineQuerySchema = z.object({
  exchange: z.string().optional().default('binance'),
  timeframe: z.string().optional().default('1h'),
  limit: z.string().transform(Number).optional().default('100')
})

const orderbookQuerySchema = z.object({
  exchange: z.string().optional().default('binance'),
  limit: z.string().transform(Number).optional().default('20')
})

const priceAlertSchema = z.object({
  symbol: z.string().min(1, 'Symbol is required'),
  condition: z.enum(['above', 'below'], { required_error: 'Condition must be above or below' }),
  targetPrice: z.number().positive('Target price must be positive')
})

export default async function marketDataRoutes(fastify: FastifyInstance) {
  // 获取单个交易对价格
  fastify.get('/price/:symbol', {
    schema: {
      description: 'Get current price for a trading pair',
      tags: ['Market Data'],
      params: {
        type: 'object',
        required: ['symbol'],
        properties: {
          symbol: { type: 'string', description: 'Trading pair symbol (e.g., BTC/USDT)' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          exchange: { type: 'string', default: 'binance', description: 'Exchange name' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                symbol: { type: 'string' },
                price: { type: 'number' },
                bid: { type: 'number' },
                ask: { type: 'number' },
                volume: { type: 'number' },
                timestamp: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { symbol } = symbolParamSchema.parse(request.params)
      const { exchange } = exchangeQuerySchema.parse(request.query)

      const marketData = await fastify.marketDataService.getMarketData(symbol, exchange)
      
      if (!marketData) {
        return reply.status(404).send({
          success: false,
          error: 'Market data not found',
          message: `No data available for ${symbol} on ${exchange}`
        })
      }

      reply.send({
        success: true,
        data: marketData
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(e => e.message)
        })
      }
      
      fastify.log.error('Get price error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取价格对比
  fastify.get('/price-comparison/:symbol', {
    schema: {
      description: 'Compare prices across multiple exchanges',
      tags: ['Market Data'],
      params: {
        type: 'object',
        required: ['symbol'],
        properties: {
          symbol: { type: 'string', description: 'Trading pair symbol' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              additionalProperties: {
                type: 'object',
                properties: {
                  symbol: { type: 'string' },
                  price: { type: 'number' },
                  bid: { type: 'number' },
                  ask: { type: 'number' },
                  volume: { type: 'number' },
                  timestamp: { type: 'number' }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { symbol } = symbolParamSchema.parse(request.params)

      const comparison = await fastify.marketDataService.getPriceComparison(symbol)

      reply.send({
        success: true,
        data: comparison
      })
    } catch (error) {
      fastify.log.error('Get price comparison error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取K线数据
  fastify.get('/kline/:symbol', {
    schema: {
      description: 'Get candlestick/kline data for a trading pair',
      tags: ['Market Data'],
      params: {
        type: 'object',
        required: ['symbol'],
        properties: {
          symbol: { type: 'string', description: 'Trading pair symbol' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          exchange: { type: 'string', default: 'binance' },
          timeframe: { type: 'string', default: '1h', description: 'Timeframe (1m, 5m, 15m, 1h, 4h, 1d)' },
          limit: { type: 'integer', default: 100, minimum: 1, maximum: 1000 }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { symbol } = symbolParamSchema.parse(request.params)
      const { exchange, timeframe, limit } = klineQuerySchema.parse(request.query)

      const klineData = await fastify.marketDataService.getKlineData(symbol, timeframe, limit, exchange)
      
      if (!klineData) {
        return reply.status(404).send({
          success: false,
          error: 'Kline data not found'
        })
      }

      reply.send({
        success: true,
        data: klineData
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(e => e.message)
        })
      }
      
      fastify.log.error('Get kline error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取订单簿
  fastify.get('/orderbook/:symbol', {
    schema: {
      description: 'Get order book for a trading pair',
      tags: ['Market Data'],
      params: {
        type: 'object',
        required: ['symbol'],
        properties: {
          symbol: { type: 'string', description: 'Trading pair symbol' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          exchange: { type: 'string', default: 'binance' },
          limit: { type: 'integer', default: 20, minimum: 5, maximum: 100 }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { symbol } = symbolParamSchema.parse(request.params)
      const { exchange, limit } = orderbookQuerySchema.parse(request.query)

      const orderBook = await fastify.marketDataService.getOrderBook(symbol, limit, exchange)
      
      if (!orderBook) {
        return reply.status(404).send({
          success: false,
          error: 'Order book not found'
        })
      }

      reply.send({
        success: true,
        data: orderBook
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(e => e.message)
        })
      }
      
      fastify.log.error('Get orderbook error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取热门交易对
  fastify.get('/popular', {
    schema: {
      description: 'Get popular trading pairs',
      tags: ['Market Data'],
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', default: 10, minimum: 1, maximum: 50 }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = request.query as { limit?: number }
      const limit = query.limit || 10

      const popularSymbols = await fastify.marketDataService.getPopularSymbols(limit)

      reply.send({
        success: true,
        data: popularSymbols
      })
    } catch (error) {
      fastify.log.error('Get popular symbols error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 创建价格预警 (需要认证)
  fastify.post('/alerts', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Create a price alert',
      tags: ['Market Data'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['symbol', 'condition', 'targetPrice'],
        properties: {
          symbol: { type: 'string' },
          condition: { type: 'string', enum: ['above', 'below'] },
          targetPrice: { type: 'number', minimum: 0 }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id
      const validatedData = priceAlertSchema.parse(request.body)

      const alertId = await fastify.marketDataService.createPriceAlert(
        userId,
        validatedData.symbol,
        validatedData.condition,
        validatedData.targetPrice
      )

      reply.status(201).send({
        success: true,
        message: 'Price alert created successfully',
        data: { alertId }
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(e => e.message)
        })
      }
      
      fastify.log.error('Create price alert error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取用户的价格预警 (需要认证)
  fastify.get('/alerts', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get user price alerts',
      tags: ['Market Data'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id

      const alerts = await fastify.marketDataService.getUserPriceAlerts(userId)

      reply.send({
        success: true,
        data: alerts
      })
    } catch (error) {
      fastify.log.error('Get price alerts error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 删除价格预警 (需要认证)
  fastify.delete('/alerts/:alertId', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Delete a price alert',
      tags: ['Market Data'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['alertId'],
        properties: {
          alertId: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id
      const { alertId } = request.params as { alertId: string }

      const deleted = await fastify.marketDataService.deletePriceAlert(userId, alertId)
      
      if (!deleted) {
        return reply.status(404).send({
          success: false,
          error: 'Price alert not found'
        })
      }

      reply.send({
        success: true,
        message: 'Price alert deleted successfully'
      })
    } catch (error) {
      fastify.log.error('Delete price alert error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取市场统计
  fastify.get('/stats', {
    schema: {
      description: 'Get market statistics',
      tags: ['Market Data']
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const stats = await fastify.marketDataService.getMarketStats()

      reply.send({
        success: true,
        data: stats
      })
    } catch (error) {
      fastify.log.error('Get market stats error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取价格异常监控数据
  fastify.get('/anomalies', {
    schema: {
      description: 'Get recent price anomalies',
      tags: ['Market Data'],
      querystring: {
        type: 'object',
        properties: {
          symbol: { type: 'string', description: 'Filter by symbol' },
          limit: { type: 'integer', default: 50, minimum: 1, maximum: 200 }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = request.query as { symbol?: string; limit?: number }
      const limit = query.limit || 50

      let anomalies
      if (query.symbol) {
        anomalies = fastify.priceMonitorService.getSymbolAnomalies(query.symbol, limit)
      } else {
        anomalies = fastify.priceMonitorService.getRecentAnomalies(limit)
      }

      reply.send({
        success: true,
        data: anomalies
      })
    } catch (error) {
      fastify.log.error('Get anomalies error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })
}
