import { FastifyInstance } from 'fastify'

export async function strategyManagementRoutes(fastify: FastifyInstance) {
  // 创建套利策略
  fastify.post('/api/v1/strategy-manager/arbitrage', {
    schema: {
      description: 'Create a new arbitrage strategy',
      tags: ['Strategy Management'],
      body: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          symbol: { type: 'string' },
          exchanges: {
            type: 'array',
            items: { type: 'string' }
          },
          minProfitPercent: { type: 'number' },
          maxPositionSize: { type: 'number' },
          checkInterval: { type: 'number' }
        },
        required: ['name', 'symbol', 'exchanges', 'minProfitPercent']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                type: { type: 'string' },
                status: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { name, symbol, exchanges, minProfitPercent, maxPositionSize = 1000, checkInterval = 5000 } = request.body as any

    try {
      const id = `arb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      const config = {
        symbol,
        exchanges,
        minProfitPercent,
        maxPositionSize,
        checkInterval
      }

      const strategy = await fastify.strategyManager.createArbitrageStrategy(id, name, config)

      return {
        success: true,
        data: {
          id: strategy.id,
          name: strategy.name,
          type: strategy.type,
          status: strategy.status
        }
      }
    } catch (error) {
      fastify.log.error('Error creating arbitrage strategy:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to create arbitrage strategy'
      })
    }
  })

  // 创建网格交易策略
  fastify.post('/api/v1/strategy-manager/grid', {
    schema: {
      description: 'Create a new grid trading strategy',
      tags: ['Strategy Management'],
      body: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          symbol: { type: 'string' },
          exchange: { type: 'string' },
          basePrice: { type: 'number' },
          gridSpacing: { type: 'number' },
          gridLevels: { type: 'number' },
          orderAmount: { type: 'number' },
          upperLimit: { type: 'number' },
          lowerLimit: { type: 'number' }
        },
        required: ['name', 'symbol', 'exchange', 'basePrice', 'gridSpacing', 'gridLevels', 'orderAmount']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                type: { type: 'string' },
                status: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { name, symbol, exchange, basePrice, gridSpacing, gridLevels, orderAmount, upperLimit, lowerLimit } = request.body as any

    try {
      const id = `grid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      const config = {
        symbol,
        exchange,
        basePrice,
        gridSpacing,
        gridLevels,
        orderAmount,
        upperLimit,
        lowerLimit
      }

      const strategy = await fastify.strategyManager.createGridStrategy(id, name, config)

      return {
        success: true,
        data: {
          id: strategy.id,
          name: strategy.name,
          type: strategy.type,
          status: strategy.status
        }
      }
    } catch (error) {
      fastify.log.error('Error creating grid strategy:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to create grid strategy'
      })
    }
  })

  // 启动策略
  fastify.post('/api/v1/strategy-manager/:id/start', {
    schema: {
      description: 'Start a strategy',
      tags: ['Strategy Management'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { id } = request.params as { id: string }

    try {
      const success = await fastify.strategyManager.startStrategy(id)

      if (success) {
        return {
          success: true,
          message: `Strategy ${id} started successfully`
        }
      } else {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: `Failed to start strategy ${id}`
        })
      }
    } catch (error) {
      fastify.log.error('Error starting strategy:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to start strategy'
      })
    }
  })

  // 停止策略
  fastify.post('/api/v1/strategy-manager/:id/stop', {
    schema: {
      description: 'Stop a strategy',
      tags: ['Strategy Management'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { id } = request.params as { id: string }

    try {
      const success = await fastify.strategyManager.stopStrategy(id)

      if (success) {
        return {
          success: true,
          message: `Strategy ${id} stopped successfully`
        }
      } else {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: `Failed to stop strategy ${id}`
        })
      }
    } catch (error) {
      fastify.log.error('Error stopping strategy:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to stop strategy'
      })
    }
  })

  // 删除策略
  fastify.delete('/api/v1/strategy-manager/:id', {
    schema: {
      description: 'Delete a strategy',
      tags: ['Strategy Management'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { id } = request.params as { id: string }

    try {
      const success = await fastify.strategyManager.removeStrategy(id)

      if (success) {
        return {
          success: true,
          message: `Strategy ${id} deleted successfully`
        }
      } else {
        return reply.status(404).send({
          success: false,
          error: 'Not Found',
          message: `Strategy ${id} not found`
        })
      }
    } catch (error) {
      fastify.log.error('Error deleting strategy:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to delete strategy'
      })
    }
  })

  // 获取策略详情
  fastify.get('/api/v1/strategy-manager/:id', {
    schema: {
      description: 'Get strategy details',
      tags: ['Strategy Management'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { id } = request.params as { id: string }

    try {
      const stats = fastify.strategyManager.getStrategyStats(id)

      if (stats) {
        return {
          success: true,
          data: stats
        }
      } else {
        return reply.status(404).send({
          success: false,
          error: 'Not Found',
          message: `Strategy ${id} not found`
        })
      }
    } catch (error) {
      fastify.log.error('Error getting strategy details:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to get strategy details'
      })
    }
  })

  // 获取所有策略
  fastify.get('/api/v1/strategy-manager', {
    schema: {
      description: 'Get all strategies',
      tags: ['Strategy Management'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const stats = fastify.strategyManager.getAllStats()

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      fastify.log.error('Error getting all strategies:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to get strategies'
      })
    }
  })

  // 获取运行中的策略
  fastify.get('/api/v1/strategy-manager/running', {
    schema: {
      description: 'Get running strategies',
      tags: ['Strategy Management'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: { type: 'object' }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const runningStrategies = fastify.strategyManager.getRunningStrategies()

      return {
        success: true,
        data: runningStrategies.map(strategy => ({
          id: strategy.id,
          name: strategy.name,
          type: strategy.type,
          status: strategy.status,
          createdAt: strategy.createdAt,
          lastUpdate: strategy.lastUpdate
        }))
      }
    } catch (error) {
      fastify.log.error('Error getting running strategies:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to get running strategies'
      })
    }
  })
}
