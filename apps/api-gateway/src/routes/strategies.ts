import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { z } from 'zod'

// 策略配置验证schema
const strategyConfigSchema = z.object({
  symbol: z.string().min(1, 'Symbol is required'),
  exchange: z.string().min(1, 'Exchange is required'),
  capital: z.number().positive('Capital must be positive'),
  riskLevel: z.enum(['low', 'medium', 'high'], { required_error: 'Risk level is required' }),
  parameters: z.record(z.any()).optional().default({}),
  stopLoss: z.number().optional(),
  takeProfit: z.number().optional(),
  maxDrawdown: z.number().optional()
})

// 创建策略验证schema
const createStrategySchema = z.object({
  name: z.string().min(1, 'Strategy name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  type: z.enum(['arbitrage', 'trend', 'grid', 'dca', 'ai', 'custom'], { 
    required_error: 'Strategy type is required' 
  }),
  config: strategyConfigSchema
})

// 更新策略验证schema
const updateStrategySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  config: strategyConfigSchema.optional(),
  status: z.enum(['active', 'inactive', 'paused']).optional()
})

export default async function strategiesRoutes(fastify: FastifyInstance) {
  // 获取用户的所有策略 (需要认证)
  fastify.get('/', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get all user strategies',
      tags: ['Strategies'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['active', 'inactive', 'paused'] },
          type: { type: 'string', enum: ['arbitrage', 'trend', 'grid', 'dca', 'ai', 'custom'] },
          limit: { type: 'integer', default: 50, minimum: 1, maximum: 200 },
          offset: { type: 'integer', default: 0, minimum: 0 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  description: { type: 'string' },
                  type: { type: 'string' },
                  status: { type: 'string' },
                  config: { type: 'object' },
                  createdAt: { type: 'string' },
                  updatedAt: { type: 'string' },
                  lastExecuted: { type: 'string' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                total: { type: 'integer' },
                limit: { type: 'integer' },
                offset: { type: 'integer' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id
      const query = request.query as { 
        status?: string; 
        type?: string; 
        limit?: number; 
        offset?: number 
      }

      let strategies = await fastify.strategyService.getUserStrategies(userId)

      // 应用过滤器
      if (query.status) {
        strategies = strategies.filter(s => s.status === query.status)
      }
      if (query.type) {
        strategies = strategies.filter(s => s.type === query.type)
      }

      // 分页
      const limit = query.limit || 50
      const offset = query.offset || 0
      const total = strategies.length
      const paginatedStrategies = strategies.slice(offset, offset + limit)

      reply.send({
        success: true,
        data: paginatedStrategies,
        pagination: {
          total,
          limit,
          offset
        }
      })
    } catch (error) {
      fastify.log.error('Get strategies error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 创建新策略 (需要认证)
  fastify.post('/', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Create a new strategy',
      tags: ['Strategies'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['name', 'type', 'config'],
        properties: {
          name: { type: 'string', minLength: 1, maxLength: 100 },
          description: { type: 'string', maxLength: 500 },
          type: { type: 'string', enum: ['arbitrage', 'trend', 'grid', 'dca', 'ai', 'custom'] },
          config: {
            type: 'object',
            required: ['symbol', 'exchange', 'capital', 'riskLevel'],
            properties: {
              symbol: { type: 'string' },
              exchange: { type: 'string' },
              capital: { type: 'number', minimum: 0 },
              riskLevel: { type: 'string', enum: ['low', 'medium', 'high'] },
              parameters: { type: 'object' },
              stopLoss: { type: 'number' },
              takeProfit: { type: 'number' },
              maxDrawdown: { type: 'number' }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id
      const validatedData = createStrategySchema.parse(request.body)

      const strategy = await fastify.strategyService.createStrategy(userId, {
        name: validatedData.name,
        description: validatedData.description,
        type: validatedData.type,
        config: validatedData.config,
        status: 'inactive'
      })

      reply.status(201).send({
        success: true,
        message: 'Strategy created successfully',
        data: strategy
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(e => e.message)
        })
      }
      
      fastify.log.error('Create strategy error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取单个策略详情 (需要认证)
  fastify.get('/:strategyId', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get strategy details',
      tags: ['Strategies'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['strategyId'],
        properties: {
          strategyId: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { strategyId } = request.params as { strategyId: string }
      const userId = (request.user as any).id

      const strategy = await fastify.strategyService.getStrategy(strategyId)
      
      if (!strategy) {
        return reply.status(404).send({
          success: false,
          error: 'Strategy not found'
        })
      }

      // 检查策略所有权
      if (strategy.userId !== userId) {
        return reply.status(403).send({
          success: false,
          error: 'Access denied'
        })
      }

      // 获取策略性能数据
      const performance = await fastify.strategyService.getStrategyPerformance(strategyId)

      reply.send({
        success: true,
        data: {
          ...strategy,
          performance
        }
      })
    } catch (error) {
      fastify.log.error('Get strategy error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 更新策略 (需要认证)
  fastify.put('/:strategyId', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Update strategy',
      tags: ['Strategies'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['strategyId'],
        properties: {
          strategyId: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        properties: {
          name: { type: 'string', minLength: 1, maxLength: 100 },
          description: { type: 'string', maxLength: 500 },
          config: { type: 'object' },
          status: { type: 'string', enum: ['active', 'inactive', 'paused'] }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { strategyId } = request.params as { strategyId: string }
      const userId = (request.user as any).id
      const validatedData = updateStrategySchema.parse(request.body)

      // 检查策略存在性和所有权
      const existingStrategy = await fastify.strategyService.getStrategy(strategyId)
      if (!existingStrategy) {
        return reply.status(404).send({
          success: false,
          error: 'Strategy not found'
        })
      }

      if (existingStrategy.userId !== userId) {
        return reply.status(403).send({
          success: false,
          error: 'Access denied'
        })
      }

      const updatedStrategy = await fastify.strategyService.updateStrategy(strategyId, validatedData)

      reply.send({
        success: true,
        message: 'Strategy updated successfully',
        data: updatedStrategy
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(e => e.message)
        })
      }
      
      fastify.log.error('Update strategy error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 删除策略 (需要认证)
  fastify.delete('/:strategyId', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Delete strategy',
      tags: ['Strategies'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['strategyId'],
        properties: {
          strategyId: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { strategyId } = request.params as { strategyId: string }
      const userId = (request.user as any).id

      // 检查策略存在性和所有权
      const strategy = await fastify.strategyService.getStrategy(strategyId)
      if (!strategy) {
        return reply.status(404).send({
          success: false,
          error: 'Strategy not found'
        })
      }

      if (strategy.userId !== userId) {
        return reply.status(403).send({
          success: false,
          error: 'Access denied'
        })
      }

      const deleted = await fastify.strategyService.deleteStrategy(strategyId)
      
      if (!deleted) {
        return reply.status(500).send({
          success: false,
          error: 'Failed to delete strategy'
        })
      }

      reply.send({
        success: true,
        message: 'Strategy deleted successfully'
      })
    } catch (error) {
      fastify.log.error('Delete strategy error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 启动策略 (需要认证)
  fastify.post('/:strategyId/start', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Start strategy execution',
      tags: ['Strategies'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['strategyId'],
        properties: {
          strategyId: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { strategyId } = request.params as { strategyId: string }
      const userId = (request.user as any).id

      // 检查策略所有权
      const strategy = await fastify.strategyService.getStrategy(strategyId)
      if (!strategy || strategy.userId !== userId) {
        return reply.status(404).send({
          success: false,
          error: 'Strategy not found'
        })
      }

      const started = await fastify.strategyService.startStrategy(strategyId)
      
      if (!started) {
        return reply.status(500).send({
          success: false,
          error: 'Failed to start strategy'
        })
      }

      reply.send({
        success: true,
        message: 'Strategy started successfully'
      })
    } catch (error) {
      fastify.log.error('Start strategy error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 停止策略 (需要认证)
  fastify.post('/:strategyId/stop', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Stop strategy execution',
      tags: ['Strategies'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['strategyId'],
        properties: {
          strategyId: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { strategyId } = request.params as { strategyId: string }
      const userId = (request.user as any).id

      // 检查策略所有权
      const strategy = await fastify.strategyService.getStrategy(strategyId)
      if (!strategy || strategy.userId !== userId) {
        return reply.status(404).send({
          success: false,
          error: 'Strategy not found'
        })
      }

      const stopped = await fastify.strategyService.stopStrategy(strategyId)
      
      if (!stopped) {
        return reply.status(500).send({
          success: false,
          error: 'Failed to stop strategy'
        })
      }

      reply.send({
        success: true,
        message: 'Strategy stopped successfully'
      })
    } catch (error) {
      fastify.log.error('Stop strategy error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 暂停策略 (需要认证)
  fastify.post('/:strategyId/pause', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Pause strategy execution',
      tags: ['Strategies'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { strategyId } = request.params as { strategyId: string }
      const userId = (request.user as any).id

      const strategy = await fastify.strategyService.getStrategy(strategyId)
      if (!strategy || strategy.userId !== userId) {
        return reply.status(404).send({
          success: false,
          error: 'Strategy not found'
        })
      }

      const paused = await fastify.strategyService.pauseStrategy(strategyId)
      
      reply.send({
        success: true,
        message: paused ? 'Strategy paused successfully' : 'Failed to pause strategy'
      })
    } catch (error) {
      fastify.log.error('Pause strategy error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 恢复策略 (需要认证)
  fastify.post('/:strategyId/resume', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Resume strategy execution',
      tags: ['Strategies'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { strategyId } = request.params as { strategyId: string }
      const userId = (request.user as any).id

      const strategy = await fastify.strategyService.getStrategy(strategyId)
      if (!strategy || strategy.userId !== userId) {
        return reply.status(404).send({
          success: false,
          error: 'Strategy not found'
        })
      }

      const resumed = await fastify.strategyService.resumeStrategy(strategyId)
      
      reply.send({
        success: true,
        message: resumed ? 'Strategy resumed successfully' : 'Failed to resume strategy'
      })
    } catch (error) {
      fastify.log.error('Resume strategy error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取策略性能 (需要认证)
  fastify.get('/:strategyId/performance', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get strategy performance metrics',
      tags: ['Strategies'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { strategyId } = request.params as { strategyId: string }
      const userId = (request.user as any).id

      const strategy = await fastify.strategyService.getStrategy(strategyId)
      if (!strategy || strategy.userId !== userId) {
        return reply.status(404).send({
          success: false,
          error: 'Strategy not found'
        })
      }

      const performance = await fastify.strategyService.getStrategyPerformance(strategyId)
      
      reply.send({
        success: true,
        data: performance
      })
    } catch (error) {
      fastify.log.error('Get strategy performance error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取策略统计 (需要认证)
  fastify.get('/stats/overview', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get strategy statistics overview',
      tags: ['Strategies'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id

      const stats = await fastify.strategyService.getStrategyStats(userId)
      
      reply.send({
        success: true,
        data: stats
      })
    } catch (error) {
      fastify.log.error('Get strategy stats error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })
}
