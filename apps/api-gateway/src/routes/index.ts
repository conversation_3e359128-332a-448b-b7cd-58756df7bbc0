import { FastifyInstance } from 'fastify'

// 导入路由模块
import healthRoutes from './health'
import authRoutes from './auth'
import marketDataRoutes from './market-data'
import strategiesRoutes from './strategies'
import tradingRoutes from './trading'
import advancedRoutes from './advanced'
import { realtimeRoutes } from './realtime'
import { strategyManagementRoutes } from './strategy-management'

export async function setupRoutes(fastify: FastifyInstance) {
  // API版本前缀
  const apiPrefix = '/api/v1'

  // 健康检查路由 (无前缀)
  await fastify.register(healthRoutes, { prefix: '/health' })

  // 认证路由
  await fastify.register(authRoutes, { prefix: `${apiPrefix}/auth` })

  // 市场数据路由
  await fastify.register(marketDataRoutes, { prefix: `${apiPrefix}/market-data` })

  // 策略管理路由
  await fastify.register(strategiesRoutes, { prefix: `${apiPrefix}/strategies` })

  // 交易执行路由
  await fastify.register(tradingRoutes, { prefix: `${apiPrefix}/trading` })

  // 高级功能路由
  await fastify.register(advancedRoutes, { prefix: `${apiPrefix}/advanced` })

  // 实时数据路由
  await fastify.register(realtimeRoutes)

  // 策略管理路由
  await fastify.register(strategyManagementRoutes)

  // 根路由
  fastify.get('/', {
    schema: {
      description: 'API Gateway root endpoint',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            version: { type: 'string' },
            description: { type: 'string' },
            status: { type: 'string' },
            timestamp: { type: 'string' },
            endpoints: {
              type: 'object',
              properties: {
                health: { type: 'string' },
                docs: { type: 'string' },
                websocket: { type: 'string' },
                api: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const baseUrl = `${request.protocol}://${request.hostname}`

    return {
      name: 'SFQuant API Gateway',
      version: '1.0.0',
      description: 'Cryptocurrency Quantitative Strategy Management System',
      status: 'running',
      timestamp: new Date().toISOString(),
      endpoints: {
        health: `${baseUrl}/health`,
        docs: `${baseUrl}/docs`,
        websocket: `ws://${request.hostname}/ws`,
        api: `${baseUrl}${apiPrefix}`
      }
    }
  })

  // API信息路由
  fastify.get(`${apiPrefix}`, {
    schema: {
      description: 'API information endpoint',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            version: { type: 'string' },
            endpoints: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  path: { type: 'string' },
                  description: { type: 'string' },
                  methods: {
                    type: 'array',
                    items: { type: 'string' }
                  }
                }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    return {
      version: 'v1',
      endpoints: [
        {
          path: '/auth',
          description: 'Authentication and user management',
          methods: ['POST', 'GET', 'PUT', 'DELETE']
        },
        {
          path: '/market-data',
          description: 'Real-time and historical market data',
          methods: ['GET', 'POST']
        },
        {
          path: '/strategies',
          description: 'Strategy management and execution',
          methods: ['GET', 'POST', 'PUT', 'DELETE']
        },
        {
          path: '/trading',
          description: 'Trading execution and order management',
          methods: ['GET', 'POST', 'PUT', 'DELETE']
        },
        {
          path: '/advanced',
          description: 'Advanced features and analytics',
          methods: ['GET', 'POST']
        }
      ]
    }
  })

  // 404处理已在app.ts中设置
  fastify.log.info('All routes registered successfully')
}
