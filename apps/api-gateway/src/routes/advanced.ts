import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'

export default async function advancedRoutes(fastify: FastifyInstance) {
  // 获取系统性能指标
  fastify.get('/metrics/performance', {
    schema: {
      description: 'Get system performance metrics',
      tags: ['Advanced'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                requestCount: { type: 'number' },
                averageResponseTime: { type: 'number' },
                errorRate: { type: 'number' },
                activeConnections: { type: 'number' },
                memoryUsage: { type: 'object' },
                cpuUsage: { type: 'object' },
                timestamp: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const metrics = fastify.performanceMetrics?.getMetrics()

      reply.send({
        success: true,
        data: metrics || {}
      })
    } catch (error) {
      fastify.log.error('Get performance metrics error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取性能告警
  fastify.get('/metrics/alerts', {
    schema: {
      description: 'Get performance alerts',
      tags: ['Advanced']
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const alerts = fastify.performanceMetrics?.getAlerts() || []

      reply.send({
        success: true,
        data: alerts
      })
    } catch (error) {
      fastify.log.error('Get performance alerts error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 重置性能指标 (需要管理员权限)
  fastify.post('/metrics/reset', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Reset performance metrics',
      tags: ['Advanced'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = request.user as any
      
      // 检查管理员权限
      if (user.role !== 'admin') {
        return reply.status(403).send({
          success: false,
          error: 'Admin access required'
        })
      }

      fastify.performanceMetrics?.resetMetrics()

      reply.send({
        success: true,
        message: 'Performance metrics reset successfully'
      })
    } catch (error) {
      fastify.log.error('Reset metrics error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取价格监控统计
  fastify.get('/monitoring/stats', {
    schema: {
      description: 'Get price monitoring statistics',
      tags: ['Advanced']
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const stats = fastify.priceMonitorService.getMonitoringStats()

      reply.send({
        success: true,
        data: stats
      })
    } catch (error) {
      fastify.log.error('Get monitoring stats error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取价格监控配置 (需要认证)
  fastify.get('/monitoring/config', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get price monitoring configurations',
      tags: ['Advanced'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const configs = fastify.priceMonitorService.getMonitoringConfigs()

      reply.send({
        success: true,
        data: configs
      })
    } catch (error) {
      fastify.log.error('Get monitoring config error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 更新价格监控配置 (需要管理员权限)
  fastify.put('/monitoring/config/:symbol', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Update price monitoring configuration',
      tags: ['Advanced'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['symbol'],
        properties: {
          symbol: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        properties: {
          enabled: { type: 'boolean' },
          priceChangeThreshold: { type: 'number', minimum: 0 },
          volumeChangeThreshold: { type: 'number', minimum: 0 },
          checkInterval: { type: 'integer', minimum: 1000 },
          alertChannels: {
            type: 'array',
            items: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = request.user as any
      const { symbol } = request.params as { symbol: string }
      const updates = request.body as any

      // 检查管理员权限
      if (user.role !== 'admin') {
        return reply.status(403).send({
          success: false,
          error: 'Admin access required'
        })
      }

      const updated = fastify.priceMonitorService.updateMonitoringConfig(symbol, updates)

      if (!updated) {
        return reply.status(404).send({
          success: false,
          error: 'Monitoring configuration not found'
        })
      }

      reply.send({
        success: true,
        message: 'Monitoring configuration updated successfully'
      })
    } catch (error) {
      fastify.log.error('Update monitoring config error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取系统缓存统计
  fastify.get('/cache/stats', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get cache statistics',
      tags: ['Advanced'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = request.user as any

      // 检查管理员权限
      if (user.role !== 'admin') {
        return reply.status(403).send({
          success: false,
          error: 'Admin access required'
        })
      }

      // 这里可以添加实际的缓存统计逻辑
      const stats = {
        redisConnected: fastify.redis.connected,
        cacheHitRate: 0.85, // 示例数据
        totalKeys: 1250,
        memoryUsage: '45MB',
        lastUpdate: Date.now()
      }

      reply.send({
        success: true,
        data: stats
      })
    } catch (error) {
      fastify.log.error('Get cache stats error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 清理缓存 (需要管理员权限)
  fastify.post('/cache/clear', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Clear cache',
      tags: ['Advanced'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          pattern: { type: 'string', description: 'Cache key pattern to clear' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = request.user as any
      const body = request.body as { pattern?: string }

      // 检查管理员权限
      if (user.role !== 'admin') {
        return reply.status(403).send({
          success: false,
          error: 'Admin access required'
        })
      }

      // 这里可以添加实际的缓存清理逻辑
      // 例如：根据pattern清理特定的缓存键

      reply.send({
        success: true,
        message: 'Cache cleared successfully'
      })
    } catch (error) {
      fastify.log.error('Clear cache error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取活跃策略列表 (需要认证)
  fastify.get('/strategies/active', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get active strategies',
      tags: ['Advanced'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const activeStrategies = await fastify.strategyService.getActiveStrategies()

      reply.send({
        success: true,
        data: activeStrategies
      })
    } catch (error) {
      fastify.log.error('Get active strategies error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 系统健康检查详情
  fastify.get('/system/health', {
    schema: {
      description: 'Detailed system health check',
      tags: ['Advanced']
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0',
        services: {
          database: {
            status: fastify.database.connected ? 'healthy' : 'unhealthy',
            connected: fastify.database.connected
          },
          redis: {
            status: fastify.redis.connected ? 'healthy' : 'unhealthy',
            connected: fastify.redis.connected
          },
          exchanges: {
            status: 'healthy',
            available: fastify.exchangeService.getAvailableExchanges()
          }
        },
        performance: fastify.performanceMetrics?.getMetrics() || {},
        monitoring: fastify.priceMonitorService.getMonitoringStats()
      }

      reply.send({
        success: true,
        data: health
      })
    } catch (error) {
      fastify.log.error('Get system health error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取系统配置 (需要管理员权限)
  fastify.get('/system/config', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get system configuration',
      tags: ['Advanced'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = request.user as any

      // 检查管理员权限
      if (user.role !== 'admin') {
        return reply.status(403).send({
          success: false,
          error: 'Admin access required'
        })
      }

      // 返回安全的配置信息（不包含敏感数据）
      const safeConfig = {
        environment: process.env.NODE_ENV,
        logLevel: process.env.LOG_LEVEL,
        corsOrigins: process.env.CORS_ORIGINS?.split(',') || [],
        rateLimitMax: process.env.RATE_LIMIT_MAX,
        enableMetrics: process.env.ENABLE_METRICS === 'true',
        wsMaxConnections: process.env.WS_MAX_CONNECTIONS
      }

      reply.send({
        success: true,
        data: safeConfig
      })
    } catch (error) {
      fastify.log.error('Get system config error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 导出数据 (需要认证)
  fastify.get('/export/:type', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Export data',
      tags: ['Advanced'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['type'],
        properties: {
          type: { type: 'string', enum: ['strategies', 'trades', 'performance'] }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          format: { type: 'string', enum: ['json', 'csv'], default: 'json' },
          startDate: { type: 'string' },
          endDate: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id
      const { type } = request.params as { type: string }
      const query = request.query as { 
        format?: string; 
        startDate?: string; 
        endDate?: string 
      }

      // 这里可以添加实际的数据导出逻辑
      const exportData = {
        type,
        userId,
        exportedAt: new Date().toISOString(),
        data: [], // 实际导出的数据
        format: query.format || 'json'
      }

      reply.send({
        success: true,
        data: exportData
      })
    } catch (error) {
      fastify.log.error('Export data error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })
}
