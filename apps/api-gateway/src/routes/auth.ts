import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import bcrypt from 'bcryptjs'
import { z } from 'zod'

// 请求验证schemas
const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  username: z.string().min(3, 'Username must be at least 3 characters').max(20, 'Username must be at most 20 characters'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string()
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required')
})

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
  confirmPassword: z.string()
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

const updateProfileSchema = z.object({
  username: z.string().min(3).max(20).optional(),
  email: z.string().email().optional(),
  firstName: z.string().max(50).optional(),
  lastName: z.string().max(50).optional(),
  timezone: z.string().optional(),
  language: z.string().optional()
})

export default async function authRoutes(fastify: FastifyInstance) {
  // 用户注册
  fastify.post('/register', {
    schema: {
      description: 'Register a new user',
      tags: ['Auth'],
      body: {
        type: 'object',
        required: ['email', 'username', 'password', 'confirmPassword'],
        properties: {
          email: { type: 'string', format: 'email' },
          username: { type: 'string', minLength: 3, maxLength: 20 },
          password: { type: 'string', minLength: 8 },
          confirmPassword: { type: 'string', minLength: 8 }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                username: { type: 'string' },
                token: { type: 'string' }
              }
            }
          }
        },
        400: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            error: { type: 'string' },
            details: { type: 'array' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const validatedData = registerSchema.parse(request.body)
      
      // 检查用户是否已存在
      const existingUser = await fastify.database.findUserByEmail(validatedData.email)
      if (existingUser) {
        return reply.status(400).send({
          success: false,
          error: 'User already exists',
          details: ['Email is already registered']
        })
      }

      // 加密密码
      const saltRounds = 12
      const passwordHash = await bcrypt.hash(validatedData.password, saltRounds)

      // 创建用户
      const user = await fastify.database.createUser({
        email: validatedData.email,
        username: validatedData.username,
        passwordHash,
        role: 'user'
      })

      // 生成JWT token
      const token = await reply.jwtSign({
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role
      })

      // 缓存用户信息
      await fastify.redis.set(`user:${user.id}`, {
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role
      }, 3600) // 1小时缓存

      reply.status(201).send({
        success: true,
        message: 'User registered successfully',
        data: {
          id: user.id,
          email: user.email,
          username: user.username,
          token
        }
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(e => e.message)
        })
      }
      
      fastify.log.error('Registration error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 用户登录
  fastify.post('/login', {
    schema: {
      description: 'User login',
      tags: ['Auth'],
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                username: { type: 'string' },
                token: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const validatedData = loginSchema.parse(request.body)
      
      // 查找用户
      const user = await fastify.database.findUserByEmail(validatedData.email)
      if (!user) {
        return reply.status(401).send({
          success: false,
          error: 'Invalid credentials'
        })
      }

      // 验证密码
      const isValidPassword = await bcrypt.compare(validatedData.password, user.passwordHash)
      if (!isValidPassword) {
        return reply.status(401).send({
          success: false,
          error: 'Invalid credentials'
        })
      }

      // 生成JWT token
      const token = await reply.jwtSign({
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role
      })

      // 缓存用户信息
      await fastify.redis.set(`user:${user.id}`, {
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role
      }, 3600)

      // 记录登录时间
      await fastify.database.updateUser(user.id, {
        lastLoginAt: new Date()
      })

      reply.send({
        success: true,
        message: 'Login successful',
        data: {
          id: user.id,
          email: user.email,
          username: user.username,
          token
        }
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(e => e.message)
        })
      }
      
      fastify.log.error('Login error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取用户信息 (需要认证)
  fastify.get('/profile', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get user profile',
      tags: ['Auth'],
      security: [{ Bearer: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                username: { type: 'string' },
                role: { type: 'string' },
                createdAt: { type: 'string' },
                lastLoginAt: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id
      
      // 先从缓存获取
      const cachedUser = await fastify.redis.get(`user:${userId}`)
      if (cachedUser) {
        return reply.send({
          success: true,
          data: cachedUser
        })
      }

      // 从数据库获取
      const user = await fastify.database.findUserById(userId)
      if (!user) {
        return reply.status(404).send({
          success: false,
          error: 'User not found'
        })
      }

      const userData = {
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }

      // 更新缓存
      await fastify.redis.set(`user:${userId}`, userData, 3600)

      reply.send({
        success: true,
        data: userData
      })
    } catch (error) {
      fastify.log.error('Get profile error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 更新用户信息 (需要认证)
  fastify.put('/profile', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Update user profile',
      tags: ['Auth'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          username: { type: 'string', minLength: 3, maxLength: 20 },
          email: { type: 'string', format: 'email' },
          firstName: { type: 'string', maxLength: 50 },
          lastName: { type: 'string', maxLength: 50 },
          timezone: { type: 'string' },
          language: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id
      const validatedData = updateProfileSchema.parse(request.body)

      // 如果更新邮箱，检查是否已被使用
      if (validatedData.email) {
        const existingUser = await fastify.database.findUserByEmail(validatedData.email)
        if (existingUser && existingUser.id !== userId) {
          return reply.status(400).send({
            success: false,
            error: 'Email is already in use'
          })
        }
      }

      const updatedUser = await fastify.database.updateUser(userId, validatedData)
      
      // 清除缓存
      await fastify.redis.del(`user:${userId}`)

      reply.send({
        success: true,
        message: 'Profile updated successfully',
        data: {
          id: updatedUser.id,
          email: updatedUser.email,
          username: updatedUser.username,
          role: updatedUser.role
        }
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(e => e.message)
        })
      }
      
      fastify.log.error('Update profile error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 修改密码 (需要认证)
  fastify.post('/change-password', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Change user password',
      tags: ['Auth'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['currentPassword', 'newPassword', 'confirmPassword'],
        properties: {
          currentPassword: { type: 'string' },
          newPassword: { type: 'string', minLength: 8 },
          confirmPassword: { type: 'string', minLength: 8 }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id
      const validatedData = changePasswordSchema.parse(request.body)

      // 获取用户信息
      const user = await fastify.database.findUserById(userId)
      if (!user) {
        return reply.status(404).send({
          success: false,
          error: 'User not found'
        })
      }

      // 验证当前密码
      const isValidPassword = await bcrypt.compare(validatedData.currentPassword, user.passwordHash)
      if (!isValidPassword) {
        return reply.status(400).send({
          success: false,
          error: 'Current password is incorrect'
        })
      }

      // 加密新密码
      const saltRounds = 12
      const newPasswordHash = await bcrypt.hash(validatedData.newPassword, saltRounds)

      // 更新密码
      await fastify.database.updateUser(userId, {
        passwordHash: newPasswordHash
      })

      // 清除用户缓存
      await fastify.redis.del(`user:${userId}`)

      reply.send({
        success: true,
        message: 'Password changed successfully'
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(e => e.message)
        })
      }
      
      fastify.log.error('Change password error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 登出 (需要认证)
  fastify.post('/logout', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'User logout',
      tags: ['Auth'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id
      
      // 清除用户缓存
      await fastify.redis.del(`user:${userId}`)
      
      // 这里可以添加token黑名单逻辑
      
      reply.send({
        success: true,
        message: 'Logout successful'
      })
    } catch (error) {
      fastify.log.error('Logout error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })
}
