import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { performHealthCheck, getSystemMetrics } from '../startup'

export default async function healthRoutes(fastify: FastifyInstance) {
  // 基础健康检查
  fastify.get('/', {
    schema: {
      description: 'Basic health check',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string' },
            uptime: { type: 'number' },
            version: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0'
    }
  })



  // 详细健康检查
  fastify.get('/detailed', {
    schema: {
      description: 'Detailed health check with service status',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string' },
            uptime: { type: 'number' },
            version: { type: 'string' },
            services: {
              type: 'object',
              properties: {
                database: { type: 'object' },
                redis: { type: 'object' },
                websocket: { type: 'object' },
                exchanges: { type: 'object' }
              }
            },
            system: {
              type: 'object',
              properties: {
                memory: { type: 'object' },
                cpu: { type: 'object' },
                platform: { type: 'string' },
                nodeVersion: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const services = await checkServices(fastify)
    const system = getSystemInfo()

    const overallStatus = Object.values(services).every(service => service.status === 'healthy')
      ? 'healthy'
      : 'degraded'

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      services,
      system
    }
  })

  // 就绪检查 (Kubernetes readiness probe)
  fastify.get('/ready', {
    schema: {
      description: 'Readiness check for Kubernetes',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            ready: { type: 'boolean' },
            timestamp: { type: 'string' }
          }
        },
        503: {
          type: 'object',
          properties: {
            ready: { type: 'boolean' },
            timestamp: { type: 'string' },
            reason: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const services = await checkServices(fastify)
      const criticalServices = ['database', 'redis']

      const criticalServicesHealthy = criticalServices.every(
        service => services[service]?.status === 'healthy'
      )

      if (criticalServicesHealthy) {
        return {
          ready: true,
          timestamp: new Date().toISOString()
        }
      } else {
        reply.status(503)
        return {
          ready: false,
          timestamp: new Date().toISOString(),
          reason: 'Critical services are not healthy'
        }
      }
    } catch (error) {
      reply.status(503)
      return {
        ready: false,
        timestamp: new Date().toISOString(),
        reason: 'Health check failed'
      }
    }
  })

  // 存活检查 (Kubernetes liveness probe)
  fastify.get('/live', {
    schema: {
      description: 'Liveness check for Kubernetes',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            alive: { type: 'boolean' },
            timestamp: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    return {
      alive: true,
      timestamp: new Date().toISOString()
    }
  })

  // 性能指标
  fastify.get('/metrics', {
    schema: {
      description: 'Performance metrics',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            performance: { type: 'object' },
            websocket: { type: 'object' },
            execution: { type: 'object' },
            exchanges: { type: 'object' },
            memory: { type: 'object' },
            timestamp: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    return getSystemMetrics(fastify)
  })
}

// 检查各个服务的健康状态
async function checkServices(fastify: FastifyInstance) {
  const services: Record<string, any> = {}

  // 检查数据库
  try {
    if (fastify.database) {
      await fastify.database.healthCheck()
      services.database = {
        status: 'healthy',
        responseTime: 0, // 可以添加实际的响应时间测量
        lastCheck: new Date().toISOString()
      }
    } else {
      services.database = {
        status: 'unavailable',
        error: 'Database service not initialized',
        lastCheck: new Date().toISOString()
      }
    }
  } catch (error) {
    services.database = {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      lastCheck: new Date().toISOString()
    }
  }

  // 检查Redis
  try {
    if (fastify.redis) {
      await fastify.redis.ping()
      services.redis = {
        status: 'healthy',
        responseTime: 0,
        lastCheck: new Date().toISOString()
      }
    } else {
      services.redis = {
        status: 'unavailable',
        error: 'Redis service not initialized',
        lastCheck: new Date().toISOString()
      }
    }
  } catch (error) {
    services.redis = {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      lastCheck: new Date().toISOString()
    }
  }

  // 检查WebSocket
  try {
    if (fastify.wsManager) {
      const stats = fastify.wsManager.getStats()
      services.websocket = {
        status: 'healthy',
        connections: stats.totalClients,
        lastCheck: new Date().toISOString()
      }
    } else {
      services.websocket = {
        status: 'unavailable',
        error: 'WebSocket manager not initialized',
        lastCheck: new Date().toISOString()
      }
    }
  } catch (error) {
    services.websocket = {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      lastCheck: new Date().toISOString()
    }
  }

  // 检查交易所连接
  try {
    if (fastify.exchangeService) {
      // 这里可以添加实际的交易所连接检查
      services.exchanges = {
        status: 'healthy',
        connectedExchanges: [], // 可以添加实际连接的交易所列表
        lastCheck: new Date().toISOString()
      }
    } else {
      services.exchanges = {
        status: 'unavailable',
        error: 'Exchange service not initialized',
        lastCheck: new Date().toISOString()
      }
    }
  } catch (error) {
    services.exchanges = {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      lastCheck: new Date().toISOString()
    }
  }

  return services
}

// 获取系统信息
function getSystemInfo() {
  const memoryUsage = process.memoryUsage()
  const cpuUsage = process.cpuUsage()

  return {
    memory: {
      rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
      external: Math.round(memoryUsage.external / 1024 / 1024), // MB
      arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024) // MB
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system
    },
    platform: process.platform,
    nodeVersion: process.version,
    pid: process.pid,
    uptime: process.uptime()
  }
}
