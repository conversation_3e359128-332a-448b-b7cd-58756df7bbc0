import { FastifyInstance } from 'fastify'

export async function realtimeRoutes(fastify: FastifyInstance) {
  // 获取实时价格
  fastify.get('/api/v1/realtime/price/:exchange/:symbol', {
    schema: {
      description: 'Get real-time price for a symbol on an exchange',
      tags: ['Real-time Data'],
      params: {
        type: 'object',
        properties: {
          exchange: { type: 'string' },
          symbol: { type: 'string' }
        },
        required: ['exchange', 'symbol']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                symbol: { type: 'string' },
                exchange: { type: 'string' },
                price: { type: 'number' },
                bid: { type: 'number' },
                ask: { type: 'number' },
                volume: { type: 'number' },
                change24h: { type: 'number' },
                timestamp: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { exchange, symbol } = request.params as { exchange: string; symbol: string }
    
    try {
      const priceData = await fastify.realTimeMarketDataService.getRealTimePrice(exchange, symbol)
      
      if (!priceData) {
        return reply.status(404).send({
          success: false,
          error: 'Price data not found',
          message: `No real-time price data available for ${symbol} on ${exchange}`
        })
      }

      return {
        success: true,
        data: priceData
      }
    } catch (error) {
      fastify.log.error('Error fetching real-time price:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to fetch real-time price data'
      })
    }
  })

  // 获取实时订单簿
  fastify.get('/api/v1/realtime/orderbook/:exchange/:symbol', {
    schema: {
      description: 'Get real-time order book for a symbol on an exchange',
      tags: ['Real-time Data'],
      params: {
        type: 'object',
        properties: {
          exchange: { type: 'string' },
          symbol: { type: 'string' }
        },
        required: ['exchange', 'symbol']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                symbol: { type: 'string' },
                exchange: { type: 'string' },
                bids: {
                  type: 'array',
                  items: {
                    type: 'array',
                    items: { type: 'number' }
                  }
                },
                asks: {
                  type: 'array',
                  items: {
                    type: 'array',
                    items: { type: 'number' }
                  }
                },
                timestamp: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { exchange, symbol } = request.params as { exchange: string; symbol: string }
    
    try {
      const orderBookData = await fastify.realTimeMarketDataService.getRealTimeOrderBook(exchange, symbol)
      
      if (!orderBookData) {
        return reply.status(404).send({
          success: false,
          error: 'Order book data not found',
          message: `No real-time order book data available for ${symbol} on ${exchange}`
        })
      }

      return {
        success: true,
        data: orderBookData
      }
    } catch (error) {
      fastify.log.error('Error fetching real-time order book:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to fetch real-time order book data'
      })
    }
  })

  // 启动实时数据服务
  fastify.post('/api/v1/realtime/start', {
    schema: {
      description: 'Start real-time market data service',
      tags: ['Real-time Data'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      await fastify.realTimeMarketDataService.start()
      
      return {
        success: true,
        message: 'Real-time market data service started successfully'
      }
    } catch (error) {
      fastify.log.error('Error starting real-time data service:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to start real-time market data service'
      })
    }
  })

  // 停止实时数据服务
  fastify.post('/api/v1/realtime/stop', {
    schema: {
      description: 'Stop real-time market data service',
      tags: ['Real-time Data'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      await fastify.realTimeMarketDataService.stop()
      
      return {
        success: true,
        message: 'Real-time market data service stopped successfully'
      }
    } catch (error) {
      fastify.log.error('Error stopping real-time data service:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to stop real-time market data service'
      })
    }
  })

  // 启动策略执行引擎
  fastify.post('/api/v1/execution/start', {
    schema: {
      description: 'Start strategy execution engine',
      tags: ['Strategy Execution'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      await fastify.strategyExecutionEngine.start()
      
      return {
        success: true,
        message: 'Strategy execution engine started successfully'
      }
    } catch (error) {
      fastify.log.error('Error starting strategy execution engine:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to start strategy execution engine'
      })
    }
  })

  // 停止策略执行引擎
  fastify.post('/api/v1/execution/stop', {
    schema: {
      description: 'Stop strategy execution engine',
      tags: ['Strategy Execution'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      await fastify.strategyExecutionEngine.stop()
      
      return {
        success: true,
        message: 'Strategy execution engine stopped successfully'
      }
    } catch (error) {
      fastify.log.error('Error stopping strategy execution engine:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to stop strategy execution engine'
      })
    }
  })

  // 获取执行引擎状态
  fastify.get('/api/v1/execution/status', {
    schema: {
      description: 'Get strategy execution engine status',
      tags: ['Strategy Execution'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                isRunning: { type: 'boolean' },
                activeStrategies: { type: 'number' },
                queueLength: { type: 'number' },
                strategies: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      type: { type: 'string' },
                      symbol: { type: 'string' },
                      exchange: { type: 'string' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const stats = fastify.strategyExecutionEngine.getExecutionStats()
      
      return {
        success: true,
        data: stats
      }
    } catch (error) {
      fastify.log.error('Error fetching execution engine status:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to fetch execution engine status'
      })
    }
  })

  // 获取交易所状态
  fastify.get('/api/v1/exchanges/status', {
    schema: {
      description: 'Get all exchange connection status',
      tags: ['Real-time Data'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              additionalProperties: {
                type: 'object',
                properties: {
                  status: { type: 'boolean' },
                  lastCheck: { type: 'number' },
                  supported: { type: 'boolean' }
                }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const status = fastify.exchangeService.getAllExchangeStatus()
      
      return {
        success: true,
        data: status
      }
    } catch (error) {
      fastify.log.error('Error fetching exchange status:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to fetch exchange status'
      })
    }
  })
}
