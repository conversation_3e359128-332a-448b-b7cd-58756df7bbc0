import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { z } from 'zod'

// 订单验证schema
const createOrderSchema = z.object({
  symbol: z.string().min(1, 'Symbol is required'),
  exchange: z.string().min(1, 'Exchange is required'),
  type: z.enum(['market', 'limit'], { required_error: 'Order type is required' }),
  side: z.enum(['buy', 'sell'], { required_error: 'Order side is required' }),
  amount: z.number().positive('Amount must be positive'),
  price: z.number().positive('Price must be positive').optional(),
  stopLoss: z.number().positive().optional(),
  takeProfit: z.number().positive().optional()
})

export default async function tradingRoutes(fastify: FastifyInstance) {
  // 获取账户余额 (需要认证)
  fastify.get('/balance', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get account balance',
      tags: ['Trading'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          exchange: { type: 'string', default: 'binance' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = request.query as { exchange?: string }
      const exchange = query.exchange || 'binance'

      const balance = await fastify.exchangeService.getBalance(exchange)
      
      if (!balance) {
        return reply.status(404).send({
          success: false,
          error: 'Balance not available'
        })
      }

      reply.send({
        success: true,
        data: balance
      })
    } catch (error) {
      fastify.log.error('Get balance error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 创建订单 (需要认证)
  fastify.post('/orders', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Create a new order',
      tags: ['Trading'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['symbol', 'exchange', 'type', 'side', 'amount'],
        properties: {
          symbol: { type: 'string' },
          exchange: { type: 'string' },
          type: { type: 'string', enum: ['market', 'limit'] },
          side: { type: 'string', enum: ['buy', 'sell'] },
          amount: { type: 'number', minimum: 0 },
          price: { type: 'number', minimum: 0 },
          stopLoss: { type: 'number', minimum: 0 },
          takeProfit: { type: 'number', minimum: 0 }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = (request.user as any).id
      const validatedData = createOrderSchema.parse(request.body)

      // 验证限价单必须有价格
      if (validatedData.type === 'limit' && !validatedData.price) {
        return reply.status(400).send({
          success: false,
          error: 'Price is required for limit orders'
        })
      }

      const order = await fastify.exchangeService.createOrder(
        validatedData.exchange,
        validatedData.symbol,
        validatedData.type,
        validatedData.side,
        validatedData.amount,
        validatedData.price
      )

      if (!order) {
        return reply.status(500).send({
          success: false,
          error: 'Failed to create order'
        })
      }

      reply.status(201).send({
        success: true,
        message: 'Order created successfully',
        data: order
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(e => e.message)
        })
      }
      
      fastify.log.error('Create order error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取订单状态 (需要认证)
  fastify.get('/orders/:orderId', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get order status',
      tags: ['Trading'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['orderId'],
        properties: {
          orderId: { type: 'string' }
        }
      },
      querystring: {
        type: 'object',
        required: ['symbol', 'exchange'],
        properties: {
          symbol: { type: 'string' },
          exchange: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { orderId } = request.params as { orderId: string }
      const query = request.query as { symbol: string; exchange: string }

      const order = await fastify.exchangeService.getOrderStatus(
        query.exchange,
        orderId,
        query.symbol
      )

      if (!order) {
        return reply.status(404).send({
          success: false,
          error: 'Order not found'
        })
      }

      reply.send({
        success: true,
        data: order
      })
    } catch (error) {
      fastify.log.error('Get order status error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 取消订单 (需要认证)
  fastify.delete('/orders/:orderId', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Cancel an order',
      tags: ['Trading'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['orderId'],
        properties: {
          orderId: { type: 'string' }
        }
      },
      querystring: {
        type: 'object',
        required: ['symbol', 'exchange'],
        properties: {
          symbol: { type: 'string' },
          exchange: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { orderId } = request.params as { orderId: string }
      const query = request.query as { symbol: string; exchange: string }

      const result = await fastify.exchangeService.cancelOrder(
        query.exchange,
        orderId,
        query.symbol
      )

      if (!result) {
        return reply.status(500).send({
          success: false,
          error: 'Failed to cancel order'
        })
      }

      reply.send({
        success: true,
        message: 'Order cancelled successfully',
        data: result
      })
    } catch (error) {
      fastify.log.error('Cancel order error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取开放订单 (需要认证)
  fastify.get('/orders', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get open orders',
      tags: ['Trading'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          exchange: { type: 'string', default: 'binance' },
          symbol: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = request.query as { exchange?: string; symbol?: string }
      const exchange = query.exchange || 'binance'

      const orders = await fastify.exchangeService.getOpenOrders(exchange, query.symbol)

      reply.send({
        success: true,
        data: orders || []
      })
    } catch (error) {
      fastify.log.error('Get open orders error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取交易历史 (需要认证)
  fastify.get('/history', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get trade history',
      tags: ['Trading'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          exchange: { type: 'string', default: 'binance' },
          symbol: { type: 'string' },
          limit: { type: 'integer', default: 100, minimum: 1, maximum: 1000 }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = request.query as { 
        exchange?: string; 
        symbol?: string; 
        limit?: number 
      }
      const exchange = query.exchange || 'binance'
      const limit = query.limit || 100

      const trades = await fastify.exchangeService.getTradeHistory(
        exchange, 
        query.symbol, 
        limit
      )

      reply.send({
        success: true,
        data: trades || []
      })
    } catch (error) {
      fastify.log.error('Get trade history error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取可用市场 (需要认证)
  fastify.get('/markets', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Get available markets',
      tags: ['Trading'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          exchange: { type: 'string', default: 'binance' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = request.query as { exchange?: string }
      const exchange = query.exchange || 'binance'

      const markets = await fastify.exchangeService.getMarkets(exchange)

      if (!markets) {
        return reply.status(404).send({
          success: false,
          error: 'Markets not available'
        })
      }

      reply.send({
        success: true,
        data: markets
      })
    } catch (error) {
      fastify.log.error('Get markets error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 测试交易所连接 (需要认证)
  fastify.get('/exchanges/status', {
    onRequest: [fastify.authenticate],
    schema: {
      description: 'Test exchange connections',
      tags: ['Trading'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const statuses = await fastify.exchangeService.getExchangeStatuses()

      reply.send({
        success: true,
        data: statuses
      })
    } catch (error) {
      fastify.log.error('Get exchange statuses error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // 获取可用交易所列表
  fastify.get('/exchanges', {
    schema: {
      description: 'Get available exchanges',
      tags: ['Trading']
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const exchanges = fastify.exchangeService.getAvailableExchanges()

      reply.send({
        success: true,
        data: exchanges
      })
    } catch (error) {
      fastify.log.error('Get exchanges error:', error)
      reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })
}
