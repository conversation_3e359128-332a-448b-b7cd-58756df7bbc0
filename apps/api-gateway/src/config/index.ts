import { z } from 'zod'

// 环境变量验证schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('8080'),
  HOST: z.string().default('0.0.0.0'),

  // 数据库配置
  DATABASE_URL: z.string().default('postgresql://postgres:password@localhost:5432/sfquant'),

  // Redis配置
  REDIS_URL: z.string().default('redis://localhost:6379'),

  // JWT配置
  JWT_SECRET: z.string().default('your-super-secret-jwt-key-change-in-production'),
  JWT_EXPIRES_IN: z.string().default('7d'),

  // CORS配置
  CORS_ORIGINS: z.string().default('http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:3003'),

  // 速率限制配置
  RATE_LIMIT_MAX: z.string().transform(Number).default('1000'),
  RATE_LIMIT_WINDOW: z.string().default('1 minute'),

  // 日志配置
  LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),

  // 加密配置
  ENCRYPTION_KEY: z.string().default('your-encryption-key-32-characters'),

  // 外部API配置
  COINGECKO_API_KEY: z.string().optional(),
  COINMARKETCAP_API_KEY: z.string().optional(),

  // WebSocket配置
  WS_HEARTBEAT_INTERVAL: z.string().transform(Number).default('15000'),
  WS_HEARTBEAT_TIMEOUT: z.string().transform(Number).default('30000'),
  WS_MAX_CONNECTIONS: z.string().transform(Number).default('1000'),

  // 交易所API配置
  BINANCE_API_KEY: z.string().optional(),
  BINANCE_SECRET_KEY: z.string().optional(),
  BINANCE_SANDBOX: z.string().transform(val => val === 'true').default('true'),

  OKX_API_KEY: z.string().optional(),
  OKX_SECRET_KEY: z.string().optional(),
  OKX_PASSPHRASE: z.string().optional(),
  OKX_SANDBOX: z.string().transform(val => val === 'true').default('true'),

  BYBIT_API_KEY: z.string().optional(),
  BYBIT_SECRET_KEY: z.string().optional(),
  BYBIT_SANDBOX: z.string().transform(val => val === 'true').default('true'),

  HUOBI_API_KEY: z.string().optional(),
  HUOBI_SECRET_KEY: z.string().optional(),
  HUOBI_SANDBOX: z.string().transform(val => val === 'true').default('true'),

  // 性能监控配置
  ENABLE_METRICS: z.string().transform(val => val === 'true').default('true'),
  METRICS_INTERVAL: z.string().transform(Number).default('10000'),

  // 缓存配置
  CACHE_TTL: z.string().transform(Number).default('300'), // 5分钟
  CACHE_MAX_SIZE: z.string().transform(Number).default('1000'),

  // 文件上传配置
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'), // 10MB
  UPLOAD_DIR: z.string().default('./uploads'),

  // 邮件配置 (可选)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().transform(Number).optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),

  // 监控和告警配置
  ALERT_WEBHOOK_URL: z.string().optional(),
  SLACK_WEBHOOK_URL: z.string().optional(),

  // 安全配置
  ENABLE_HELMET: z.string().transform(val => val === 'true').default('true'),
  ENABLE_RATE_LIMIT: z.string().transform(val => val === 'true').default('true'),
  ENABLE_CORS: z.string().transform(val => val === 'true').default('true'),

  // 开发配置
  ENABLE_SWAGGER: z.string().transform(val => val === 'true').default('true'),
  ENABLE_PLAYGROUND: z.string().transform(val => val === 'true').default('false'),
})

// 验证环境变量，提供默认值用于开发测试
let env
try {
  env = envSchema.parse(process.env)
} catch (error) {
  console.warn('⚠️  Using default configuration for development/testing')
  env = envSchema.parse({
    NODE_ENV: 'development',
    PORT: '8080',
    HOST: '0.0.0.0',
    DATABASE_URL: 'postgresql://postgres:password@localhost:5432/sfquant_test',
    REDIS_URL: 'redis://localhost:6379',
    JWT_SECRET: 'development-jwt-secret-key-change-in-production',
    JWT_EXPIRES_IN: '7d',
    CORS_ORIGINS: 'http://localhost:3000,http://localhost:3001',
    LOG_LEVEL: 'info',
    ENCRYPTION_KEY: 'development-encryption-key-32-char',
    ENABLE_METRICS: 'true',
    ENABLE_SWAGGER: 'true'
  })
}

// 导出配置
export const config = {
  // 基础配置
  NODE_ENV: env.NODE_ENV,
  PORT: env.PORT,
  HOST: env.HOST,

  // 数据库
  DATABASE_URL: env.DATABASE_URL,

  // Redis
  REDIS_URL: env.REDIS_URL,

  // JWT
  JWT_SECRET: env.JWT_SECRET,
  JWT_EXPIRES_IN: env.JWT_EXPIRES_IN,

  // CORS
  CORS_ORIGINS: env.CORS_ORIGINS.split(','),

  // 限流
  RATE_LIMIT_MAX: env.RATE_LIMIT_MAX,
  RATE_LIMIT_WINDOW: env.RATE_LIMIT_WINDOW,

  // 日志
  LOG_LEVEL: env.LOG_LEVEL,

  // 加密
  ENCRYPTION_KEY: env.ENCRYPTION_KEY,

  // 外部API
  COINGECKO_API_KEY: env.COINGECKO_API_KEY,
  COINMARKETCAP_API_KEY: env.COINMARKETCAP_API_KEY,

  // WebSocket
  WS_HEARTBEAT_INTERVAL: env.WS_HEARTBEAT_INTERVAL,
  WS_HEARTBEAT_TIMEOUT: env.WS_HEARTBEAT_TIMEOUT,
  WS_MAX_CONNECTIONS: env.WS_MAX_CONNECTIONS,

  // 交易所
  EXCHANGES: {
    BINANCE: {
      API_KEY: env.BINANCE_API_KEY,
      SECRET_KEY: env.BINANCE_SECRET_KEY,
      SANDBOX: env.BINANCE_SANDBOX
    },
    OKX: {
      API_KEY: env.OKX_API_KEY,
      SECRET_KEY: env.OKX_SECRET_KEY,
      PASSPHRASE: env.OKX_PASSPHRASE,
      SANDBOX: env.OKX_SANDBOX
    },
    BYBIT: {
      API_KEY: env.BYBIT_API_KEY,
      SECRET_KEY: env.BYBIT_SECRET_KEY,
      SANDBOX: env.BYBIT_SANDBOX
    },
    HUOBI: {
      API_KEY: env.HUOBI_API_KEY,
      SECRET_KEY: env.HUOBI_SECRET_KEY,
      SANDBOX: env.HUOBI_SANDBOX
    }
  },

  // 性能监控
  PERFORMANCE: {
    ENABLE_METRICS: env.ENABLE_METRICS,
    METRICS_INTERVAL: env.METRICS_INTERVAL
  },

  // 缓存
  CACHE: {
    TTL: env.CACHE_TTL,
    MAX_SIZE: env.CACHE_MAX_SIZE
  },

  // 文件上传
  UPLOAD: {
    MAX_FILE_SIZE: env.MAX_FILE_SIZE,
    UPLOAD_DIR: env.UPLOAD_DIR
  },

  // 邮件
  SMTP: {
    HOST: env.SMTP_HOST,
    PORT: env.SMTP_PORT,
    USER: env.SMTP_USER,
    PASS: env.SMTP_PASS
  },

  // 监控告警
  ALERTS: {
    WEBHOOK_URL: env.ALERT_WEBHOOK_URL,
    SLACK_WEBHOOK_URL: env.SLACK_WEBHOOK_URL
  },

  // 安全
  SECURITY: {
    ENABLE_HELMET: env.ENABLE_HELMET,
    ENABLE_RATE_LIMIT: env.ENABLE_RATE_LIMIT,
    ENABLE_CORS: env.ENABLE_CORS
  },

  // 开发
  DEVELOPMENT: {
    ENABLE_SWAGGER: env.ENABLE_SWAGGER,
    ENABLE_PLAYGROUND: env.ENABLE_PLAYGROUND
  }
}

// 配置验证函数
export function validateConfig() {
  const errors: string[] = []

  // 检查必需的配置
  if (config.NODE_ENV === 'production') {
    if (config.JWT_SECRET === 'your-super-secret-jwt-key-change-in-production') {
      errors.push('JWT_SECRET must be changed in production')
    }

    if (config.ENCRYPTION_KEY === 'your-encryption-key-32-characters') {
      errors.push('ENCRYPTION_KEY must be changed in production')
    }

    if (!config.DATABASE_URL.includes('localhost') === false) {
      console.warn('⚠️  Using localhost database in production')
    }
  }

  // 检查数据库连接字符串
  if (!config.DATABASE_URL.startsWith('postgresql://')) {
    errors.push('DATABASE_URL must be a valid PostgreSQL connection string')
  }

  // 检查Redis连接字符串
  if (!config.REDIS_URL.startsWith('redis://')) {
    errors.push('REDIS_URL must be a valid Redis connection string')
  }

  if (errors.length > 0) {
    console.error('❌ Configuration errors:')
    errors.forEach(error => console.error(`  - ${error}`))
    process.exit(1)
  }

  console.log('✅ Configuration validated successfully')
}

// 打印配置信息（隐藏敏感信息）
export function printConfig() {
  const safeConfig = {
    NODE_ENV: config.NODE_ENV,
    PORT: config.PORT,
    HOST: config.HOST,
    LOG_LEVEL: config.LOG_LEVEL,
    CORS_ORIGINS: config.CORS_ORIGINS,
    RATE_LIMIT_MAX: config.RATE_LIMIT_MAX,
    WS_MAX_CONNECTIONS: config.WS_MAX_CONNECTIONS,
    PERFORMANCE: config.PERFORMANCE,
    CACHE: config.CACHE,
    SECURITY: config.SECURITY,
    DEVELOPMENT: config.DEVELOPMENT
  }

  console.log('📋 Current configuration:')
  console.log(JSON.stringify(safeConfig, null, 2))
}

// 导出类型
export type Config = typeof config

// 在启动时验证配置
if (require.main === module) {
  validateConfig()
  printConfig()
}
