import { FastifyInstance } from 'fastify'
import rateLimit from '@fastify/rate-limit'
import { config } from '../config'

export async function applyRateLimitConfig(fastify: FastifyInstance): Promise<void> {
  // 注册速率限制插件
  await fastify.register(rateLimit, {
    max: config.RATE_LIMIT_MAX,
    timeWindow: config.RATE_LIMIT_WINDOW,
    skipSuccessfulRequests: config.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS,
    keyGenerator: (request) => {
      // 优先使用用户ID，其次使用IP地址
      const user = request.user as any
      return user?.id || request.ip
    },
    errorResponseBuilder: (request, context) => {
      return {
        statusCode: 429,
        error: 'Too Many Requests',
        message: `Rate limit exceeded, retry in ${Math.round(context.ttl / 1000)} seconds`,
        retryAfter: Math.round(context.ttl / 1000)
      }
    },
    onExceeding: (request) => {
      fastify.log.warn(`Rate limit exceeded for ${request.ip}`)
    }
  })

  fastify.log.info('Rate limiting configured successfully')
}
