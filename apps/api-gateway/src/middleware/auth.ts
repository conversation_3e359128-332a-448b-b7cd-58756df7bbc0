import { FastifyRequest, FastifyReply } from 'fastify'
import fp from 'fastify-plugin'

// 扩展 FastifyRequest 类型
declare module 'fastify' {
  interface FastifyRequest {
    user?: {
      id: string
      email: string
      username: string
      role: string
    }
  }

  interface FastifyInstance {
    authenticate: (request: FastifyRequest, reply: FastifyReply) => Promise<void>
    optionalAuth: (request: FastifyRequest, reply: FastifyReply) => Promise<void>
    requireRole: (role: string) => (request: FastifyRequest, reply: FastifyReply) => Promise<void>
  }
}

async function authPlugin(fastify: any) {
  // 必需认证中间件
  fastify.decorate('authenticate', async function(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 从请求头获取token
      const authHeader = request.headers.authorization
      if (!authHeader) {
        return reply.status(401).send({
          success: false,
          error: 'No authorization header',
          message: 'Authorization header is required'
        })
      }

      // 验证Bearer token格式
      const token = authHeader.replace('Bearer ', '')
      if (!token || token === authHeader) {
        return reply.status(401).send({
          success: false,
          error: 'Invalid authorization format',
          message: 'Authorization header must be in format: Bearer <token>'
        })
      }

      try {
        // 验证JWT token
        const decoded = await request.jwtVerify()
        request.user = decoded as any

        // 可选：从缓存验证用户信息
        const userId = (decoded as any).id
        if (userId) {
          const cachedUser = await fastify.redis.get(`user:${userId}`)
          if (cachedUser) {
            request.user = cachedUser
          }
        }

      } catch (jwtError) {
        // 处理JWT相关错误
        if (jwtError.code === 'FST_JWT_AUTHORIZATION_TOKEN_EXPIRED') {
          return reply.status(401).send({
            success: false,
            error: 'Token expired',
            message: 'JWT token has expired, please login again'
          })
        }

        if (jwtError.code === 'FST_JWT_AUTHORIZATION_TOKEN_INVALID') {
          return reply.status(401).send({
            success: false,
            error: 'Invalid token',
            message: 'JWT token is invalid'
          })
        }

        // 其他JWT错误
        return reply.status(401).send({
          success: false,
          error: 'Authentication failed',
          message: 'Failed to authenticate token'
        })
      }

    } catch (error) {
      fastify.log.error('Authentication error:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal server error',
        message: 'Authentication service error'
      })
    }
  })

  // 可选认证中间件（不强制要求认证）
  fastify.decorate('optionalAuth', async function(request: FastifyRequest, reply: FastifyReply) {
    try {
      const authHeader = request.headers.authorization
      if (!authHeader) {
        return // 没有认证头，继续处理请求
      }

      const token = authHeader.replace('Bearer ', '')
      if (!token || token === authHeader) {
        return // 无效的token格式，继续处理请求
      }

      try {
        const decoded = await request.jwtVerify()
        request.user = decoded as any

        // 从缓存获取用户信息
        const userId = (decoded as any).id
        if (userId) {
          const cachedUser = await fastify.redis.get(`user:${userId}`)
          if (cachedUser) {
            request.user = cachedUser
          }
        }
      } catch (jwtError) {
        // JWT验证失败，但不阻止请求继续
        fastify.log.warn('Optional auth failed:', jwtError.message)
      }

    } catch (error) {
      fastify.log.error('Optional authentication error:', error)
      // 不阻止请求继续
    }
  })

  // 角色验证中间件工厂
  fastify.decorate('requireRole', function(requiredRole: string) {
    return async function(request: FastifyRequest, reply: FastifyReply) {
      // 首先确保用户已认证
      await fastify.authenticate(request, reply)
      
      // 检查用户角色
      const user = request.user
      if (!user || user.role !== requiredRole) {
        return reply.status(403).send({
          success: false,
          error: 'Insufficient permissions',
          message: `${requiredRole} role required`
        })
      }
    }
  })

  // 管理员权限验证
  fastify.decorate('requireAdmin', async function(request: FastifyRequest, reply: FastifyReply) {
    await fastify.authenticate(request, reply)
    
    const user = request.user
    if (!user || user.role !== 'admin') {
      return reply.status(403).send({
        success: false,
        error: 'Admin access required',
        message: 'This endpoint requires administrator privileges'
      })
    }
  })

  // 用户权限验证（用户只能访问自己的资源）
  fastify.decorate('requireOwnership', function(userIdParam = 'userId') {
    return async function(request: FastifyRequest, reply: FastifyReply) {
      await fastify.authenticate(request, reply)
      
      const user = request.user
      const params = request.params as any
      const resourceUserId = params[userIdParam]

      // 管理员可以访问所有资源
      if (user?.role === 'admin') {
        return
      }

      // 用户只能访问自己的资源
      if (!user || user.id !== resourceUserId) {
        return reply.status(403).send({
          success: false,
          error: 'Access denied',
          message: 'You can only access your own resources'
        })
      }
    }
  })

  // API密钥认证（用于第三方集成）
  fastify.decorate('authenticateApiKey', async function(request: FastifyRequest, reply: FastifyReply) {
    try {
      const apiKey = request.headers['x-api-key'] as string
      
      if (!apiKey) {
        return reply.status(401).send({
          success: false,
          error: 'API key required',
          message: 'X-API-Key header is required'
        })
      }

      // 验证API密钥（这里应该从数据库或缓存中验证）
      const isValidApiKey = await validateApiKey(apiKey, fastify)
      
      if (!isValidApiKey) {
        return reply.status(401).send({
          success: false,
          error: 'Invalid API key',
          message: 'The provided API key is invalid'
        })
      }

      // 可以在这里设置API密钥相关的用户信息
      // request.apiKey = apiKey

    } catch (error) {
      fastify.log.error('API key authentication error:', error)
      return reply.status(500).send({
        success: false,
        error: 'Internal server error',
        message: 'API key authentication service error'
      })
    }
  })

  // 速率限制豁免检查
  fastify.decorate('checkRateLimitExemption', async function(request: FastifyRequest, reply: FastifyReply) {
    const user = request.user
    
    // 管理员用户豁免速率限制
    if (user?.role === 'admin') {
      request.headers['x-rate-limit-exempt'] = 'true'
    }

    // 高级用户有更高的速率限制
    if (user?.role === 'premium') {
      request.headers['x-rate-limit-tier'] = 'premium'
    }
  })
}

// 验证API密钥的辅助函数
async function validateApiKey(apiKey: string, fastify: any): Promise<boolean> {
  try {
    // 从Redis缓存中检查API密钥
    const cachedKey = await fastify.redis.get(`api_key:${apiKey}`)
    if (cachedKey) {
      return true
    }

    // 从数据库中验证API密钥
    // 这里应该实现实际的数据库查询逻辑
    // const apiKeyRecord = await fastify.database.findApiKey(apiKey)
    // if (apiKeyRecord && apiKeyRecord.active) {
    //   // 缓存有效的API密钥
    //   await fastify.redis.set(`api_key:${apiKey}`, 'valid', 3600)
    //   return true
    // }

    return false
  } catch (error) {
    console.error('API key validation error:', error)
    return false
  }
}

// 创建认证相关的工具函数
export const authUtils = {
  // 从请求中提取用户ID
  getUserId(request: FastifyRequest): string | null {
    return request.user?.id || null
  },

  // 检查用户是否有特定角色
  hasRole(request: FastifyRequest, role: string): boolean {
    return request.user?.role === role
  },

  // 检查用户是否是管理员
  isAdmin(request: FastifyRequest): boolean {
    return request.user?.role === 'admin'
  },

  // 检查用户是否是资源所有者
  isOwner(request: FastifyRequest, resourceUserId: string): boolean {
    return request.user?.id === resourceUserId || this.isAdmin(request)
  },

  // 生成API密钥
  generateApiKey(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = 'sk_'
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }
}

export default fp(authPlugin, {
  fastify: '4.x',
  name: 'auth-plugin'
})
