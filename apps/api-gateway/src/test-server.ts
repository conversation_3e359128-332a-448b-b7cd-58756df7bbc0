import { build, setupErrorHandling, setupGracefulShutdown } from './app'

// 简化的配置用于测试
const testConfig = {
  NODE_ENV: 'development',
  PORT: 8080,
  HOST: '0.0.0.0',
  LOG_LEVEL: 'info',
  
  // 使用内存数据库进行测试
  DATABASE_URL: 'file:./test.db',
  REDIS_URL: 'redis://localhost:6379',
  
  JWT_SECRET: 'test-jwt-secret-key-for-development',
  JWT_EXPIRES_IN: '7d',
  
  CORS_ORIGINS: ['http://localhost:3000', 'http://localhost:3001'],
  
  // 测试用的交易所配置（沙盒模式）
  EXCHANGES: {
    BINANCE: {
      API_KEY: undefined,
      SECRET_KEY: undefined,
      SANDBOX: true
    },
    OKX: {
      API_KEY: undefined,
      SECRET_KEY: undefined,
      PASSPHRASE: undefined,
      SANDBOX: true
    }
  },
  
  PERFORMANCE: {
    ENABLE_METRICS: true,
    METRICS_INTERVAL: 10000
  },
  
  CACHE: {
    TTL: 300,
    MAX_SIZE: 1000
  },
  
  WS_HEARTBEAT_INTERVAL: 15000,
  WS_HEARTBEAT_TIMEOUT: 30000,
  WS_MAX_CONNECTIONS: 1000,
  
  SECURITY: {
    ENABLE_HELMET: true,
    ENABLE_RATE_LIMIT: true,
    ENABLE_CORS: true
  },
  
  DEVELOPMENT: {
    ENABLE_SWAGGER: true,
    ENABLE_PLAYGROUND: false
  }
}

// 模拟配置模块
const mockConfig = {
  ...testConfig,
  RATE_LIMIT_MAX: 1000,
  RATE_LIMIT_WINDOW: '1 minute',
  ENCRYPTION_KEY: 'test-encryption-key-32-characters'
}

// 替换配置模块
jest.doMock('./config', () => ({
  config: mockConfig
}))

async function startTestServer() {
  try {
    console.log('🚀 Starting SFQuant API Gateway Test Server...')
    
    // 构建 Fastify 应用
    const fastify = await build({
      logger: {
        level: 'info',
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'HH:MM:ss Z',
            ignore: 'pid,hostname'
          }
        }
      }
    })

    // 设置错误处理
    setupErrorHandling(fastify)

    // 设置优雅关闭
    setupGracefulShutdown(fastify)

    // 启动服务器
    await fastify.listen({
      port: testConfig.PORT,
      host: testConfig.HOST
    })

    console.log(`
╔══════════════════════════════════════════════════════════════╗
║                🚀 SFQuant API Gateway Test Server            ║
║                                                              ║
║  🌐 Server: http://${testConfig.HOST}:${testConfig.PORT.toString().padEnd(43)} ║
║  📚 Docs:   http://${testConfig.HOST}:${testConfig.PORT}/docs${' '.repeat(35)} ║
║  🔌 WebSocket: ws://${testConfig.HOST}:${testConfig.PORT}/ws${' '.repeat(37)} ║
║  📊 Metrics: http://${testConfig.HOST}:${testConfig.PORT}/api/v1/metrics/performance${' '.repeat(11)} ║
║                                                              ║
║  Environment: ${testConfig.NODE_ENV.toUpperCase().padEnd(47)} ║
║  Log Level:   ${testConfig.LOG_LEVEL.toUpperCase().padEnd(47)} ║
║                                                              ║
║  ⚠️  TEST MODE - Using simplified configuration               ║
║  🎯 Ready for testing quantitative trading operations!       ║
╚══════════════════════════════════════════════════════════════╝
    `)

    // 输出可用的API端点
    console.log('\n📡 Available API Endpoints:')
    console.log('  GET  /                          - API Gateway info')
    console.log('  GET  /health                    - Health check')
    console.log('  GET  /health/detailed           - Detailed health check')
    console.log('  GET  /docs                      - API Documentation')
    console.log('  POST /api/v1/auth/register      - User registration')
    console.log('  POST /api/v1/auth/login         - User login')
    console.log('  GET  /api/v1/market-data/price/:symbol - Get price')
    console.log('  GET  /api/v1/strategies         - Get strategies (auth required)')
    console.log('  GET  /api/v1/metrics/performance - Performance metrics')
    console.log('  WS   /ws                        - WebSocket connection')
    console.log('  WS   /ws/market-data           - Market data WebSocket')
    console.log('  WS   /ws/strategy               - Strategy WebSocket')

    console.log('\n🧪 Test Commands:')
    console.log('  curl http://localhost:8080/health')
    console.log('  curl http://localhost:8080/api/v1/market-data/price/BTC/USDT')
    console.log('  curl http://localhost:8080/api/v1/metrics/performance')

  } catch (error) {
    console.error('❌ Failed to start test server:', error)
    process.exit(1)
  }
}

// 启动测试服务器
if (require.main === module) {
  startTestServer()
}

export { startTestServer, testConfig }
