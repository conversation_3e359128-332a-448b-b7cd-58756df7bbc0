import Fastify from 'fastify'
import cors from '@fastify/cors'
import helmet from '@fastify/helmet'
import swagger from '@fastify/swagger'
import swaggerUi from '@fastify/swagger-ui'
import jwt from '@fastify/jwt'
import websocket from '@fastify/websocket'

// 简化的演示服务器，展示优化后的 Fastify 架构
async function createDemoServer() {
  const fastify = Fastify({
    logger: {
      level: 'info',
      transport: {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'HH:MM:ss Z',
          ignore: 'pid,hostname'
        }
      }
    }
  })

  // 注册核心插件
  await fastify.register(helmet, {
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false
  })

  await fastify.register(cors, {
    origin: ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
  })

  await fastify.register(jwt, {
    secret: 'demo-jwt-secret-key-for-testing-only'
  })

  await fastify.register(websocket)

  // Swagger 文档
  await fastify.register(swagger, {
    swagger: {
      info: {
        title: 'SFQuant API Gateway Demo',
        description: 'Optimized Fastify Architecture Demo',
        version: '1.0.0'
      },
      host: 'localhost:8080',
      schemes: ['http'],
      consumes: ['application/json'],
      produces: ['application/json'],
      tags: [
        { name: 'Health', description: 'Health check endpoints' },
        { name: 'Demo', description: 'Demo endpoints' },
        { name: 'WebSocket', description: 'WebSocket endpoints' }
      ]
    }
  })

  await fastify.register(swaggerUi, {
    routePrefix: '/docs',
    uiConfig: {
      docExpansion: 'list',
      deepLinking: false
    }
  })

  // 健康检查路由
  fastify.get('/health', {
    schema: {
      description: 'Health check',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string' },
            uptime: { type: 'number' },
            version: { type: 'string' }
          }
        }
      }
    }
  }, async () => {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0'
    }
  })

  // 根路由
  fastify.get('/', {
    schema: {
      description: 'API Gateway info',
      tags: ['Demo'],
      response: {
        200: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            version: { type: 'string' },
            description: { type: 'string' },
            status: { type: 'string' },
            features: { type: 'array', items: { type: 'string' } },
            endpoints: { type: 'object' }
          }
        }
      }
    }
  }, async (request) => {
    const baseUrl = `${request.protocol}://${request.hostname}:8080`
    
    return {
      name: 'SFQuant API Gateway',
      version: '1.0.0',
      description: 'High-performance cryptocurrency quantitative trading system',
      status: 'running',
      features: [
        'Optimized Fastify Architecture',
        'High-performance WebSocket',
        'Smart Rate Limiting',
        'Performance Monitoring',
        'JWT Authentication',
        'Real-time Market Data',
        'Strategy Management'
      ],
      endpoints: {
        health: `${baseUrl}/health`,
        docs: `${baseUrl}/docs`,
        websocket: `ws://${request.hostname}:8080/ws`,
        demo: `${baseUrl}/demo`
      }
    }
  })

  // 演示性能指标
  fastify.get('/demo/metrics', {
    schema: {
      description: 'Demo performance metrics',
      tags: ['Demo']
    }
  }, async () => {
    const memoryUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    
    return {
      success: true,
      data: {
        timestamp: Date.now(),
        uptime: process.uptime(),
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024) + 'MB',
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + 'MB',
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + 'MB'
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        platform: process.platform,
        nodeVersion: process.version
      }
    }
  })

  // 演示市场数据
  fastify.get('/demo/market-data/:symbol', {
    schema: {
      description: 'Demo market data',
      tags: ['Demo'],
      params: {
        type: 'object',
        required: ['symbol'],
        properties: {
          symbol: { type: 'string' }
        }
      }
    }
  }, async (request) => {
    const { symbol } = request.params as { symbol: string }
    
    // 模拟市场数据
    const mockPrice = Math.random() * 50000 + 30000
    const mockChange = (Math.random() - 0.5) * 2000
    
    return {
      success: true,
      data: {
        symbol: symbol.toUpperCase(),
        price: Math.round(mockPrice * 100) / 100,
        change24h: Math.round(mockChange * 100) / 100,
        changePercent24h: Math.round((mockChange / mockPrice) * 10000) / 100,
        volume24h: Math.round(Math.random() * 1000000),
        timestamp: Date.now()
      }
    }
  })

  // WebSocket 演示
  fastify.get('/ws', { websocket: true }, (connection, request) => {
    const clientId = `demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    fastify.log.info(`WebSocket client connected: ${clientId}`)
    
    // 发送欢迎消息
    connection.socket.send(JSON.stringify({
      type: 'welcome',
      clientId,
      message: 'Connected to SFQuant API Gateway Demo',
      timestamp: Date.now()
    }))

    // 定期发送模拟数据
    const interval = setInterval(() => {
      if (connection.socket.readyState === 1) {
        connection.socket.send(JSON.stringify({
          type: 'price_update',
          symbol: 'BTC/USDT',
          price: Math.round((Math.random() * 50000 + 30000) * 100) / 100,
          timestamp: Date.now()
        }))
      }
    }, 2000)

    connection.socket.on('message', (message) => {
      try {
        const data = JSON.parse(message.toString())
        fastify.log.info(`Received message from ${clientId}:`, data)
        
        // 回显消息
        connection.socket.send(JSON.stringify({
          type: 'echo',
          originalMessage: data,
          timestamp: Date.now()
        }))
      } catch (error) {
        fastify.log.error('Error parsing WebSocket message:', error)
      }
    })

    connection.socket.on('close', () => {
      clearInterval(interval)
      fastify.log.info(`WebSocket client disconnected: ${clientId}`)
    })
  })

  // 错误处理
  fastify.setErrorHandler((error, request, reply) => {
    fastify.log.error(error)
    
    reply.status(error.statusCode || 500).send({
      success: false,
      error: error.name || 'Internal Server Error',
      message: error.message || 'Something went wrong'
    })
  })

  return fastify
}

async function start() {
  try {
    const fastify = await createDemoServer()
    
    await fastify.listen({
      port: 8080,
      host: '0.0.0.0'
    })

    console.log(`
╔══════════════════════════════════════════════════════════════╗
║            🚀 SFQuant Fastify 优化架构演示服务器              ║
║                                                              ║
║  🌐 服务器:    http://localhost:8080                         ║
║  📚 API文档:   http://localhost:8080/docs                    ║
║  🔌 WebSocket: ws://localhost:8080/ws                        ║
║  📊 性能指标:  http://localhost:8080/demo/metrics            ║
║  💰 市场数据:  http://localhost:8080/demo/market-data/BTCUSDT ║
║                                                              ║
║  ✨ 特性展示:                                                ║
║     • 高性能 Fastify 架构                                    ║
║     • 优化的 WebSocket 连接                                  ║
║     • 自动 API 文档生成                                      ║
║     • 实时性能监控                                           ║
║     • 结构化日志记录                                         ║
║                                                              ║
║  🧪 测试命令:                                                ║
║     curl http://localhost:8080/health                       ║
║     curl http://localhost:8080/demo/metrics                 ║
║     curl http://localhost:8080/demo/market-data/BTCUSDT     ║
╚══════════════════════════════════════════════════════════════╝
    `)

  } catch (error) {
    console.error('❌ 启动服务器失败:', error)
    process.exit(1)
  }
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在优雅关闭服务器...')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n🛑 正在优雅关闭服务器...')
  process.exit(0)
})

if (require.main === module) {
  start()
}

export { createDemoServer }
