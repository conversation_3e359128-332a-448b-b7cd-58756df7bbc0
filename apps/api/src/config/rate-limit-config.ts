import { FastifyInstance } from 'fastify'

// 量化交易系统的速率限制配置
export interface RateLimitConfig {
  // 全局限制
  global: {
    max: number
    timeWindow: number
  }
  
  // 按端点类型的限制
  endpoints: {
    [key: string]: {
      max: number
      timeWindow: number
      skipOnError?: boolean
      continueExceeding?: boolean
    }
  }
  
  // 按用户类型的限制
  userTypes: {
    [key: string]: {
      max: number
      timeWindow: number
    }
  }
}

// 针对量化交易的速率限制配置
export const tradingRateLimitConfig: RateLimitConfig = {
  // 全局默认限制
  global: {
    max: 1000,           // 每分钟1000次请求
    timeWindow: 60000    // 1分钟窗口
  },
  
  // 按端点类型的精细化限制
  endpoints: {
    // 认证相关 - 较严格
    'auth': {
      max: 10,           // 每分钟10次
      timeWindow: 60000,
      skipOnError: false,
      continueExceeding: false
    },
    
    // 市场数据 - 高频访问
    'market-data': {
      max: 500,          // 每分钟500次
      timeWindow: 60000,
      skipOnError: true,
      continueExceeding: true
    },
    
    // 交易执行 - 中等限制
    'trading': {
      max: 100,          // 每分钟100次
      timeWindow: 60000,
      skipOnError: false,
      continueExceeding: false
    },
    
    // 策略管理 - 较宽松
    'strategies': {
      max: 200,          // 每分钟200次
      timeWindow: 60000,
      skipOnError: true,
      continueExceeding: true
    },
    
    // 回测功能 - 限制较严
    'backtest': {
      max: 20,           // 每分钟20次
      timeWindow: 60000,
      skipOnError: false,
      continueExceeding: false
    },
    
    // 高级功能 - 中等限制
    'advanced': {
      max: 50,           // 每分钟50次
      timeWindow: 60000,
      skipOnError: true,
      continueExceeding: true
    },
    
    // WebSocket连接 - 连接限制
    'websocket': {
      max: 10,           // 每分钟10个新连接
      timeWindow: 60000,
      skipOnError: false,
      continueExceeding: false
    }
  },
  
  // 按用户类型的限制
  userTypes: {
    // 免费用户
    'free': {
      max: 100,          // 每分钟100次
      timeWindow: 60000
    },
    
    // 高级用户
    'premium': {
      max: 500,          // 每分钟500次
      timeWindow: 60000
    },
    
    // 开发者用户
    'developer': {
      max: 1000,         // 每分钟1000次
      timeWindow: 60000
    },
    
    // 管理员
    'admin': {
      max: 10000,        // 每分钟10000次
      timeWindow: 60000
    }
  }
}

// 自定义密钥生成器 - 基于用户ID和IP
export function customKeyGenerator(request: any): string {
  // 优先使用用户ID（已认证用户）
  if (request.user?.id) {
    return `user:${request.user.id}`
  }
  
  // 回退到IP地址（未认证用户）
  return `ip:${request.ip}`
}

// 自定义错误响应构建器
export function customErrorResponseBuilder(request: any, context: any) {
  return {
    statusCode: 429,
    error: 'Too Many Requests',
    message: `Rate limit exceeded. Maximum ${context.max} requests per ${Math.floor(context.after / 1000)} seconds.`,
    retryAfter: Math.ceil(context.ttl / 1000),
    timestamp: new Date().toISOString(),
    endpoint: request.routerPath || request.url,
    limit: context.max,
    remaining: Math.max(0, context.max - context.current),
    resetTime: new Date(Date.now() + context.ttl).toISOString()
  }
}

// 动态最大请求数计算器
export async function dynamicMaxCalculator(request: any, key: string): Promise<number> {
  // 基于用户类型调整限制
  const userType = request.user?.role || 'free'
  const baseLimit = tradingRateLimitConfig.userTypes[userType]?.max || 100
  
  // 基于时间段调整（交易时间 vs 非交易时间）
  const hour = new Date().getHours()
  const isTradingHours = hour >= 9 && hour <= 16 // 简化的交易时间
  
  if (isTradingHours) {
    return Math.floor(baseLimit * 1.5) // 交易时间增加50%限制
  }
  
  return baseLimit
}

// 白名单检查函数
export function allowListChecker(request: any, key: string): boolean {
  // 管理员用户绕过限制
  if (request.user?.role === 'admin') {
    return true
  }
  
  // 内部服务调用绕过限制
  if (request.headers['x-internal-service'] === 'true') {
    return true
  }
  
  // 特定IP白名单
  const whitelistedIPs = [
    '127.0.0.1',
    '::1',
    // 添加其他白名单IP
  ]
  
  return whitelistedIPs.includes(request.ip)
}

// 超限回调函数
export function onExceededCallback(request: any, key: string) {
  // 记录超限事件
  console.log(`Rate limit exceeded for key: ${key}, endpoint: ${request.url}, time: ${new Date().toISOString()}`)
  
  // 可以在这里添加额外的监控或告警逻辑
  // 例如：发送到监控系统、记录到数据库等
}

// 接近限制回调函数
export function onExceedingCallback(request: any, key: string) {
  // 记录接近限制的事件
  console.log(`Approaching rate limit for key: ${key}, endpoint: ${request.url}`)
}

// 应用速率限制配置到 Fastify 实例
export async function applyRateLimitConfig(fastify: FastifyInstance) {
  // 注册全局速率限制
  await fastify.register(require('@fastify/rate-limit'), {
    global: false, // 我们将手动应用到特定路由
    max: dynamicMaxCalculator,
    timeWindow: tradingRateLimitConfig.global.timeWindow,
    keyGenerator: customKeyGenerator,
    errorResponseBuilder: customErrorResponseBuilder,
    allowList: allowListChecker,
    onExceeded: onExceededCallback,
    onExceeding: onExceedingCallback,
    skipOnError: true,
    continueExceeding: true,
    enableDraftSpec: true, // 使用标准的速率限制头
    addHeaders: {
      'x-ratelimit-limit': true,
      'x-ratelimit-remaining': true,
      'x-ratelimit-reset': true,
      'retry-after': true
    }
  })
  
  // 为不同的路由组应用不同的限制
  await applyEndpointSpecificLimits(fastify)
}

// 应用端点特定的限制
async function applyEndpointSpecificLimits(fastify: FastifyInstance) {
  // 认证路由的严格限制
  fastify.register(async function(instance) {
    await instance.register(require('@fastify/rate-limit'), {
      max: tradingRateLimitConfig.endpoints.auth.max,
      timeWindow: tradingRateLimitConfig.endpoints.auth.timeWindow,
      keyGenerator: customKeyGenerator,
      errorResponseBuilder: customErrorResponseBuilder
    })
  }, { prefix: '/api/v1/auth' })
  
  // 市场数据路由的高频限制
  fastify.register(async function(instance) {
    await instance.register(require('@fastify/rate-limit'), {
      max: tradingRateLimitConfig.endpoints['market-data'].max,
      timeWindow: tradingRateLimitConfig.endpoints['market-data'].timeWindow,
      keyGenerator: customKeyGenerator,
      skipOnError: true
    })
  }, { prefix: '/api/v1/market-data' })
  
  // 交易路由的中等限制
  fastify.register(async function(instance) {
    await instance.register(require('@fastify/rate-limit'), {
      max: tradingRateLimitConfig.endpoints.trading.max,
      timeWindow: tradingRateLimitConfig.endpoints.trading.timeWindow,
      keyGenerator: customKeyGenerator,
      errorResponseBuilder: customErrorResponseBuilder
    })
  }, { prefix: '/api/v1/trading' })
}

// 导出配置和函数
export {
  tradingRateLimitConfig as default,
  customKeyGenerator,
  customErrorResponseBuilder,
  dynamicMaxCalculator,
  allowListChecker,
  onExceededCallback,
  onExceedingCallback,
  applyRateLimitConfig
}
