import { FastifyInstance, FastifyRequest } from 'fastify'
import rateLimit from '@fastify/rate-limit'

// 量化交易专用速率限制配置
export class AdvancedRateLimitConfig {
  
  // 为不同类型的用户设置不同的限制
  static getUserTierLimits(userTier: string) {
    const limits = {
      'free': { max: 100, timeWindow: '1 minute' },
      'premium': { max: 500, timeWindow: '1 minute' },
      'pro': { max: 2000, timeWindow: '1 minute' },
      'enterprise': { max: 10000, timeWindow: '1 minute' }
    }
    return limits[userTier as keyof typeof limits] || limits.free
  }

  // 为不同的API端点设置不同的限制
  static getEndpointLimits(endpoint: string) {
    const endpointLimits = {
      // 认证相关 - 严格限制
      '/api/v1/auth/login': { max: 5, timeWindow: '5 minutes' },
      '/api/v1/auth/register': { max: 3, timeWindow: '10 minutes' },
      
      // 市场数据 - 中等限制
      '/api/v1/market-data/*': { max: 200, timeWindow: '1 minute' },
      
      // 交易相关 - 较宽松但有控制
      '/api/v1/strategies/*': { max: 100, timeWindow: '1 minute' },
      
      // 高级功能 - 更严格
      '/api/v1/advanced/backtest': { max: 10, timeWindow: '5 minutes' },
      '/api/v1/advanced/technical-analysis': { max: 50, timeWindow: '1 minute' },
      
      // 健康检查 - 宽松
      '/api/v1/health/*': { max: 1000, timeWindow: '1 minute' }
    }

    // 查找匹配的端点模式
    for (const [pattern, limits] of Object.entries(endpointLimits)) {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace('*', '.*'))
        if (regex.test(endpoint)) {
          return limits
        }
      } else if (endpoint === pattern) {
        return limits
      }
    }

    // 默认限制
    return { max: 100, timeWindow: '1 minute' }
  }

  // 智能密钥生成器 - 基于多个因素
  static createKeyGenerator() {
    return function(request: FastifyRequest) {
      // 优先使用用户ID（如果已认证）
      if (request.user?.id) {
        return `user:${request.user.id}`
      }

      // 使用API密钥（如果提供）
      const apiKey = request.headers['x-api-key'] as string
      if (apiKey) {
        return `api:${apiKey}`
      }

      // 检查可信的代理头
      const realIP = request.headers['x-real-ip'] as string
      const forwardedFor = request.headers['x-forwarded-for'] as string
      const clientIP = request.headers['x-client-ip'] as string

      // 使用最可信的IP
      return realIP || clientIP || (forwardedFor?.split(',')[0]) || request.ip
    }
  }

  // 动态最大请求数计算
  static createDynamicMaxCalculator() {
    return async function(request: FastifyRequest, key: string) {
      // 基于用户等级调整限制
      if (request.user?.tier) {
        const tierLimits = AdvancedRateLimitConfig.getUserTierLimits(request.user.tier)
        return tierLimits.max
      }

      // 基于API密钥类型调整
      const apiKey = request.headers['x-api-key'] as string
      if (apiKey) {
        // 这里可以查询数据库获取API密钥的限制
        // 暂时返回默认值
        return 200
      }

      // 基于时间段调整（交易时间 vs 非交易时间）
      const hour = new Date().getHours()
      const isTradingHours = hour >= 9 && hour <= 16 // 简化的交易时间
      
      return isTradingHours ? 150 : 100
    }
  }

  // 自定义错误响应构建器
  static createErrorResponseBuilder() {
    return function(request: FastifyRequest, context: any) {
      const retryAfter = Math.ceil(context.ttl / 1000)
      
      return {
        statusCode: 429,
        error: 'Rate Limit Exceeded',
        message: `Too many requests. You have exceeded the limit of ${context.max} requests per ${context.after}. Please try again in ${retryAfter} seconds.`,
        details: {
          limit: context.max,
          window: context.after,
          retryAfter: retryAfter,
          endpoint: request.url,
          timestamp: new Date().toISOString()
        }
      }
    }
  }

  // 白名单函数
  static createAllowListFunction() {
    return function(request: FastifyRequest, key: string) {
      // 内部服务调用
      const internalToken = request.headers['x-internal-service'] as string
      if (internalToken === process.env.INTERNAL_SERVICE_TOKEN) {
        return true
      }

      // 管理员用户
      if (request.user?.role === 'admin') {
        return true
      }

      // 健康检查端点
      if (request.url.includes('/health')) {
        return true
      }

      return false
    }
  }

  // 超限回调 - 用于监控和告警
  static createOnExceededCallback() {
    return function(request: FastifyRequest, key: string) {
      // 记录超限事件
      console.warn(`Rate limit exceeded for key: ${key}, endpoint: ${request.url}, IP: ${request.ip}`)
      
      // 这里可以集成告警系统
      // alertingService.sendAlert({
      //   type: 'rate_limit_exceeded',
      //   key,
      //   endpoint: request.url,
      //   ip: request.ip,
      //   timestamp: new Date()
      // })
    }
  }

  // 接近限制回调 - 用于预警
  static createOnExceedingCallback() {
    return function(request: FastifyRequest, key: string) {
      // 当达到80%限制时发出预警
      console.info(`Rate limit warning for key: ${key}, endpoint: ${request.url}`)
    }
  }
}

// 注册高级速率限制中间件
export async function registerAdvancedRateLimit(fastify: FastifyInstance) {
  // 全局速率限制
  await fastify.register(rateLimit, {
    global: true,
    max: AdvancedRateLimitConfig.createDynamicMaxCalculator(),
    timeWindow: '1 minute',
    keyGenerator: AdvancedRateLimitConfig.createKeyGenerator(),
    errorResponseBuilder: AdvancedRateLimitConfig.createErrorResponseBuilder(),
    allowList: AdvancedRateLimitConfig.createAllowListFunction(),
    onExceeded: AdvancedRateLimitConfig.createOnExceededCallback(),
    onExceeding: AdvancedRateLimitConfig.createOnExceedingCallback(),
    
    // 启用草案规范头部
    enableDraftSpec: true,
    
    // 配置响应头
    addHeaders: {
      'x-ratelimit-limit': true,
      'x-ratelimit-remaining': true,
      'x-ratelimit-reset': true,
      'retry-after': true
    },
    
    // 在接近限制时也显示头部
    addHeadersOnExceeding: {
      'x-ratelimit-limit': true,
      'x-ratelimit-remaining': true,
      'x-ratelimit-reset': true
    },

    // 跳过错误时的限制（避免级联失败）
    skipOnError: true,

    // 继续计数即使超过限制（用于统计）
    continueExceeding: true
  })

  // 为特定路由组注册专门的限制
  await fastify.register(async function(fastify) {
    // 认证路由的严格限制
    await fastify.register(rateLimit, {
      max: 5,
      timeWindow: '5 minutes',
      keyGenerator: (request) => {
        // 认证路由使用IP + User-Agent组合
        return `auth:${request.ip}:${request.headers['user-agent']}`
      },
      errorResponseBuilder: (request, context) => ({
        statusCode: 429,
        error: 'Authentication Rate Limit Exceeded',
        message: 'Too many authentication attempts. Please wait before trying again.',
        retryAfter: Math.ceil(context.ttl / 1000)
      })
    })
  }, { prefix: '/api/v1/auth' })

  // 市场数据路由的优化限制
  await fastify.register(async function(fastify) {
    await fastify.register(rateLimit, {
      max: async (request) => {
        // 基于订阅的符号数量动态调整
        const symbolCount = request.query?.symbols?.split(',').length || 1
        return Math.min(500, 50 * symbolCount)
      },
      timeWindow: '1 minute',
      groupId: 'market-data' // 将所有市场数据请求归为一组
    })
  }, { prefix: '/api/v1/market-data' })

  // 回测功能的特殊限制
  await fastify.register(async function(fastify) {
    await fastify.register(rateLimit, {
      max: 3, // 回测很消耗资源，严格限制
      timeWindow: '10 minutes',
      keyGenerator: (request) => {
        // 回测按用户限制
        return request.user?.id || request.ip
      },
      ban: 5, // 5次超限后临时封禁
      onBanReach: (request, key) => {
        console.warn(`User ${key} has been temporarily banned for excessive backtest requests`)
      }
    })
  }, { prefix: '/api/v1/advanced/backtest' })
}

// 类型声明扩展
declare module 'fastify' {
  interface FastifyRequest {
    user?: {
      id: string
      tier: string
      role: string
    }
  }
}
