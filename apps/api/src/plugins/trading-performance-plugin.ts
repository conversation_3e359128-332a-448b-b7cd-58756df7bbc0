import fp from 'fastify-plugin'
import { FastifyInstance, FastifyPluginOptions } from 'fastify'

// 性能监控接口
interface PerformanceMetrics {
  requestCount: number
  averageResponseTime: number
  errorRate: number
  activeConnections: number
  memoryUsage: NodeJS.MemoryUsage
  cpuUsage: NodeJS.CpuUsage
  timestamp: number
}

// 交易性能监控插件选项
interface TradingPerformanceOptions extends FastifyPluginOptions {
  enableMetrics?: boolean
  enableTracing?: boolean
  metricsInterval?: number
  alertThresholds?: {
    responseTime?: number
    errorRate?: number
    memoryUsage?: number
  }
}

// 交易性能监控插件
async function tradingPerformancePlugin(
  fastify: FastifyInstance,
  options: TradingPerformanceOptions
) {
  const {
    enableMetrics = true,
    enableTracing = true,
    metricsInterval = 10000, // 10秒
    alertThresholds = {
      responseTime: 1000,    // 1秒
      errorRate: 0.05,       // 5%
      memoryUsage: 512 * 1024 * 1024 // 512MB
    }
  } = options

  // 性能指标存储
  const metrics = {
    requests: new Map<string, number>(),
    responseTimes: new Map<string, number[]>(),
    errors: new Map<string, number>(),
    startTime: Date.now(),
    lastMetricsReset: Date.now()
  }

  // 装饰 Fastify 实例
  fastify.decorate('performanceMetrics', {
    getMetrics: () => calculateMetrics(),
    resetMetrics: () => resetMetrics(),
    getAlerts: () => checkAlerts()
  })

  // 请求开始时间追踪
  fastify.addHook('onRequest', async (request, reply) => {
    if (enableTracing) {
      request.startTime = process.hrtime.bigint()
    }
  })

  // 响应时间和错误追踪
  fastify.addHook('onResponse', async (request, reply) => {
    if (!enableMetrics) return

    const route = request.routerPath || request.url
    const method = request.method
    const key = `${method} ${route}`

    // 更新请求计数
    metrics.requests.set(key, (metrics.requests.get(key) || 0) + 1)

    // 计算响应时间
    if (enableTracing && request.startTime) {
      const responseTime = Number(process.hrtime.bigint() - request.startTime) / 1000000 // 转换为毫秒
      
      if (!metrics.responseTimes.has(key)) {
        metrics.responseTimes.set(key, [])
      }
      metrics.responseTimes.get(key)!.push(responseTime)
      
      // 保持最近100个响应时间记录
      const times = metrics.responseTimes.get(key)!
      if (times.length > 100) {
        times.shift()
      }
    }

    // 错误追踪
    if (reply.statusCode >= 400) {
      metrics.errors.set(key, (metrics.errors.get(key) || 0) + 1)
    }
  })

  // 错误处理钩子
  fastify.addHook('onError', async (request, reply, error) => {
    if (!enableMetrics) return

    const route = request.routerPath || request.url
    const method = request.method
    const key = `${method} ${route}`

    metrics.errors.set(key, (metrics.errors.get(key) || 0) + 1)

    // 记录严重错误
    fastify.log.error({
      route: key,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    }, 'Request error occurred')
  })

  // 计算性能指标
  function calculateMetrics(): PerformanceMetrics {
    const now = Date.now()
    const uptime = now - metrics.startTime
    
    // 计算总请求数
    let totalRequests = 0
    metrics.requests.forEach(count => totalRequests += count)

    // 计算平均响应时间
    let totalResponseTime = 0
    let responseTimeCount = 0
    metrics.responseTimes.forEach(times => {
      times.forEach(time => {
        totalResponseTime += time
        responseTimeCount++
      })
    })
    const averageResponseTime = responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0

    // 计算错误率
    let totalErrors = 0
    metrics.errors.forEach(count => totalErrors += count)
    const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0

    // 获取系统资源使用情况
    const memoryUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()

    return {
      requestCount: totalRequests,
      averageResponseTime,
      errorRate,
      activeConnections: fastify.server.connections || 0,
      memoryUsage,
      cpuUsage,
      timestamp: now
    }
  }

  // 重置指标
  function resetMetrics() {
    metrics.requests.clear()
    metrics.responseTimes.clear()
    metrics.errors.clear()
    metrics.lastMetricsReset = Date.now()
  }

  // 检查告警
  function checkAlerts() {
    const currentMetrics = calculateMetrics()
    const alerts = []

    // 响应时间告警
    if (currentMetrics.averageResponseTime > alertThresholds.responseTime!) {
      alerts.push({
        type: 'HIGH_RESPONSE_TIME',
        message: `Average response time ${currentMetrics.averageResponseTime.toFixed(2)}ms exceeds threshold ${alertThresholds.responseTime}ms`,
        severity: 'WARNING',
        timestamp: Date.now()
      })
    }

    // 错误率告警
    if (currentMetrics.errorRate > alertThresholds.errorRate!) {
      alerts.push({
        type: 'HIGH_ERROR_RATE',
        message: `Error rate ${(currentMetrics.errorRate * 100).toFixed(2)}% exceeds threshold ${(alertThresholds.errorRate! * 100).toFixed(2)}%`,
        severity: 'CRITICAL',
        timestamp: Date.now()
      })
    }

    // 内存使用告警
    if (currentMetrics.memoryUsage.heapUsed > alertThresholds.memoryUsage!) {
      alerts.push({
        type: 'HIGH_MEMORY_USAGE',
        message: `Memory usage ${Math.round(currentMetrics.memoryUsage.heapUsed / 1024 / 1024)}MB exceeds threshold ${Math.round(alertThresholds.memoryUsage! / 1024 / 1024)}MB`,
        severity: 'WARNING',
        timestamp: Date.now()
      })
    }

    return alerts
  }

  // 定期指标收集和告警检查
  if (enableMetrics) {
    const metricsTimer = setInterval(() => {
      const currentMetrics = calculateMetrics()
      const alerts = checkAlerts()

      // 记录指标
      fastify.log.info({
        metrics: currentMetrics,
        alerts: alerts.length > 0 ? alerts : undefined
      }, 'Performance metrics collected')

      // 发送告警（如果有）
      if (alerts.length > 0) {
        alerts.forEach(alert => {
          fastify.log.warn(alert, 'Performance alert triggered')
        })
      }
    }, metricsInterval)

    // 清理定时器
    fastify.addHook('onClose', async () => {
      clearInterval(metricsTimer)
    })
  }

  // 添加性能监控路由
  fastify.get('/metrics/performance', {
    schema: {
      description: 'Get current performance metrics',
      tags: ['monitoring'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                requestCount: { type: 'number' },
                averageResponseTime: { type: 'number' },
                errorRate: { type: 'number' },
                activeConnections: { type: 'number' },
                memoryUsage: { type: 'object' },
                cpuUsage: { type: 'object' },
                timestamp: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    return {
      success: true,
      data: calculateMetrics()
    }
  })

  // 添加告警检查路由
  fastify.get('/metrics/alerts', {
    schema: {
      description: 'Get current performance alerts',
      tags: ['monitoring']
    }
  }, async (request, reply) => {
    return {
      success: true,
      data: checkAlerts()
    }
  })

  // 添加指标重置路由
  fastify.post('/metrics/reset', {
    schema: {
      description: 'Reset performance metrics',
      tags: ['monitoring']
    }
  }, async (request, reply) => {
    resetMetrics()
    return {
      success: true,
      message: 'Performance metrics reset successfully'
    }
  })
}

// 使用 fastify-plugin 包装以保持作用域
export default fp(tradingPerformancePlugin, {
  fastify: '4.x',
  name: 'trading-performance-plugin'
})

// 类型声明
declare module 'fastify' {
  interface FastifyInstance {
    performanceMetrics: {
      getMetrics(): PerformanceMetrics
      resetMetrics(): void
      getAlerts(): Array<{
        type: string
        message: string
        severity: string
        timestamp: number
      }>
    }
  }
  
  interface FastifyRequest {
    startTime?: bigint
  }
}
