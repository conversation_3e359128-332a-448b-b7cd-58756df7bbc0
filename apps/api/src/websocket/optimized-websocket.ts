import { FastifyInstance } from 'fastify'
import { WebSocket } from 'ws'

interface OptimizedWebSocketClient {
  id: string
  socket: WebSocket
  subscriptions: Set<string>
  lastPing: number
  authenticated: boolean
  userId?: string
  rateLimit: {
    count: number
    resetTime: number
  }
}

// 高性能 WebSocket 管理器
export class OptimizedWebSocketManager {
  private clients = new Map<string, OptimizedWebSocketClient>()
  private subscriptions = new Map<string, Set<string>>() // symbol -> clientIds
  private rateLimitConfig = {
    maxRequests: 100,
    windowMs: 60000 // 1分钟
  }

  constructor(private fastify: FastifyInstance) {}

  // 注册优化的 WebSocket 路由
  async registerRoutes() {
    // 主要的 WebSocket 连接端点
    this.fastify.get('/ws', {
      websocket: true,
      preValidation: this.preValidationHook.bind(this)
    }, this.handleConnection.bind(this))

    // 专用的市场数据流
    this.fastify.get('/ws/market-data', {
      websocket: true,
      preValidation: this.preValidationHook.bind(this)
    }, this.handleMarketDataConnection.bind(this))

    // 专用的策略执行流
    this.fastify.get('/ws/strategy', {
      websocket: true,
      preValidation: this.preValidationHook.bind(this)
    }, this.handleStrategyConnection.bind(this))
  }

  // 预验证钩子 - 在建立 WebSocket 连接前进行认证
  private async preValidationHook(request: any, reply: any) {
    const token = request.headers.authorization?.replace('Bearer ', '')

    if (!token) {
      await reply.code(401).send({ error: 'Authentication required' })
      return
    }

    try {
      const decoded = this.fastify.jwt.verify(token)
      request.user = decoded
    } catch (error) {
      await reply.code(401).send({ error: 'Invalid token' })
    }
  }

  // 处理主要 WebSocket 连接
  private handleConnection(socket: WebSocket, request: any) {
    const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const client: OptimizedWebSocketClient = {
      id: clientId,
      socket,
      subscriptions: new Set(),
      lastPing: Date.now(),
      authenticated: !!request.user,
      userId: request.user?.id,
      rateLimit: {
        count: 0,
        resetTime: Date.now() + this.rateLimitConfig.windowMs
      }
    }

    this.clients.set(clientId, client)
    this.fastify.log.info(`WebSocket client connected: ${clientId}`)

    // 发送连接确认
    this.sendMessage(socket, {
      type: 'connection_established',
      clientId,
      timestamp: Date.now(),
      authenticated: client.authenticated
    })

    // 设置消息处理器
    socket.on('message', (data) => this.handleMessage(client, data))
    socket.on('close', () => this.handleDisconnection(client))
    socket.on('error', (error) => this.handleError(client, error))

    // 设置心跳检测
    this.setupHeartbeat(client)
  }

  // 处理市场数据专用连接
  private handleMarketDataConnection(socket: WebSocket, request: any) {
    const clientId = `market_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const client: OptimizedWebSocketClient = {
      id: clientId,
      socket,
      subscriptions: new Set(),
      lastPing: Date.now(),
      authenticated: !!request.user,
      userId: request.user?.id,
      rateLimit: {
        count: 0,
        resetTime: Date.now() + this.rateLimitConfig.windowMs
      }
    }

    this.clients.set(clientId, client)

    // 自动订阅热门交易对
    const popularSymbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
    popularSymbols.forEach(symbol => {
      this.subscribeToSymbol(client, symbol)
    })

    socket.on('message', (data) => this.handleMarketDataMessage(client, data))
    socket.on('close', () => this.handleDisconnection(client))
    socket.on('error', (error) => this.handleError(client, error))
  }

  // 处理策略执行专用连接
  private handleStrategyConnection(socket: WebSocket, request: any) {
    if (!request.user) {
      socket.close(1008, 'Authentication required for strategy connection')
      return
    }

    const clientId = `strategy_${request.user.id}_${Date.now()}`
    const client: OptimizedWebSocketClient = {
      id: clientId,
      socket,
      subscriptions: new Set(),
      lastPing: Date.now(),
      authenticated: true,
      userId: request.user.id,
      rateLimit: {
        count: 0,
        resetTime: Date.now() + this.rateLimitConfig.windowMs
      }
    }

    this.clients.set(clientId, client)

    socket.on('message', (data) => this.handleStrategyMessage(client, data))
    socket.on('close', () => this.handleDisconnection(client))
    socket.on('error', (error) => this.handleError(client, error))
  }

  // 处理消息 - 带速率限制
  private async handleMessage(client: OptimizedWebSocketClient, data: any) {
    // 速率限制检查
    if (!this.checkRateLimit(client)) {
      this.sendMessage(client.socket, {
        type: 'error',
        message: 'Rate limit exceeded',
        timestamp: Date.now()
      })
      return
    }

    try {
      const message = JSON.parse(data.toString())

      switch (message.type) {
        case 'ping':
          this.handlePing(client)
          break
        case 'subscribe':
          await this.handleSubscribe(client, message.payload)
          break
        case 'unsubscribe':
          await this.handleUnsubscribe(client, message.payload)
          break
        case 'get_market_data':
          await this.handleGetMarketData(client, message.payload)
          break
        default:
          this.sendMessage(client.socket, {
            type: 'error',
            message: `Unknown message type: ${message.type}`,
            timestamp: Date.now()
          })
      }
    } catch (error) {
      this.fastify.log.error('Error processing WebSocket message:', error)
      this.sendMessage(client.socket, {
        type: 'error',
        message: 'Invalid message format',
        timestamp: Date.now()
      })
    }
  }

  // 速率限制检查
  private checkRateLimit(client: OptimizedWebSocketClient): boolean {
    const now = Date.now()

    if (now > client.rateLimit.resetTime) {
      client.rateLimit.count = 0
      client.rateLimit.resetTime = now + this.rateLimitConfig.windowMs
    }

    client.rateLimit.count++
    return client.rateLimit.count <= this.rateLimitConfig.maxRequests
  }

  // 处理心跳
  private handlePing(client: OptimizedWebSocketClient) {
    client.lastPing = Date.now()
    this.sendMessage(client.socket, {
      type: 'pong',
      timestamp: Date.now()
    })
  }

  // 订阅符号
  private subscribeToSymbol(client: OptimizedWebSocketClient, symbol: string) {
    client.subscriptions.add(symbol)

    if (!this.subscriptions.has(symbol)) {
      this.subscriptions.set(symbol, new Set())
    }
    this.subscriptions.get(symbol)!.add(client.id)

    // 启动该符号的数据流（如果还没有启动）
    this.startSymbolDataStream(symbol)
  }

  // 启动符号数据流
  private async startSymbolDataStream(symbol: string) {
    // 这里集成市场数据服务
    try {
      await this.fastify.marketDataService.subscribeToSymbol(symbol, (data) => {
        this.broadcastToSubscribers(symbol, {
          type: 'price_update',
          symbol,
          data,
          timestamp: Date.now()
        })
      })
    } catch (error) {
      this.fastify.log.error(`Failed to start data stream for ${symbol}:`, error)
    }
  }

  // 广播给订阅者
  private broadcastToSubscribers(symbol: string, message: any) {
    const subscribers = this.subscriptions.get(symbol)
    if (!subscribers) return

    subscribers.forEach(clientId => {
      const client = this.clients.get(clientId)
      if (client && client.socket.readyState === WebSocket.OPEN) {
        this.sendMessage(client.socket, message)
      }
    })
  }

  // 发送消息
  private sendMessage(socket: WebSocket, message: any) {
    if (socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify(message))
    }
  }

  // 设置心跳检测
  private setupHeartbeat(client: OptimizedWebSocketClient) {
    const heartbeatInterval = setInterval(() => {
      if (Date.now() - client.lastPing > 30000) { // 30秒超时
        this.fastify.log.info(`Client ${client.id} heartbeat timeout`)
        client.socket.close(1000, 'Heartbeat timeout')
        clearInterval(heartbeatInterval)
      } else {
        this.sendMessage(client.socket, {
          type: 'ping',
          timestamp: Date.now()
        })
      }
    }, 15000) // 每15秒发送心跳

    client.socket.on('close', () => clearInterval(heartbeatInterval))
  }

  // 处理断开连接
  private handleDisconnection(client: OptimizedWebSocketClient) {
    this.fastify.log.info(`WebSocket client disconnected: ${client.id}`)

    // 清理订阅
    client.subscriptions.forEach(symbol => {
      const subscribers = this.subscriptions.get(symbol)
      if (subscribers) {
        subscribers.delete(client.id)
        if (subscribers.size === 0) {
          this.subscriptions.delete(symbol)
          // 停止该符号的数据流
          this.fastify.marketDataService.unsubscribeFromSymbol(symbol)
        }
      }
    })

    this.clients.delete(client.id)
  }

  // 处理错误
  private handleError(client: OptimizedWebSocketClient, error: Error) {
    this.fastify.log.error(`WebSocket error for client ${client.id}:`, error)
  }

  // 处理订阅请求
  private async handleSubscribe(client: OptimizedWebSocketClient, payload: any) {
    const { symbols } = payload
    if (!Array.isArray(symbols)) {
      this.sendMessage(client.socket, {
        type: 'error',
        message: 'Invalid symbols format',
        timestamp: Date.now()
      })
      return
    }

    symbols.forEach(symbol => {
      this.subscribeToSymbol(client, symbol)
    })

    this.sendMessage(client.socket, {
      type: 'subscription_confirmed',
      symbols,
      timestamp: Date.now()
    })
  }

  // 处理取消订阅请求
  private async handleUnsubscribe(client: OptimizedWebSocketClient, payload: any) {
    const { symbols } = payload
    if (!Array.isArray(symbols)) {
      this.sendMessage(client.socket, {
        type: 'error',
        message: 'Invalid symbols format',
        timestamp: Date.now()
      })
      return
    }

    symbols.forEach(symbol => {
      client.subscriptions.delete(symbol)
      const subscribers = this.subscriptions.get(symbol)
      if (subscribers) {
        subscribers.delete(client.id)
        if (subscribers.size === 0) {
          this.subscriptions.delete(symbol)
          this.fastify.marketDataService.unsubscribeFromSymbol(symbol)
        }
      }
    })

    this.sendMessage(client.socket, {
      type: 'unsubscription_confirmed',
      symbols,
      timestamp: Date.now()
    })
  }

  // 处理获取市场数据请求
  private async handleGetMarketData(client: OptimizedWebSocketClient, payload: any) {
    const { symbol } = payload
    try {
      const marketData = await this.fastify.marketDataService.getMarketData(symbol)
      this.sendMessage(client.socket, {
        type: 'market_data_response',
        symbol,
        data: marketData,
        timestamp: Date.now()
      })
    } catch (error) {
      this.sendMessage(client.socket, {
        type: 'error',
        message: `Failed to get market data for ${symbol}`,
        timestamp: Date.now()
      })
    }
  }

  // 处理市场数据消息
  private async handleMarketDataMessage(client: OptimizedWebSocketClient, data: any) {
    if (!this.checkRateLimit(client)) {
      this.sendMessage(client.socket, {
        type: 'error',
        message: 'Rate limit exceeded',
        timestamp: Date.now()
      })
      return
    }

    try {
      const message = JSON.parse(data.toString())

      switch (message.type) {
        case 'subscribe_symbols':
          await this.handleSubscribe(client, message.payload)
          break
        case 'unsubscribe_symbols':
          await this.handleUnsubscribe(client, message.payload)
          break
        case 'get_orderbook':
          await this.handleGetOrderbook(client, message.payload)
          break
        default:
          this.sendMessage(client.socket, {
            type: 'error',
            message: `Unknown message type: ${message.type}`,
            timestamp: Date.now()
          })
      }
    } catch (error) {
      this.fastify.log.error('Error processing market data message:', error)
    }
  }

  // 处理策略消息
  private async handleStrategyMessage(client: OptimizedWebSocketClient, data: any) {
    if (!this.checkRateLimit(client)) {
      this.sendMessage(client.socket, {
        type: 'error',
        message: 'Rate limit exceeded',
        timestamp: Date.now()
      })
      return
    }

    try {
      const message = JSON.parse(data.toString())

      switch (message.type) {
        case 'start_strategy':
          await this.handleStartStrategy(client, message.payload)
          break
        case 'stop_strategy':
          await this.handleStopStrategy(client, message.payload)
          break
        case 'get_strategy_status':
          await this.handleGetStrategyStatus(client, message.payload)
          break
        default:
          this.sendMessage(client.socket, {
            type: 'error',
            message: `Unknown message type: ${message.type}`,
            timestamp: Date.now()
          })
      }
    } catch (error) {
      this.fastify.log.error('Error processing strategy message:', error)
    }
  }

  // 处理获取订单簿请求
  private async handleGetOrderbook(client: OptimizedWebSocketClient, payload: any) {
    const { symbol, depth = 20 } = payload
    try {
      const orderbook = await this.fastify.marketDataService.getOrderbook(symbol, depth)
      this.sendMessage(client.socket, {
        type: 'orderbook_response',
        symbol,
        data: orderbook,
        timestamp: Date.now()
      })
    } catch (error) {
      this.sendMessage(client.socket, {
        type: 'error',
        message: `Failed to get orderbook for ${symbol}`,
        timestamp: Date.now()
      })
    }
  }

  // 处理启动策略请求
  private async handleStartStrategy(client: OptimizedWebSocketClient, payload: any) {
    const { strategyId } = payload
    try {
      await this.fastify.strategyService.startStrategy(strategyId, client.userId!)
      this.sendMessage(client.socket, {
        type: 'strategy_started',
        strategyId,
        timestamp: Date.now()
      })
    } catch (error) {
      this.sendMessage(client.socket, {
        type: 'error',
        message: `Failed to start strategy ${strategyId}`,
        timestamp: Date.now()
      })
    }
  }

  // 处理停止策略请求
  private async handleStopStrategy(client: OptimizedWebSocketClient, payload: any) {
    const { strategyId } = payload
    try {
      await this.fastify.strategyService.stopStrategy(strategyId, client.userId!)
      this.sendMessage(client.socket, {
        type: 'strategy_stopped',
        strategyId,
        timestamp: Date.now()
      })
    } catch (error) {
      this.sendMessage(client.socket, {
        type: 'error',
        message: `Failed to stop strategy ${strategyId}`,
        timestamp: Date.now()
      })
    }
  }

  // 处理获取策略状态请求
  private async handleGetStrategyStatus(client: OptimizedWebSocketClient, payload: any) {
    const { strategyId } = payload
    try {
      const status = await this.fastify.strategyService.getStrategyStatus(strategyId, client.userId!)
      this.sendMessage(client.socket, {
        type: 'strategy_status_response',
        strategyId,
        data: status,
        timestamp: Date.now()
      })
    } catch (error) {
      this.sendMessage(client.socket, {
        type: 'error',
        message: `Failed to get strategy status for ${strategyId}`,
        timestamp: Date.now()
      })
    }
  }

  // 获取统计信息
  getStats() {
    return {
      totalClients: this.clients.size,
      authenticatedClients: Array.from(this.clients.values()).filter(c => c.authenticated).length,
      totalSubscriptions: this.subscriptions.size,
      activeSymbols: Array.from(this.subscriptions.keys()),
      clientsByType: {
        general: Array.from(this.clients.keys()).filter(id => id.startsWith('client_')).length,
        marketData: Array.from(this.clients.keys()).filter(id => id.startsWith('market_')).length,
        strategy: Array.from(this.clients.keys()).filter(id => id.startsWith('strategy_')).length
      }
    }
  }

  // 清理所有连接
  async cleanup() {
    this.clients.forEach(client => {
      client.socket.close(1001, 'Server shutting down')
    })
    this.clients.clear()
    this.subscriptions.clear()
  }
}
